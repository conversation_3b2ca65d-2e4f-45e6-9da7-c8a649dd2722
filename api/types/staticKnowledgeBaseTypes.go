package types

import "time"

type DrugKnowLedgeRequest struct {
	SpeciID string `json:"speci_id"`
}

type DrugKnowLedgeResponse struct {
	Name    string                  `json:"name"`
	Content string                  `json:"content"`
	Link    string                  `json:"link"`
	Items   []DrugSpecificationItem `json:"items"`
}

type DrugSpecificationItem struct {
	Type          int16  `json:"type"`
	Name          string `json:"name"`
	Specification string `json:"content"`
	Jsonb         []byte `json:"jsonb"`
}

type PrescriptionKnowLedgeRequest struct {
	TcmId string `json:"tcm_id"`
}

type PrescriptionKnowLedgeResponse struct {
	Name                   string                `json:"name"`
	OriginOfPrescription   string                `json:"origin_of_prescription"`
	Formulas               string                `json:"formulas"`
	OriginalPrescription   string                `json:"original_prescription"`
	DosageForm             string                `json:"dosage_form"`
	PreparationMethod      string                `json:"preparation_method"`
	Route                  string                `json:"route"`
	Mechanism              string                `json:"mechanism"`
	Indications            string                `json:"indications"`
	EfficacyClassification string                `json:"efficacy_classification"`
	Effect                 string                `json:"effect"`
	Dialectical            string                `json:"dialectical"`
	AdditionAndSubtraction string                `json:"addition_and_subtraction"`
	SquareSolution         string                `json:"square_solution"`
	ClinicalApplication    string                `json:"clinical_application"`
	MedicationPrecautions  string                `json:"medication_precautions"`
	DecoctingMethod        string                `json:"decocting_method"`
	GroupOID               int64                 `json:"group_oid"`
	TCMID                  int64                 `json:"tcm_id"`
	CreateUser             int64                 `json:"create_user"`
	Status                 string                `json:"status"`
	PreType                string                `json:"pre_type"`
	ByStr                  string                `json:"by_str"`
	CreatedAt              time.Time             `json:"created_at"`
	FunClass1              string                `json:"fun_class1"`
	FunClass2              string                `json:"fun_class2"`
	ItemList               []DmpMdmCMedicineItem `json:"item_list"`
}

type DmpMdmCMedicineItem struct {
	ItemID          int64  `json:"item_id"`
	ItemOID         int64  `json:"item_oid"`
	TCMID           int64  `json:"tcmid"`
	CName           string `json:"c_name"`
	Qly             string `json:"qly"`
	Unit            string `json:"unit"`
	Description     string `json:"description"`
	DescofDecoction string `json:"descofDecoction"`
	Compatibility   string `json:"compatibility"`
	Status          string `json:"status"`
	YpCode          string `json:"ypCode"`
}

type KnbDocKnowLedgeRequest struct {
	ID string `json:"id"`
}

type KnbDocKnowLedgeResponse struct {
	Description string                         `json:"description"`
	Contents    string                         `json:"contents"`
	TypeName    string                         `json:"type_name"`
	List        []DasKnbDocTreeResponseContent `json:"list"`
}

type DasKnbDocTreeResponseContent struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	Contents   string `json:"contents"`
	DocContent string `json:"docContent"`
}

type SynthesisSearchRequest struct {
	Keyword string `json:"keyword"`
}

type SynthesisSearchResponse struct {
	DasKnbDocList              []SynthesisSearchResponseContent `json:"das_knb_doc_list"`              //病种
	DrugSpecificationList      []SynthesisSearchResponseContent `json:"drug_specification_list"`       //药品
	DmpMdmCMedicineList        []SynthesisSearchResponseContent `json:"dmp_mdm_c_medicine_list"`       //方剂
	DamInformationResourceList []SynthesisSearchResponseContent `json:"dam_information_resource_list"` //检验检查
}

type SynthesisSearchResponseContent struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type LaboratoryTestExaminationRequest struct {
	ResourceID string `json:"resource_id"`
}
type LaboratoryTestExaminationResponse struct {
	ResourceName string `json:"name"`
	Contents     string `json:"contents"`
}

type LaboratoryTestExaminationThirdRequest struct {
	BusinessCode string `json:"business_code"`
	HospitalID   string `json:"hospital_id"`
}

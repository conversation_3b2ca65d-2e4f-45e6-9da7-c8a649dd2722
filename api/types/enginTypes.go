package types

import (
	"kmbservice/dsl"
	"kmbservice/dsl/doctororders"
	"kmbservice/dsl/executionrecord"
	"kmbservice/dsl/medicalhistory"
	"kmbservice/dsl/report"
	"time"
)

type RuleGengineRequest struct {
	Tranno            string     `json:"tranno"`
	Msgid             string     `json:"msgid"`
	Hosno             string     `json:"hosno"`
	Sendapp           string     `json:"sendapp"`
	RecApp            string     `json:"rec_app"`
	Cainfo            string     `json:"cainfo"`
	Opter             string     `json:"opter"`
	TranTime          time.Time  `json:"tran_time"`
	Infver            string     `json:"infver"`
	EncType           string     `json:"enc_type"`
	InputData         *InputData `json:"input_data"`
	RegisterID        string     `json:"register_id"`
	SystemCodeMapping bool       `json:"system_code_mapping"` //是否映射
	CorpID            string     `json:"corp_id"`
}

type InputData struct {
	Rule                               *Rule                                             `json:"rule,omitempty"`
	Patient                            *dsl.Patient                                      `json:"patient,omitempty"`
	WesternMedicineDoctorOrdersList    []*doctororders.WesternMedicineDoctorOrders       `json:"western_medicine_doctor_orders_list"`
	ExaminationDoctorOrdersList        []*doctororders.ExaminationOrder                  `json:"examination_doctor_orders_list"`
	LaboratoryTestExaminationOrderList []*doctororders.LabTestExamDoctorOrder            `json:"laboratory_test_examination_order_list"`
	SurgicalDoctorOrdersList           []*doctororders.SurgicalDoctorOrder               `json:"surgical_doctor_orders_list"`
	WesternMedicineExecutionRecordList []*executionrecord.WesternMedicineExecutionRecord `json:"western_medicine_execution_record_list"`
	ExaminationExecutionRecordList     []*executionrecord.ExaminationExecutionRecord     `json:"examination_execution_record_list"`
	LabExaminationExecutionRecordList  []*executionrecord.LabExaminationExecutionRecord  `json:"lab_examination_execution_record_list"`
	SurgicalExecutionRecordList        []*executionrecord.SurgicalExecutionRecord        `json:"surgical_execution_record_list"`
	DiagnosisList                      []*dsl.Diagnosis                                  `json:"diagnosis_list,omitempty"`
	AllergicHistoryList                []*medicalhistory.AllergicHistory                 `json:"allergic_history_list,omitempty"`
	LaboratoryExaminationReport        []*report.LaboratoryExaminationReport             `json:"laboratory_examination_report_list"`
	Emr                                *dsl.OutpEmr                                      `json:"emr,omitempty"`
}

type Rule struct {
	RuleIDList []string `json:"rule_id_list"`
}

type PatientRequest struct {
	ID                       int    `json:"id"`                          // 病人ID
	PatientID                string `json:"patient_id"`                  //病人外部HISID
	IsPregnant               bool   `json:"is_pregnant"`                 // 是否为孕妇
	PlaceOfParticipationCode string `json:"place_of_participation_code"` //参保地编码
	InsuranceType            string `json:"insurance_type"`              //参保类型
	PatientName              string `json:"patient_name"`
	PatientGender            string `json:"patient_gender"`
	PatientBirthday          string `json:"patient_birthday"` //出生日期
	MaritalStatus            string `json:"marital_status"`
	Msg                      []Message
}

// 疾病对象
type DiseaseRequest struct {
	EncountId          string               `json:"encountId"`          //就诊ID
	PatId              string               `json:"patId"`              //患者ID
	Symptoms           []Symptom            `json:"symptoms"`           // 症状列表
	DiagnosisMethods   []DiagnosisMethod    `json:"diagnosisMethods"`   // 诊断方法列表
	Diagnosis          []string             `json:"diagnosis_list"`     // 诊断编码列表
	TreatmentMethods   []TreatmentMethod    `json:"treatmentMethods"`   // 治疗方法列表
	PreventiveMeasures []PreventiveMeasure  `json:"preventiveMeasures"` // 预防措施列表
	DiseaseProgress    []DiseaseProgression `json:"diseaseProgress"`    // 疾病进展列表
	Complications      []Complication       `json:"complications"`      // 并发症列表
	CommonCauses       []CommonCause        `json:"commonCauses"`       // 常见原因列表
	DiseaseCode        string               `json:"diseaseCode"`        //病种编码
	DiagnosisCode      string               `json:"diagnosisCode"`      //诊断编码
	DiseaseID          string               `json:"disease_id"`         // 疾病ID
	DiseaseName        string               `json:"disease_name"`       // 疾病名称
}

type Message struct {
	MessageID   string `json:"message_id"` //消息ID
	MessageDesc string `json:"message"`    //消息内容
	MessageType string `json:"type"`       //消息类型
}

type DiagnosisMethod struct {
	ID          int    `json:"id"`          // 诊断方法ID
	Name        string `json:"name"`        // 诊断方法名称
	Description string `json:"description"` // 诊断方法描述
}

type TreatmentMethod struct {
	ID          int    `json:"id"`          // 治疗方法ID
	Name        string `json:"name"`        // 治疗方法名称
	Description string `json:"description"` // 治疗方法描述
}

type PreventiveMeasure struct {
	ID          int    `json:"id"`          // 预防措施ID
	Name        string `json:"name"`        // 预防措施名称
	Description string `json:"description"` // 预防措施描述
}

type DiseaseProgression struct {
	ID          int    `json:"id"`          // 疾病进展ID
	Stage       string `json:"stage"`       // 疾病阶段
	Description string `json:"description"` // 阶段描述
}

type Complication struct {
	ID          int    `json:"id"`          // 并发症ID
	Name        string `json:"name"`        // 并发症名称
	Description string `json:"description"` // 并发症描述
}

type CommonCause struct {
	ID          int    `json:"id"`          // 常见原因ID
	Name        string `json:"name"`        // 常见原因名称
	Description string `json:"description"` // 常见原因描述
}

type Symptom struct {
	Name       string   `json:"name"`       // 名称
	Nature     string   `json:"nature"`     // 性质
	Severity   string   `json:"severity"`   // 程度
	Duration   string   `json:"duration"`   // 持续时间
	Frequency  string   `json:"frequency"`  // 频率
	Associated []string `json:"associated"` // 伴随症状
	Impact     string   `json:"impact"`     // 影响活动
	Triggers   []string `json:"triggers"`   // 发作触发因素
}

// 规则日志结构体
type RuleGengineResponse struct {
	Infcode      string      `json:"infcode"`       // 交易状态码
	TranRetno    string      `json:"tran_retno"`    // 接收报文码
	RecTime      string      `json:"rec_time"`      // 接收时间
	ResponseTime string      `json:"response_time"` // 响应时间
	Message      string      `json:"message"`       // 消息
	Signtype     string      `json:"signtype"`      // 签名类型
	Cainfo       string      `json:"cainfo"`        // 数字签名信息
	EncType      string      `json:"enc_type"`      // 加密类型
	OutputData   interface{} `json:"output_data"`   // 输出数据
}

// 响应结构体
type RuleEngineResponse struct {
	Code    string       `json:"exec_code"`
	Message string       `json:"exec_message"`
	Data    []OutputData `json:"exec_data"`
}

type OutputData struct {
	TipsMessageID string `json:"tips_message_id"`
	TipsMessage   string `json:"tips_message"`
	TipsType      string `json:"tips_type"`
}

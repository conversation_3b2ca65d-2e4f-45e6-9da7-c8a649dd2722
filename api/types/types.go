package types

import (
	"encoding/json"
)

type GetDomainObjectRequest struct {
	Name string `json:"name"`
}

type GetDomainObjectResponse struct {
	Rsplist []GetDomainObjectResponseContent `json:"rsplist"`
}

type GetDomainObjectResponseContent struct {
	DslID   string `json:"dsl_id"`   //领域对象id
	DslName string `json:"dsl_name"` //领域对象名称
	DslDesc string `json:"dsl_desc"` //对象描述
}

// 领域对象属性获取
type GetDomainObjectStatsRequest struct {
	DslID string `json:"dsl_id"` //领域对象id
}

type GetDomainObjectStatsResponse struct {
	ObjectFuncList []ObjectStats `json:"object_func_list"` //方法列表
}

type ObjectStats struct {
	Value        string                                `json:"value"`
	ObjectStatss []GetDomainObjectResponseStatsContent `json:"children"`
}

type GetDomainObjectResponseStatsContent struct {
	PropertyID   string `json:"property_id"`
	PropertyName string `json:"property_name"`
	PropertyType string `json:"property_type"` //类别1:属性；2：函数
	PropertyDesc string `json:"property_desc"`
	ParaDef      string `json:"para_def"`
	OntoSoltID   string `json:"onto_solt_id"`
}

// 知识本体获取
type GetNoumenonRequest struct {
	GlobalID string `json:"global_id"` //知识本体id
}

type GetNoumenonResponse struct {
	NoumenonName string `json:"noumenon_name"` //本体名称
	NoumenonKey  string `json:"noumenon_key"`
}

// 知识本体属性获取
type GetNoumenonSlotRequest struct {
	GlobalID string `json:"global_id"` //知识本体属性id
}

type GetNoumenonSlotResponse struct {
	SlotName         string `json:"slot_name"`          //属性名
	CodingSystemSoid string `json:"coding_system_soid"` //编码系统id
	MaxNumber        string `json:"max_number"`
	MinNumber        string `json:"min_number"`
	DefaultNumber    string `json:"default_number"`
	DataType         string `json:"data_type"`
	Unit             string `json:"unit"`
	Basic            string `json:"basic"`
	SlotOrder        string `json:"slot_order"`
	SlotKey          string `json:"slot_key"`
	SlotCode         string `json:"slot_code"`
	Operator         string `json:"operator"`
	Keyrange         string `json:"keyrange"`
}

// 规则创建请求体
type RuleCreateRequest struct {
	RuleName            string          `json:"rule_name"`             // 规则名称
	LogicalOperation    string          `json:"logical_operation"`     //逻辑运算符
	RuleBody            RuleContent     `json:"rule_body"`             // 规则条件
	RuleResult          []ResultContent `json:"rule_result"`           // 结果内容
	RuleID              string          `json:"rule_id"`               //规则id
	RuleDesc            string          `json:"rule_desc"`             //规则描述
	RuleParticulars     []string        `json:"rule_particulars"`      //规则详情
	RuleClassficationID []int64         `json:"rule_classfication_id"` //规则分类id
	Status              string          `json:"status"`
}

type RuleContent struct {
	LogicalOperator string      `json:"logical_operation"` // "AND" 或 "OR"
	Content         []Metadatas `json:"content"`
}

type Metadatas struct {
	LogicalOperator string   `json:"logical_operation"` // "AND" 或 "OR"
	Metadata        []Detail `json:"metadata"`
}

type Detail struct {
	Noumenon           string `json:"noumenon"`            // 本体
	Key                string `json:"key"`                 // 左侧键
	ComparisonOperator string `json:"comparison_operator"` // 运算操作符
	Value              string `json:"value"`               // 值
}

// 定义结果内容的结构体
type ResultContent struct {
	Noumenon string `json:"noumenon"` //本体
	Method   string `json:"method"`   //方法id
	Message  string `json:"msg"`      // 消息类型，例如 "warning"
	Code     string `json:"code"`     // 代码，例如 "GZ0101005000"
	Content  string `json:"content"`  // 消息内容，例如 "佐匹克隆片肝功能不全者慎用"
}

type RuleCreateResponse struct {
	RuleID      string `json:"rule_id"`      //规则id
	RuleDesc    string `json:"rule_desc"`    //规则描述
	RuleContent string `json:"rule_content"` //规则内容
}

// 规则属性与方法维护请求体
type RuleMethodMaintainRequest struct {
	TenantID     string          `json:"tenant_id"`     // 租户号
	PropertyID   string          `json:"property_id"`   // 属性ID
	DSLID        string          `json:"dsl_id"`        // 领域对象ID
	PropertyName string          `json:"property_name"` // 属性名称
	Status       string          `json:"status"`        // 状态
	PropertyDesc string          `json:"property_desc"` // 属性描述
	ParaDef      json.RawMessage `json:"para_def"`      // 参数定义
	OntoSlotID   string          `json:"onto_slot_id"`  // 本体SLOT属性ID
	PropertyType string          `json:"property_type"` // 类别
}

type RuleMethodMaintainResponse struct {
}

// 规则组合
type RuleSplicingRequest struct {
	RuleGroupName   string   `json:"rule_name"` //规则组名称
	RuleCode        string   `json:"rule_code"`
	UseScene        string   `json:"use_scene"`
	RuleCategoryID  string   `json:"rule_category_id"`
	RuleTreeID      string   `json:"rule_tree_id"`
	RuleDescription string   `json:"rule_description"` //规则组描述
	RuleList        []string `json:"rule_list"`        //规则id列表
	Status          string   `json:"status"`           //状态
}

type RuleSplicingResponse struct {
	ResultGroup string `json:"result_group"` //生成的规则组
}

// 规则映射表请求体
type GetRuleContentMappingRequest struct {
	RuleGroupId string `json:"rule_group_id"`
}

type GetRuleContentMappingResponse struct {
	RuleDetailList []RuleDetail `json:"rule_detail_list"`
}

type RuleDetail struct {
	RuleGroupId    string `json:"rule_group_id"`
	RuleGroupName  string `json:"rule_group_name"`
	RuleDetailId   string `json:"rule_detail_id"`
	RuleDetailName string `json:"rule_detail_name"`
}

// 映射关系修改请求体
type EditRuleContentMappingRequest struct {
	RuleGroupId    string   `json:"rule_group_id"`
	RuleDetailList []string `json:"rule_detail_list"`
}
type EditRuleContentMappingResponse struct {
	ResultGroup string `json:"result_group"`
}

// 查询规则详细信息
type GetDetailListRequest struct {
	RuleName         string   `json:"rule_name"` // 规则名称
	RuleObj          []string `json:"rule_obj"`  // 规则对象
	DetailList       []string `json:"detail_list"`
	StartDate        string   `json:"start_date"` // 开始时间
	EndDate          string   `json:"end_date"`   // 结束时间
	Status           string   `json:"status"`
	PublishingStatus string   `json:"publishing_status,optional"` // 发布状态
	IsUse            string   `json:"is_use,optional"`            //是否在用
	Limit            int      `json:"limit,optional"`             //每页限制
	Page             int      `json:"page,optional"`              //第几页
}

type GetDetailListResponse struct {
	ResultDetailList []Rule_detail_content `json:"result_detail_list"` // 规则数据列表
	Count            int                   `json:"count"`
	Limit            int                   `json:"limit"`
	Page             int                   `json:"page"`
	Number           int                   `json:"number"`
}

type Rule_detail_content struct {
	DetailID          string `json:"detail_id"`
	RuleName          string `json:"rule_name"` // 规则名称
	RuleDesc          string `json:"rule_desc"` // 规则描述
	Updated           string `json:"updated"`   // 更新日期
	Version           string `json:"version"`   // 版本
	Status            string `json:"status"`    // 状态
	DetailVersionID   string `json:"detail_version_id"`
	Using             int64  `json:"using"`             //使用状态
	PublishingStatus  string `json:"publishing_status"` // 发布状态
	RuleDetailContent string `json:"rule_detail_content"`
	ClassficationID   string `json:"classfication_id"`
}

// 提示信息获取
type GetPromptMessageRequeat struct {
	CodeSystemId string `json:"codesystemid"`
}

type GetPromptMessageResponse struct {
	GetPromptMessageResponseContentList []GetPromptMessageResponseContent `json:"get_prompt_message_response_content_list"`
}

type GetPromptMessageResponseContent struct {
	Soid     string `json:"soid"`
	Code     string `json:"code"`
	Content  string `json:"content"`
	CMeaning string `json:"c_meaning"`
}

// 查看规则详情
type GetParticularsMessageRequest struct {
	DetailVersionId string `json:"detail_version_id"`
}

type GetParticularsMessageResponse struct {
	Version          string `json:"version"`
	Status           string `json:"status"`
	RuleName         string `json:"rule_name"`
	RuleDescription  string `json:"rule_description"`
	Particulars      string `json:"particulars"`
	Prompt           string `json:"prompt"`
	WarningType      string `json:"warning_type"`
	ReqDataStruct    string `json:"req_data_struct"`   //规则的数据结构
	PublishingStatus string `json:"publishing_status"` //发布状态
}

// 规则创建v2请求体
type RuleCreateNewRequest struct {
	RuleName            string           `json:"rule_name"`             // 规则名称
	LogicalOperation    string           `json:"operator"`              //逻辑运算符
	RuleBody            Condition        `json:"rule_body"`             // 规则条件
	RuleResult          ResultContentNew `json:"rule_result"`           // 结果内容
	RuleID              string           `json:"rule_id"`               //规则id
	RuleDesc            string           `json:"rule_desc"`             //规则描述
	RuleParticulars     []string         `json:"rule_particulars"`      //规则详情
	RuleClassficationID []string         `json:"rule_classfication_id"` //规则分类id
	Status              string           `json:"status"`                //规则状态（已去掉）
}

type Condition struct {
	Operator   string      `json:"operator"`
	Detail     []Condition `json:"detail"` //metadata
	Object     string      `json:"object"`
	Property   string      `json:"property"`
	Comparison string      `json:"comparison"`
	Value      interface{} `json:"value"`
}

type ResultContentNew struct {
	Noumenon string `json:"noumenon"` //本体
	Method   string `json:"method"`   //方法id
	Message  string `json:"msg"`      // 消息类型，例如 "warning"
	Code     string `json:"code"`     // 代码，例如 "GZ0101005000"
	Content  string `json:"content"`  // 消息内容，例如 "佐匹克隆片肝功能不全者慎用"
}

type RuleCreateNewResponse struct {
	RuleID          string `json:"rule_id"` //规则id
	DetailVersionId string `json:"detail_version_id"`
	RuleDesc        string `json:"rule_desc"`    //规则描述
	RuleContent     string `json:"rule_content"` //规则内容
	RuleCname       string `json:"rule_cname"`   //规则显式内容
}

// 规则详情状态修改
type RuleDeStatusChangeRequest struct {
	RuleID   string `json:"rule_version_id"`
	Status   string `json:"status"`
	RuleType string `json:"rule_type"`
}

// 规则详情编辑
type RuleEditRequest struct {
	DetailID            string           `json:"detail_id"`             //规则详细id
	DetailVersionId     string           `json:"detail_version_id"`     //规则数据版本id
	RuleName            string           `json:"rule_name"`             // 规则名称
	LogicalOperation    string           `json:"operator"`              //逻辑运算符
	RuleBody            Condition        `json:"rule_body"`             // 规则条件
	RuleResult          ResultContentNew `json:"rule_result"`           // 结果内容
	RuleID              string           `json:"rule_id"`               //规则id
	RuleDesc            string           `json:"rule_desc"`             //规则描述
	RuleParticulars     []string         `json:"rule_particulars"`      //规则详情
	RuleClassficationID []string         `json:"rule_classfication_id"` //规则分类id
}

// 规则组列表获取
type GetRuleGroupListRequest struct {
	RuleName string `json:"rule_name,optional"`
	Status   string `json:"status,optional"`
	Limit    int    `json:"limit,optional"` //每页限制
	Page     int    `json:"page,optional"`  //第几页
}

type GetRuleGroupListResponse struct {
	GroupList []RuleGroupListContent `json:"group_list"`
	Count     int                    `json:"count"`
	Limit     int                    `json:"limit"`
	Page      int                    `json:"page"`
	Number    int                    `json:"number"`
}

type RuleGroupListContent struct {
	RuleCode      string `json:"rule_code"`
	RuleID        string `json:"rule_id"`
	RuleVersionID string `json:"rule_version_id"`
	RuleName      string `json:"rule_name"`
	RuleDesc      string `json:"rule_desc"`
	Status        string `json:"status"`
}

// 类别信息
type RuleGroupSortInfoRequest struct {
	SortType string `json:"sort_type"`
}
type RuleGroupSortInfoResponse struct {
	SortList []RuleGroupSortContetn `json:"sort_list"`
}

type RuleGroupSortContetn struct {
	Name string `json:"name"`
	ID   string `json:"id"`
}

type RuleGroupEditRequest struct {
	RuleGroupID     string   `json:"rule_id"`   //规则组id
	RuleGroupName   string   `json:"rule_name"` //规则组名称
	RuleCode        string   `json:"rule_code"`
	UseScene        string   `json:"use_scene"`
	RuleCategoryID  string   `json:"rule_category_id"`
	RuleTreeID      string   `json:"rule_tree_id"`
	RuleDescription string   `json:"rule_description"` //规则组描述
	RuleList        []string `json:"rule_list"`        //规则id列表（不修改规则内容就不传）
	Status          string   `json:"status"`           //状态
}

type RuleGroupEditResponse struct {
	ResultGroup string `json:"result_group"` //生成的规则组
}

type RuleGroupDetailInfoRequest struct {
	RuleGroupID string `json:"rule_group_id"`
}

type RuleGroupDetailInfoResponse struct {
	ResultGroupID        string                `json:"result_group_id"`
	RuleCode             string                `json:"rule_code"`
	Status               string                `json:"status"`
	Version              string                `json:"version"`
	RuleGroupName        string                `json:"rule_name"`
	RuleGroupCategory    string                `json:"rule_tree_id"`
	RuleCategoryId       string                `json:"rule_category_id"`
	UseScene             string                `json:"use_scene"`
	RuleGroupDescription string                `json:"rule_description"`
	RuleGroupContent     string                `json:"rule_content"`
	RuleDetailList       []Rule_detail_content `json:"rule_detail_list"`
}

type RuleGroupDeleteRequest struct {
	RuleGroupVersionID string `json:"rule_group_version_id"`
}

type RuleGroupSelectableRequest struct {
	RuleID         string   `json:"rule_id"`
	RuleVersion    string   `json:"rule_version"`
	DetailIDList   []string `json:"detail_id_list"`
	RuleDetailObj  []string `json:"rule_detail_obj"`
	RuleDetailName string   `json:"rule_detail_name"`
	Limit          int      `json:"limit,optional"` //每页限制
	Page           int      `json:"page,optional"`  //第几页
}

type RuleGroupSelectableResponse struct {
	SelectableList []RuleDetailContent `json:"selectable_list"`
	SelectedList   []RuleDetailContent `json:"selected_list"`
	Count          int                 `json:"count"`
	Limit          int                 `json:"limit"`
	Page           int                 `json:"page"`
	Number         int                 `json:"number"`
}

type RuleDetailContent struct {
	DetailID        string `json:"detail_id"`
	DetailVersionID string `json:"detail_version_id"`
	RuleName        string `json:"rule_name"`
	RuleDescription string `json:"rule_desc"`
	Version         string `json:"version"`
	Status          string `json:"status"`
}

type RulesundeleteRequest struct {
	RuleID       string `json:"rule_id"`
	RuleVersion  string `json:"rule_version"`
	RuleDetailid string `json:"rule_detailid"`
}

type RuleGroupSelectableNewRequest struct {
	DetailIdList []string `json:"detail_id_list"`
}
type RuleGroupSelectableNewResponse struct {
	ResultDetailList []Rule_detail_content `json:"result_detail_list"` // 规则数据列表
}

// 状态查询
type RuleDetailStatusGetRequest struct {
	Status string `json:"status"`
	Limit  int    `json:"limit,optional"` //每页限制
	Page   int    `json:"page,optional"`  //第几页
}

type RuleDetailCopyRequest struct {
	DetailVersionID string `json:"detail_version_id"`
}

type RuleDetailVersionChangerequest struct {
	DetailVersionId string `json:"detail_version_id"`
}

type RuleDetailDeleteRequest struct {
	DetailVersionId string `json:"detail_version_id"`
}

type DrugKnowLedgeThirdRequest struct {
	Code       string `json:"code"`
	HospitalID string `json:"hospital_id"`
}

type DrugKnowLedgeThirdResponse struct {
	List []DrugKnowLedgeThirdResList `json:"list"`
}

type DrugKnowLedgeThirdResList struct {
	SpeciID int64                        `json:"speci_id"`
	Name    string                       `json:"name"`
	Content string                       `json:"content"`
	Link    string                       `json:"link"`
	Items   []DrugSpecificationThirdItem `json:"items"`
}

type DrugSpecificationThirdItem struct {
	ItemID        int64  `json:"item_id"`
	Type          int16  `json:"type"`
	Name          string `json:"name"`
	Specification string `json:"content"`
	SpeciID       int64  `json:"speci_id"`
	Jsonb         []byte `json:"jsonb"`
}

type KnbDocKnowLedgeThirdRequest struct {
	Code string `json:"code"`
}

type PrescriptionKnowLedgeThirdRequest struct {
	InstanceCode string `json:"instance_code"`
}

type RuleClosedLoopListRequest struct {
	RuleName string `json:"rule_name,optional"`
	Status   string `json:"status,optional"`
	Limit    int    `json:"limit,optional"` //每页限制
	Page     int    `json:"page,optional"`  //第几页
}

type RuleClosedLoopListResponse struct {
	GroupList []RuleClosedLoopListContent `json:"group_list"`
	Count     int                         `json:"count"`
	Limit     int                         `json:"limit"`
	Page      int                         `json:"page"`
	Number    int                         `json:"number"`
}

type RuleClosedLoopListContent struct {
	RuleCode      string `json:"rule_code"`
	RuleID        string `json:"rule_id"`
	RuleVersionID string `json:"rule_version_id"`
	RuleName      string `json:"rule_name"`
	RuleDesc      string `json:"rule_desc"`
	Status        string `json:"status"`
}

//type RuleClosedLoopDetailRequest struct {
//	RuleVersionIDList []int64 `json:"rule_version_id_list"`
//}

type RuleClosedLoopDetailRequest struct {
	RuleIDList []int64 `json:"rule_id_list"`
}

type RuleClosedLoopDetailResponse struct {
	RuleNameDesc []RuleClosedLoopDetailContent `json:"rule_name_desc"`
}

type RuleClosedLoopDetailContent struct {
	RuleID        int64  `json:"rule_id"`
	RuleVersionID int64  `json:"rule_version_id"`
	Name          string `json:"name"`
	Desc          string `json:"desc"`
}

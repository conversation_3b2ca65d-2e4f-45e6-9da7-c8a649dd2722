package rule_exec

import (
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"kmbservice/api/types"
	"kmbservice/repository/models"
	"net/http"
	"time"
)

func GetNow() string {
	ret := time.Now().Format("2006-01-02 15:04:05.000")
	return ret
}
func GenerateTransactionCode() (string, error) {
	id, err := uuid.NewUUID()
	if err != nil {
		return "", err
	}
	transactionCode := id.String()
	return transactionCode, nil
}

func InitRes() types.RuleGengineResponse {
	transactionCode, err := GenerateTransactionCode()
	if err != nil {
		fmt.Println("Error generating transaction code:", err)
		return types.RuleGengineResponse{}
	}
	responeData := types.RuleGengineResponse{
		Infcode:      http.StatusText(http.StatusOK),
		TranRetno:    transactionCode,
		RecTime:      GetNow(),
		ResponseTime: "",
		Message:      "",
		Signtype:     "",
		Cainfo:       "",
		EncType:      "",
		OutputData:   "{}",
	}
	return responeData
}

// 规则引擎调用日志记录
func CallRecord(req types.RuleGengineRequest, result string) error {
	ruleLogModel := models.RuleLog{}
	log := &models.RuleLog{
		ReqIn: " ",
	}
	requestDataJSON, err := json.Marshal(req)
	if err != nil {
		return err
	}
	log.ReqIn = string(requestDataJSON)
	log.App = req.Sendapp
	log.User = req.Opter
	log.ResOut = result
	err = ruleLogModel.CreateLog(log)
	return err
}

// 处理响应参数格式
func HandleResponse(in types.RuleGengineResponse, code string) (out types.RuleEngineResponse) {
	out.Code = code
	out.Message = in.Message
	msgInfo := convertRuleRspMsg(in.OutputData)
	if len(msgInfo) != 0 {
		for _, v := range msgInfo {
			var temp types.OutputData
			temp.TipsMessageID = v.MessageID
			temp.TipsMessage = v.MessageDesc
			temp.TipsType = v.MessageType
			out.Data = append(out.Data, temp)
		}
	}
	return out
}

func convertRuleRspMsg(data interface{}) []types.Message {
	bytes, err := json.Marshal(data)
	if err != nil {
		return nil
	}
	var orders []types.Message
	if err = json.Unmarshal(bytes, &orders); err != nil {
		return nil
	}
	return orders
}

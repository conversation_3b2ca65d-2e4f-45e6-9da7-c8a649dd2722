package rule_exec

import (
	"encoding/json"
	"fmt"
	ruleExec "kmbservice/api/logic/rule-exec"
	"kmbservice/api/rpcclient/codingSystem"
	"kmbservice/api/rpcclient/codingSystem/pb"
	"kmbservice/api/tools"
	"kmbservice/api/types"
	"kmbservice/commons/enums/enginEnums"
	"kmbservice/commons/response"
	"kmbservice/dsl"
	"kmbservice/dsl/doctororders"
	"kmbservice/dsl/executionrecord"
	"kmbservice/dsl/medicalhistory"
	"log"
	"sync"

	"github.com/gin-gonic/gin"
)

/*
规则执行所有类型错误都要要返回数据结构，错误信息放在返回的data的message字段里
*/

func RuleExecHandler(c *gin.Context) {
	req, err := parseRuleExecHandlerRequest(c)
	resData := InitRes()
	var enginRsp types.RuleEngineResponse
	enginRsp.Code = enginEnums.ExecSuccess
	if err != nil {
		resData.Message = err.Error()
		resData.RecTime = GetNow()
		response.BadRequest(c, "", enginRsp)
		return
	}
	rsp, code, err := ruleExec.RuleExec(req, resData)
	enginRsp.Code = code
	if err != nil {
		response.InternalServerError(c, err.Error(), enginRsp)
		return
	}
	rspJson, err := json.Marshal(rsp)
	if err != nil {
		response.InternalServerError(c, err.Error(), enginRsp)
		return
	}
	result := string(rspJson)
	err = CallRecord(req, result)
	enginRsp = HandleResponse(rsp, code)
	surgicalDoctorOrders := req.InputData.SurgicalDoctorOrdersList
	if len(surgicalDoctorOrders) > 0 && surgicalDoctorOrders[0].SurgicalOrderitem != "" {
		enginRsp.Data[0].TipsMessage += enginEnums.SurgicalDoctorOrdersPrompt01
	}
	if err != nil {
		response.InternalServerError(c, err.Error(), enginRsp)
		return
	}
	response.Success(c, enginRsp)
}

func parseRuleExecHandlerRequest(c *gin.Context) (types.RuleGengineRequest, error) {
	var req types.RuleGengineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		return req, err
	}
	err := processInputData(req.InputData, req.SystemCodeMapping, req.CorpID)
	return req, err
}

type RuleReqParamInit interface {
	ProcessList(data *types.InputData, corpID string, mapping bool, client pb.RpcClient) error
}

type WesternMedicineStruct struct{}

func (s *WesternMedicineStruct) ProcessList(data *types.InputData, corpID string, mapping bool, client pb.RpcClient) error {
	if data.WesternMedicineDoctorOrdersList != nil && len(data.WesternMedicineDoctorOrdersList) > 0 {
		// 处理字段兼容性：将 AdminisTraionRoute 的值赋给 RouteOfAdministration
		for _, v := range data.WesternMedicineDoctorOrdersList {
			if v.AdminisTraionRoute != "" && v.RouteOfAdministration == "" {
				v.RouteOfAdministration = v.AdminisTraionRoute
			}
		}

		if mapping && client != nil {
			for _, v := range data.WesternMedicineDoctorOrdersList {
				err := tools.WesternMedicineMasterDataMapping(v, corpID)
				if err != nil {
					return err
				}
				err = tools.WesternMedicineOrderSystemCodeMapping(v, corpID, client)
				if err != nil {
					// 记录编码转换错误但继续处理，确保系统可用性
					fmt.Printf("Warning: Western medicine code mapping failed, continuing without mapping: %v\n", err)
				}
			}
		}
		store := doctororders.NewMedicineDoctorOrdersStore()
		for _, order := range data.WesternMedicineDoctorOrdersList {
			store.AddMedicalMap(order)
		}
		store.AddMedicalList(data.WesternMedicineDoctorOrdersList)
		data.WesternMedicineDoctorOrdersList[0].Store = store
	} else {
		data.WesternMedicineDoctorOrdersList = []*doctororders.WesternMedicineDoctorOrders{{}}
	}
	return nil
}

type ExaminationOrderStruct struct{}
type ExaminationOrderRecordStruct struct{}

func (s *ExaminationOrderStruct) ProcessList(data *types.InputData, corpID string, mapping bool, client pb.RpcClient) error {
	if data.ExaminationDoctorOrdersList != nil && len(data.ExaminationDoctorOrdersList) > 0 {
		if mapping {
			for _, v := range data.ExaminationDoctorOrdersList {
				err := tools.ExaminationMasterDataMapping(v, corpID)
				if err != nil {
					return err
				}
				err = tools.ExaminationOrderSystemCodeMapping(v, corpID, client)
				if err != nil {
					return err
				}
			}
		}
		store := doctororders.NewExaminationOrderStore()
		for _, order := range data.ExaminationDoctorOrdersList {
			store.AddExaminationMap(order)
		}
		store.AddExaminationList(data.ExaminationDoctorOrdersList)
		data.ExaminationDoctorOrdersList[0].Store = store
	} else {
		data.ExaminationDoctorOrdersList = []*doctororders.ExaminationOrder{{}}
	}
	return nil
}

type LabTestOrderStruct struct{}
type LabTestOrderRecordStruct struct{}

func (s *LabTestOrderStruct) ProcessList(data *types.InputData, corpID string, mapping bool, client pb.RpcClient) error {
	if data.LaboratoryTestExaminationOrderList != nil && len(data.LaboratoryTestExaminationOrderList) > 0 {
		if mapping {
			for _, v := range data.LaboratoryTestExaminationOrderList {
				err := tools.LabTestExamMaterDataMapping(v, corpID)
				if err != nil {
					return err
				}
				err = tools.LaboratoryExaminationOrderSystemCodeMapping(v, corpID, client)
				if err != nil {
					return err
				}
			}
		}
		store := doctororders.NewLabTestExamDoctorOrderStore()
		for _, order := range data.LaboratoryTestExaminationOrderList {
			store.AddLabTestExamOrderMap(order)
		}
		store.AddLabTestExamOrderList(data.LaboratoryTestExaminationOrderList)
		data.LaboratoryTestExaminationOrderList[0].Store = store
	} else {
		data.LaboratoryTestExaminationOrderList = []*doctororders.LabTestExamDoctorOrder{{}}
	}
	return nil
}

//func (s *LabTestOrderStruct) ProcessList(data *types.InputData) {
//	if data.LaboratoryTestExaminationOrderList != nil && len(data.LaboratoryTestExaminationOrderList) > 0 {
//		store := doctororders.NewLabTestExamDoctorOrderStore()
//		for _, order := range data.LaboratoryTestExaminationOrderList {
//			store.AddLabTestExamOrderMap(order)
//		}
//		store.AddLabTestExamOrderList(data.LaboratoryTestExaminationOrderList)
//	} else if data.LaboratoryTestExaminationOrderList == nil {
//		data.LaboratoryTestExaminationOrderList = []*doctororders.LabTestExamDoctorOrder{}
//	}
//}

type SurgicalOrderStruct struct{}
type SurgicalOrderRecordStruct struct{}

func (s *SurgicalOrderStruct) ProcessList(data *types.InputData, corpID string, mapping bool, client pb.RpcClient) error {
	if data.SurgicalDoctorOrdersList != nil && len(data.SurgicalDoctorOrdersList) > 0 {
		if mapping {
			for _, v := range data.SurgicalDoctorOrdersList {
				err := tools.SurgicalMasterDataMapping(v, corpID)
				if err != nil {
					return err
				}
				err = tools.SurgicalOrderSystemCodeMapping(v, corpID, client)
				if err != nil {
					return err
				}
			}
		}
		store := doctororders.NewSurgicalDoctorOrdersStore()
		for _, order := range data.SurgicalDoctorOrdersList {
			store.AddSurgicalDoctorOrdersMap(order)
		}
		store.AddSurgicalDoctorOrdersList(data.SurgicalDoctorOrdersList)
		data.SurgicalDoctorOrdersList[0].Store = store
	} else {
		data.SurgicalDoctorOrdersList = []*doctororders.SurgicalDoctorOrder{{}}
	}
	return nil
}

type WesternMedicineRecordStruct struct{}

func (w *WesternMedicineRecordStruct) ProcessList(data *types.InputData, corpID string, mapping bool, client pb.RpcClient) error {
	if data.WesternMedicineExecutionRecordList != nil && len(data.WesternMedicineExecutionRecordList) > 0 {
		if mapping {
			for _, v := range data.WesternMedicineExecutionRecordList {
				err := tools.WesternMedicineExecutionRecordMasterDataMapping(v, corpID)
				if err != nil {
					return err
				}
			}
		}
		store := executionrecord.NewMedicineDoctorOrdersStore()
		for _, order := range data.WesternMedicineExecutionRecordList {
			store.AddMedicalMap(order)
		}
		store.AddMedicalList(data.WesternMedicineExecutionRecordList)
		data.WesternMedicineExecutionRecordList[0].Store = store
	} else {
		data.WesternMedicineExecutionRecordList = []*executionrecord.WesternMedicineExecutionRecord{{}}
	}
	return nil
}

func (e *ExaminationOrderRecordStruct) ProcessList(data *types.InputData, corpID string, mapping bool, client pb.RpcClient) error {
	if data.ExaminationExecutionRecordList != nil && len(data.ExaminationExecutionRecordList) > 0 {
		if mapping {
			for _, v := range data.ExaminationExecutionRecordList {
				err := tools.ExaminationExecutionRecordMasterDataMapping(v, corpID)
				if err != nil {
					return err
				}
			}
		}
		store := executionrecord.NewExaminationExecutionRecordStore()
		for _, order := range data.ExaminationExecutionRecordList {
			store.AddExaminationMap(order)
		}
		store.AddExaminationList(data.ExaminationExecutionRecordList)
		data.ExaminationExecutionRecordList[0].Store = store
	} else {
		data.ExaminationExecutionRecordList = []*executionrecord.ExaminationExecutionRecord{{}}
	}
	return nil
}

func (e *LabTestOrderRecordStruct) ProcessList(data *types.InputData, corpID string, mapping bool, client pb.RpcClient) error {
	if data.LabExaminationExecutionRecordList != nil && len(data.LabExaminationExecutionRecordList) > 0 {
		if mapping {
			for _, v := range data.LabExaminationExecutionRecordList {
				err := tools.LabTestExamExecutionRecordMaterDataMapping(v, corpID)
				if err != nil {
					return err
				}
			}
		}
		store := executionrecord.NewLabExaminationExecutionRecordStore()
		for _, order := range data.LabExaminationExecutionRecordList {
			store.AddLabExaminationExecutionRecordMap(order)
		}
		store.AddLabExaminationExecutionRecordList(data.LabExaminationExecutionRecordList)
		data.LabExaminationExecutionRecordList[0].Store = store
	} else {
		data.LabExaminationExecutionRecordList = []*executionrecord.LabExaminationExecutionRecord{{}}
	}
	return nil
}

func (e *SurgicalOrderRecordStruct) ProcessList(data *types.InputData, corpID string, mapping bool, client pb.RpcClient) error {
	if data.SurgicalExecutionRecordList != nil && len(data.SurgicalExecutionRecordList) > 0 {
		if mapping {
			for _, v := range data.SurgicalExecutionRecordList {
				err := tools.SurgicalExecutionRecordMasterDataMapping(v, corpID)
				if err != nil {
					return err
				}
			}
		}
		store := executionrecord.NewSurgicalExecutionRecordStore()
		for _, order := range data.SurgicalExecutionRecordList {
			store.AddSurgicalExecutionRecordMap(order)
		}
		store.AddSurgicalExecutionRecordList(data.SurgicalExecutionRecordList)
		data.SurgicalExecutionRecordList[0].Store = store
	} else {
		data.SurgicalExecutionRecordList = []*executionrecord.SurgicalExecutionRecord{{}}
	}
	return nil
}

type DiagnosisStruct struct{}

func (s *DiagnosisStruct) ProcessList(data *types.InputData, corpID string, mapping bool, client pb.RpcClient) error {
	if data.DiagnosisList != nil && len(data.DiagnosisList) > 0 {
		store := dsl.NewDiagnosisStore()
		for _, diagnosis := range data.DiagnosisList {
			store.AddDiagnosisMap(diagnosis)
		}
		store.AddDiagnosisList(data.DiagnosisList)
		data.DiagnosisList[0].Store = store
	} else {
		data.DiagnosisList = []*dsl.Diagnosis{{}}
	}
	return nil
}

type AllergicHistoryStruct struct{}

func (a *AllergicHistoryStruct) ProcessList(data *types.InputData, corpID string, mapping bool, client pb.RpcClient) error {
	if data.AllergicHistoryList != nil && len(data.AllergicHistoryList) > 0 {
		store := medicalhistory.NewAllergicHistoryStore()
		for _, diagnosis := range data.AllergicHistoryList {
			store.AddAllergicHistoryMap(diagnosis)
		}
		store.AddAllergicHistoryList(data.AllergicHistoryList)
		data.AllergicHistoryList[0].Store = store
	} else {
		data.AllergicHistoryList = []*medicalhistory.AllergicHistory{{}}
	}
	return nil
}

func processInputData(data *types.InputData, mapping bool, corpID string) error {
	if data.Rule == nil {
		data.Rule = &types.Rule{}
	}
	conn := codingSystem.RpcClient()
	var client pb.RpcClient
	if conn != nil {
		defer conn.Close()
		client = pb.NewRpcClient(conn)
		log.Printf("gRPC connection successful")
	} else if mapping {
		log.Printf("gRPC connection failed, but mapping=%t", mapping)
	}

	if mapping && client != nil {
		if data.Patient == nil {
			data.Patient = &dsl.Patient{}
		}
		err := tools.PatientSystemCodeMapping(data.Patient, corpID, client)
		if err != nil {
			// 在开发环境中，如果gRPC编码服务不可用，记录警告但继续处理
			log.Printf("Warning: Patient system code mapping failed, continuing without mapping: %v", err)
			// return err  // 注释掉这行，允许在gRPC服务不可用时继续
		}
	}
	if data.Patient == nil {
		data.Patient = &dsl.Patient{}
	}
	if data.Emr == nil {
		data.Emr = &dsl.OutpEmr{}
	}

	list := []RuleReqParamInit{
		&WesternMedicineStruct{},
		&ExaminationOrderStruct{},
		&LabTestOrderStruct{},
		&SurgicalOrderStruct{},
		&WesternMedicineRecordStruct{},
		&ExaminationOrderRecordStruct{},
		&LabTestOrderRecordStruct{},
		&SurgicalOrderRecordStruct{},
		&DiagnosisStruct{},
		&AllergicHistoryStruct{},
	}
	var wg sync.WaitGroup
	errCh := make(chan error, len(list)) // 创建一个用于捕获错误的channel
	for _, i := range list {
		wg.Add(1)
		go func(s RuleReqParamInit) {
			defer wg.Done()
			if err := s.ProcessList(data, corpID, mapping, client); err != nil {
				errCh <- err // 将错误发送到channel
			}
		}(i)
	}
	// 开启一个协程等待所有任务完成
	go func() {
		wg.Wait()
		close(errCh) // 所有任务完成后关闭channel
	}()
	// 检查是否有错误
	for err := range errCh {
		if err != nil {
			return err // 返回第一个遇到的错误
		}
	}
	return nil
}

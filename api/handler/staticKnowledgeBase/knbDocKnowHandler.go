package staticKnowledgeBase

import (
	"github.com/gin-gonic/gin"
	"kmbservice/api/logic/staticKnowledgeBase"
	"kmbservice/api/types"
	"kmbservice/commons/response"
)

func KnbDocKnowLedgeHandler(c *gin.Context) {
	req, err := parseKnbDocKnowLedgeHandlerRequest(c)
	if err != nil {
		response.BadRequest(c, "", err)
		return
	}
	rsp, err := staticKnowledgeBase.KnbDocKnowLedge(req)
	if err != nil {
		response.InternalServerError(c, "", err)
		return
	}
	if rsp.Contents == "" && rsp.Description == "" && len(rsp.List) == 0 && rsp.TypeName == "" {
		response.NoRecord(c, "知识库无该病种信息", rsp)
		return
	}
	response.Success(c, rsp)
	return
}

func parseKnbDocKnowLedgeHandlerRequest(c *gin.Context) (types.KnbDocKnowLedgeRequest, error) {
	var req types.KnbDocKnowLedgeRequest
	err := c.ShouldBind(&req)
	if err != nil {
		return req, err
	}
	return req, nil
}

package staticKnowledgeBase

import (
	"github.com/gin-gonic/gin"
	"kmbservice/api/logic/staticKnowledgeBase"
	"kmbservice/api/types"
	"kmbservice/commons/response"
)

func DrugKnowLedgeHandler(c *gin.Context) {
	req, err := parseDrugKnowLedgeHandlerRequest(c)
	if err != nil {
		response.BadRequest(c, "", err)
		return
	}
	rsp, err := staticKnowledgeBase.GetDrugKnowLedge(req)
	if err != nil {
		response.InternalServerError(c, "", err)
		return
	}
	response.Success(c, rsp)
}

func parseDrugKnowLedgeHandlerRequest(c *gin.Context) (types.DrugKnowLedgeRequest, error) {
	var req types.DrugKnowLedgeRequest
	err := c.ShouldBind(&req)
	if err != nil {
		return req, err
	}
	return req, nil
}

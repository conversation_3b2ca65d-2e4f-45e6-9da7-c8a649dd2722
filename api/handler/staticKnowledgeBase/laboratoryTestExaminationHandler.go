package staticKnowledgeBase

import (
	"github.com/gin-gonic/gin"
	"kmbservice/api/logic/staticKnowledgeBase"
	"kmbservice/api/types"
	"kmbservice/commons/response"
)

func LaboratoryTestExaminationHandler(c *gin.Context) {
	req, err := parseLaboratoryTestExaminationHandlerRequest(c)
	if err != nil {
		response.BadRequest(c, "", err)
		return
	}
	rsp, err := staticKnowledgeBase.GetLaboratoryTestExamination(req)
	if err != nil {
		response.InternalServerError(c, "", err)
		return
	}
	response.Success(c, rsp)
}

func parseLaboratoryTestExaminationHandlerRequest(c *gin.Context) (types.LaboratoryTestExaminationRequest, error) {
	var req types.LaboratoryTestExaminationRequest
	err := c.ShouldBind(&req)
	if err != nil {
		return req, err
	}
	return req, nil
}

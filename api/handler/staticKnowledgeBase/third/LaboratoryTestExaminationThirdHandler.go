package third

import (
	"github.com/gin-gonic/gin"
	"kmbservice/api/logic/staticKnowledgeBase/third"
	"kmbservice/api/types"
	"kmbservice/commons/response"
)

func LaboratoryTestExaminationThirdHandler(c *gin.Context) {
	req, err := parseLaboratoryTestExaminationThirdHandlerRequest(c)
	if err != nil {
		response.BadRequest(c, "", err)
		return
	}
	rsp, err := third.GetLaboratoryTestExaminationThird(req)
	if err != nil {
		response.InternalServerError(c, "", err)
		return
	}
	if rsp.ResourceName == "" && rsp.Contents == "" {
		response.NoRecord(c, "知识库无该检验检查信息", rsp)
		return
	}
	response.Success(c, rsp)
	return
}

func parseLaboratoryTestExaminationThirdHandlerRequest(c *gin.Context) (types.LaboratoryTestExaminationThirdRequest, error) {
	var req types.LaboratoryTestExaminationThirdRequest
	err := c.ShouldBind(&req)
	if err != nil {
		return req, err
	}
	return req, nil
}

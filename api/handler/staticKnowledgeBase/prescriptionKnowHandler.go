package staticKnowledgeBase

import (
	"github.com/gin-gonic/gin"
	"kmbservice/api/logic/staticKnowledgeBase"
	"kmbservice/api/types"
	"kmbservice/commons/response"
)

func PrescriptionKnowLedgeHandler(c *gin.Context) {
	req, err := parsePrescriptionKnowLedgeHandlerRequest(c)
	if err != nil {
		response.BadRequest(c, "", err)
		return
	}
	rsp, err := staticKnowledgeBase.GetPrescriptionKnowLedge(req)
	if err != nil {
		response.InternalServerError(c, "", err)
		return
	}
	if rsp.TCMID == 0 {
		response.NoRecord(c, "知识库无该方剂信息", rsp)
		return
	}
	response.Success(c, rsp)
	return
}

func parsePrescriptionKnowLedgeHandlerRequest(c *gin.Context) (types.PrescriptionKnowLedgeRequest, error) {
	var req types.PrescriptionKnowLedgeRequest
	err := c.ShouldBind(&req)
	if err != nil {
		return req, err
	}
	return req, nil
}

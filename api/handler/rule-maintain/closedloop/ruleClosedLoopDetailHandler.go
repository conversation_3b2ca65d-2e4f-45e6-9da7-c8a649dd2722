package closedloop

import (
	"github.com/gin-gonic/gin"
	ruleMaintain "kmbservice/api/logic/rule-maintain"
	"kmbservice/api/types"
	"kmbservice/commons/response"
)

func RuleClosedLoopDetailHandler(c *gin.Context) {
	req, err := parseRuleClosedLoopDetailHandlerRequest(c)
	if err != nil {
		response.BadRequest(c, "", err)
		return
	}
	rsp, err := ruleMaintain.RuleClosedLoopDetail(req)
	if err != nil {
		response.InternalServerError(c, "", err)
		return
	}
	response.Success(c, rsp)
}

func parseRuleClosedLoopDetailHandlerRequest(c *gin.Context) (types.RuleClosedLoopDetailRequest, error) {
	var req types.RuleClosedLoopDetailRequest
	err := c.ShouldBind(&req)
	if err != nil {
		return req, err
	}
	return req, nil
}

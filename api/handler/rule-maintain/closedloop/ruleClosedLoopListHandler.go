package closedloop

import (
	"github.com/gin-gonic/gin"
	ruleMaintain "kmbservice/api/logic/rule-maintain"
	"kmbservice/api/types"
	"kmbservice/commons/response"
)

func RuleClosedLoopListHandler(c *gin.Context) {
	req, err := parseRuleClosedLoopListHandlerRequest(c)
	if err != nil {
		response.BadRequest(c, "", err)
		return
	}
	rsp, err := ruleMaintain.RuleClosedLoopList(req)
	if err != nil {
		response.InternalServerError(c, "", err)
		return
	}
	response.Success(c, rsp)
}

func parseRuleClosedLoopListHandlerRequest(c *gin.Context) (types.RuleClosedLoopListRequest, error) {
	var req types.RuleClosedLoopListRequest
	err := c.ShouldBind(&req)
	if err != nil {
		return req, err
	}
	return req, nil
}

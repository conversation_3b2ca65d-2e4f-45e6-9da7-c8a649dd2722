package tools

import (
	"context"
	"errors"
	"log"
	"kmbservice/api/rpcclient/codingSystem/pb"
	"kmbservice/commons/enums/enginEnums"
	"kmbservice/dsl"
	"kmbservice/dsl/doctororders"
	"kmbservice/repository/models"
	"strconv"
	"time"
)

var dmf_data_sets_model models.DMFDataSets
var rule_code_system_conf_model models.RuleCodeSystemConf

// 患者
func PatientSystemCodeMapping(in *dsl.Patient, corpID string, client pb.RpcClient) error {
	if err := convertCode(&in.InsuranceType, corpID, enginEnums.PatientDSL, dsl.InsuranceType, client, "患者参保类型入参错误"); err != nil {
		return err
	}
	if err := convertCode(&in.PatientGender, corpID, enginEnums.PatientDSL, dsl.PatientGender, client, "患者性别入参错误"); err != nil {
		return err
	}
	if err := convertCode(&in.MaritalStatus, corpID, enginEnums.PatientDSL, dsl.MaritalStatus, client, "患者婚姻情况入参错误"); err != nil {
		return err
	}
	return nil
}

// 西药医嘱
func WesternMedicineOrderSystemCodeMapping(in *doctororders.WesternMedicineDoctorOrders, corpID string, client pb.RpcClient) error {
	if err := convertCode(&in.UrgentMark, corpID, enginEnums.WesternMedicineDoctorOrderDSL, doctororders.UrgentMark, client, "加急标识入参错误"); err != nil {
		return err
	}
	if err := convertCode(&in.OrderDuration, corpID, enginEnums.WesternMedicineDoctorOrderDSL, doctororders.OrderDuration, client, "医嘱时效性入参错误"); err != nil {
		return err
	}
	if err := convertCode(&in.DrugDosageUnit, corpID, enginEnums.WesternMedicineDoctorOrderDSL, doctororders.DrugDosageUnit, client, "医嘱计量单位入参错误"); err != nil {
		return err
	}
	if err := convertCode(&in.RouteOfAdministration, corpID, enginEnums.WesternMedicineDoctorOrderDSL, doctororders.RouteOfAdministration, client, "给药途径入参错误"); err != nil {
		return err
	}
	return nil
}

// 检查医嘱
func ExaminationOrderSystemCodeMapping(in *doctororders.ExaminationOrder, corpID string, client pb.RpcClient) error {
	if err := convertCode(&in.UrgentMark, corpID, enginEnums.WesternMedicineDoctorOrderDSL, doctororders.UrgentMark, client, "加急标识入参错误"); err != nil {
		return err
	}
	if err := convertCode(&in.OrderDuration, corpID, enginEnums.WesternMedicineDoctorOrderDSL, doctororders.OrderDuration, client, "医嘱时效性入参错误"); err != nil {
		return err
	}
	return nil
}

// 检验医嘱
func LaboratoryExaminationOrderSystemCodeMapping(in *doctororders.LabTestExamDoctorOrder, corpID string, client pb.RpcClient) error {
	if err := convertCode(&in.UrgentMark, corpID, enginEnums.WesternMedicineDoctorOrderDSL, doctororders.UrgentMark, client, "加急标识入参错误"); err != nil {
		return err
	}
	if err := convertCode(&in.OrderDuration, corpID, enginEnums.WesternMedicineDoctorOrderDSL, doctororders.OrderDuration, client, "医嘱时效性入参错误"); err != nil {
		return err
	}
	return nil
}

// 手术医嘱
func SurgicalOrderSystemCodeMapping(in *doctororders.SurgicalDoctorOrder, corpID string, client pb.RpcClient) error {
	if err := convertCode(&in.UrgentMark, corpID, enginEnums.WesternMedicineDoctorOrderDSL, doctororders.UrgentMark, client, "加急标识入参错误"); err != nil {
		return err
	}
	if err := convertCode(&in.OrderDuration, corpID, enginEnums.WesternMedicineDoctorOrderDSL, doctororders.OrderDuration, client, "医嘱时效性入参错误"); err != nil {
		return err
	}
	return nil
}

func convertCode(field *string, corpID, category, codeType string, client pb.RpcClient, errMsg string) error {
	if *field == "" {
		return nil
	}
	confInfo, err := rule_code_system_conf_model.FindByDataValueCodeANDCodeSystemId(corpID, category, codeType)
	if err != nil || confInfo == nil {
		return errors.New("规则对象配置信息查询失败")
	}
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	req := &pb.CodeSystemConvertRequest{
		CorpId:             corpID,
		Code:               *field,
		SourceCodesystemId: confInfo.DicID,
	}
	res, err := client.CodeSystemConvert(ctx, req)
	if err != nil {
		log.Printf("gRPC CodeSystemConvert error: %v, request: %+v", err, req)
		return errors.New(errMsg + ": " + err.Error())
	}
	if res == nil {
		log.Printf("gRPC CodeSystemConvert returned nil response for request: %+v", req)
		return errors.New(errMsg + ": response is nil")
	}
	*field = strconv.FormatInt(res.TargetDataValueSoid, 10)
	return nil
}

//func PatientSystemCodeMapping(in *dsl.Patient, corp_id string) error {
//	if in.InsuranceType != "" {
//		conf_info, err := rule_code_system_conf_model.FindByDataValueCodeANDCodeSystemId(corp_id, enginEnums.PatientDSL, dsl.InsuranceType)
//		if err != nil || conf_info == nil {
//			err = errors.New("规则对象配置信息查询失败")
//			return err
//		}
//		conn := codingSystem.RpcClient()
//		defer conn.Close()
//		client := pb.NewRpcClient(conn)
//		ctx, cancel := context.WithTimeout(context.Background(), time.Second)
//		defer cancel()
//		req := &pb.CodeSystemConvertRequest{
//			CorpId:             corp_id,
//			Code:               in.InsuranceType,
//			SourceCodesystemId: conf_info.DicID,
//		}
//		res, err := client.CodeSystemConvert(ctx, req)
//		if err != nil || res == nil {
//			err = errors.New("患者参保类型入参错误")
//		} else {
//			in.InsuranceType = strconv.FormatInt(res.TargetDataValueSoid, 10)
//		}
//	}
//	if in.PatientGender != "" {
//		conf_info, err := rule_code_system_conf_model.FindByDataValueCodeANDCodeSystemId(corp_id, enginEnums.PatientDSL, dsl.PatientGender)
//		if err != nil || conf_info == nil {
//			err = errors.New("规则对象配置信息查询失败")
//			return err
//		}
//		conn := codingSystem.RpcClient()
//		defer conn.Close()
//		client := pb.NewRpcClient(conn)
//		ctx, cancel := context.WithTimeout(context.Background(), time.Second)
//		defer cancel()
//		req := &pb.CodeSystemConvertRequest{
//			CorpId:             corp_id,
//			Code:               in.PatientGender,
//			SourceCodesystemId: conf_info.DicID,
//		}
//		res, err := client.CodeSystemConvert(ctx, req)
//		if err != nil || res == nil {
//			err = errors.New("患者性别入参错误")
//		} else {
//			in.PatientGender = strconv.FormatInt(res.TargetDataValueSoid, 10)
//		}
//	}
//	return nil
//}

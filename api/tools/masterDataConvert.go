package tools

import (
	"errors"
	"kmbservice/dsl/doctororders"
	"kmbservice/dsl/executionrecord"
	"kmbservice/repository/models"
	"strconv"
)

var jzMasterData models.CombinedMedicine
var mapping models.DksKnbDicMapping

const (
	WesternMedicineAndChinesePatentMedicine = "西药中成药"
	Examination                             = "检查      "
	LaboratoryExamination                   = "化验      "
	SurgicalOrder                           = "其他      "
)

// 西药属性值映射
func WesternMedicineMasterDataMapping(in *doctororders.WesternMedicineDoctorOrders, corp_id string) error {
	switch corp_id {
	case "1":
		{
			list, _ := jzMasterData.FindCombinedMedicine(0, in.MedicationPrescriptionItem, "", WesternMedicineAndChinesePatentMedicine, "")
			if len(list) > 1 {
				return errors.New("精总西药主数据不唯一，请排查")
			}
			if len(list) < 1 {
				return errors.New("精总西药主数据未查询到该数据")
			}
			result, _ := mapping.GetoneBySoidAndOrientation("st", list[0].ID)
			if result != nil {
				tID := strconv.FormatInt(result.TargetDicID, 10)
				in.MedicationPrescriptionItem = tID
				return nil
			}
			return errors.New("字典映射表中缺少西药医嘱映射关系")
		}
	default:
		return errors.New("合作方不存在")
	}
}

func WesternMedicineExecutionRecordMasterDataMapping(in *executionrecord.WesternMedicineExecutionRecord, corp_id string) error {
	switch corp_id {
	case "1":
		{
			list, _ := jzMasterData.FindCombinedMedicine(0, in.ExecutionTime, "", WesternMedicineAndChinesePatentMedicine, "")
			if len(list) > 1 {
				return errors.New("精总西药主数据不唯一，请排查")
			}
			if len(list) < 1 {
				return errors.New("精总西药主数据未查询到该数据")
			}
			result, _ := mapping.GetoneBySoidAndOrientation("st", list[0].ID)
			if result != nil {
				tID := strconv.FormatInt(result.TargetDicID, 10)
				in.ExecutionItem = tID
				return nil
			}
			return errors.New("字典映射表中缺少西药医嘱映射关系")
		}
	default:
		return errors.New("合作方不存在")
	}
}

// 检查属性映射
func ExaminationMasterDataMapping(in *doctororders.ExaminationOrder, corp_id string) error {
	switch corp_id {
	case "1":
		{
			list, _ := jzMasterData.FindCombinedMedicine(0, in.ExaminationOrderItem, "", Examination, "")
			if len(list) > 1 {
				return errors.New("精总检查主数据不唯一，请排查")
			}
			if len(list) < 1 {
				return errors.New("精总检查主数据未查询到该数据")
			}
			result, _ := mapping.GetoneBySoidAndOrientation("st", list[0].ID)
			if result != nil {
				tID := strconv.FormatInt(result.TargetDicID, 10)
				in.ExaminationOrderItem = tID
				return nil
			}
			return errors.New("字典映射表中缺少检查医嘱映射关系")
		}
	default:
		return errors.New("合作方不存在")
	}
}

func ExaminationExecutionRecordMasterDataMapping(in *executionrecord.ExaminationExecutionRecord, corp_id string) error {
	switch corp_id {
	case "1":
		{
			list, _ := jzMasterData.FindCombinedMedicine(0, in.ExecutionItem, "", Examination, "")
			if len(list) > 1 {
				return errors.New("精总检查主数据不唯一，请排查")
			}
			if len(list) < 1 {
				return errors.New("精总检查主数据未查询到该数据")
			}
			result, _ := mapping.GetoneBySoidAndOrientation("st", list[0].ID)
			if result != nil {
				tID := strconv.FormatInt(result.TargetDicID, 10)
				in.ExecutionItem = tID
				return nil
			}
			return errors.New("字典映射表中缺少检查医嘱映射关系")
		}
	default:
		return errors.New("合作方不存在")
	}
}

// 检验属性映射
func LabTestExamMaterDataMapping(in *doctororders.LabTestExamDoctorOrder, corp_id string) error {
	switch corp_id {
	case "1":
		{
			list, _ := jzMasterData.FindCombinedMedicine(0, in.LaboratoryTestOrder, "", LaboratoryExamination, "")
			if len(list) > 1 {
				return errors.New("精总检查主数据不唯一，请排查")
			}
			if len(list) < 1 {
				return errors.New("精总检查主数据未查询到该数据")
			}
			result, _ := mapping.GetoneBySoidAndOrientation("st", list[0].ID)
			if result != nil {
				tID := strconv.FormatInt(result.TargetDicID, 10)
				in.LaboratoryTestOrder = tID
				return nil
			}
			return errors.New("字典映射表中缺少检验医嘱映射关系")
		}
	default:
		return errors.New("合作方不存在")
	}
}

func LabTestExamExecutionRecordMaterDataMapping(in *executionrecord.LabExaminationExecutionRecord, corp_id string) error {
	switch corp_id {
	case "1":
		{
			list, _ := jzMasterData.FindCombinedMedicine(0, in.ExecutionItem, "", LaboratoryExamination, "")
			if len(list) > 1 {
				return errors.New("精总检查主数据不唯一，请排查")
			}
			if len(list) < 1 {
				return errors.New("精总检查主数据未查询到该数据")
			}
			result, _ := mapping.GetoneBySoidAndOrientation("st", list[0].ID)
			if result != nil {
				tID := strconv.FormatInt(result.TargetDicID, 10)
				in.ExecutionItem = tID
				return nil
			}
			return errors.New("字典映射表中缺少检验医嘱映射关系")
		}
	default:
		return errors.New("合作方不存在")
	}
}

// 手术属性映射
func SurgicalMasterDataMapping(in *doctororders.SurgicalDoctorOrder, corp_id string) error {
	switch corp_id {
	case "1":
		{
			list, _ := jzMasterData.FindCombinedMedicine(0, in.SurgicalOrderitem, "", SurgicalOrder, "")
			if len(list) > 1 {
				return errors.New("精总检查主数据不唯一，请排查")
			}
			if len(list) < 1 {
				return errors.New("精总检查主数据未查询到该数据")
			}
			result, _ := mapping.GetoneBySoidAndOrientation("st", list[0].ID)
			if result != nil {
				tID := strconv.FormatInt(result.TargetDicID, 10)
				in.SurgicalOrderitem = tID
				return nil
			}
			return errors.New("字典映射表中缺少手术医嘱映射关系")
		}
	default:
		return errors.New("合作方不存在")
	}
}

func SurgicalExecutionRecordMasterDataMapping(in *executionrecord.SurgicalExecutionRecord, corp_id string) error {
	switch corp_id {
	case "1":
		{
			list, _ := jzMasterData.FindCombinedMedicine(0, in.ExecutionItem, "", SurgicalOrder, "")
			if len(list) > 1 {
				return errors.New("精总检查主数据不唯一，请排查")
			}
			if len(list) < 1 {
				return errors.New("精总检查主数据未查询到该数据")
			}
			result, _ := mapping.GetoneBySoidAndOrientation("st", list[0].ID)
			if result != nil {
				tID := strconv.FormatInt(result.TargetDicID, 10)
				in.ExecutionItem = tID
				return nil
			}
			return errors.New("字典映射表中缺少手术医嘱映射关系")
		}
	default:
		return errors.New("合作方不存在")
	}
}

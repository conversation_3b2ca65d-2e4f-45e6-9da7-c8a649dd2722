package codingSystem

import (
	"context"
	"net"
	"time"

	"github.com/prometheus/common/log"
	"google.golang.org/grpc"
)

func RpcClient() *grpc.ClientConn {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	
	// 创建直连的Dialer，绕过HTTP代理
	dialer := &net.Dialer{
		Timeout: 3 * time.Second,
	}
	
	conn, err := grpc.DialContext(ctx, "10.0.0.155:9894", 
		grpc.WithInsecure(),
		grpc.WithContextDialer(func(ctx context.Context, addr string) (net.Conn, error) {
			return dialer.DialContext(ctx, "tcp", addr)
		}))
	if err != nil {
		log.Errorf("failed to connect to gRPC service: %v", err)
		return nil
	}
	return conn
}

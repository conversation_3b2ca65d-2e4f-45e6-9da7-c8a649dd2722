// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.27.3
// source: codeSystem.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Rpc_CodeSystemConvert_FullMethodName = "/codeSystem.Rpc/CodeSystemConvert"
)

// RpcClient is the client API for Rpc service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RpcClient interface {
	CodeSystemConvert(ctx context.Context, in *CodeSystemConvertRequest, opts ...grpc.CallOption) (*CodeSystemConvertResponse, error)
}

type rpcClient struct {
	cc grpc.ClientConnInterface
}

func NewRpcClient(cc grpc.ClientConnInterface) RpcClient {
	return &rpcClient{cc}
}

func (c *rpcClient) CodeSystemConvert(ctx context.Context, in *CodeSystemConvertRequest, opts ...grpc.CallOption) (*CodeSystemConvertResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CodeSystemConvertResponse)
	err := c.cc.Invoke(ctx, Rpc_CodeSystemConvert_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RpcServer is the server API for Rpc service.
// All implementations must embed UnimplementedRpcServer
// for forward compatibility.
type RpcServer interface {
	CodeSystemConvert(context.Context, *CodeSystemConvertRequest) (*CodeSystemConvertResponse, error)
	mustEmbedUnimplementedRpcServer()
}

// UnimplementedRpcServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRpcServer struct{}

func (UnimplementedRpcServer) CodeSystemConvert(context.Context, *CodeSystemConvertRequest) (*CodeSystemConvertResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CodeSystemConvert not implemented")
}
func (UnimplementedRpcServer) mustEmbedUnimplementedRpcServer() {}
func (UnimplementedRpcServer) testEmbeddedByValue()             {}

// UnsafeRpcServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RpcServer will
// result in compilation errors.
type UnsafeRpcServer interface {
	mustEmbedUnimplementedRpcServer()
}

func RegisterRpcServer(s grpc.ServiceRegistrar, srv RpcServer) {
	// If the following call pancis, it indicates UnimplementedRpcServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Rpc_ServiceDesc, srv)
}

func _Rpc_CodeSystemConvert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CodeSystemConvertRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcServer).CodeSystemConvert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rpc_CodeSystemConvert_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcServer).CodeSystemConvert(ctx, req.(*CodeSystemConvertRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Rpc_ServiceDesc is the grpc.ServiceDesc for Rpc service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Rpc_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "codeSystem.Rpc",
	HandlerType: (*RpcServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CodeSystemConvert",
			Handler:    _Rpc_CodeSystemConvert_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "codeSystem.proto",
}

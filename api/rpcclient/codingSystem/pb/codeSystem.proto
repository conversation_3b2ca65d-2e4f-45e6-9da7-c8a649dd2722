syntax = "proto3";

package codeSystem;
option go_package = "./;pb";

message CodeSystemConvertRequest{
  string code = 1;//编码体系明码
  int64 source_codesystem_id = 2;//源代码系统SOID
  string corp_id = 3;//合作方id
}


message CodeSystemConvertResponse{
  int64   target_data_value_soid = 1;//目标soid
}

service Rpc {
  rpc CodeSystemConvert(CodeSystemConvertRequest)returns(CodeSystemConvertResponse);//编码系统编码转换
}

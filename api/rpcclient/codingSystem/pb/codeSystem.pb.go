// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.27.3
// source: codeSystem.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CodeSystemConvertRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code               string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`                                                          //编码体系明码
	SourceCodesystemId int64  `protobuf:"varint,2,opt,name=source_codesystem_id,json=sourceCodesystemId,proto3" json:"source_codesystem_id,omitempty"` //源代码系统SOID
	CorpId             string `protobuf:"bytes,3,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`                                        //合作方id
}

func (x *CodeSystemConvertRequest) Reset() {
	*x = CodeSystemConvertRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codeSystem_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CodeSystemConvertRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeSystemConvertRequest) ProtoMessage() {}

func (x *CodeSystemConvertRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codeSystem_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeSystemConvertRequest.ProtoReflect.Descriptor instead.
func (*CodeSystemConvertRequest) Descriptor() ([]byte, []int) {
	return file_codeSystem_proto_rawDescGZIP(), []int{0}
}

func (x *CodeSystemConvertRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CodeSystemConvertRequest) GetSourceCodesystemId() int64 {
	if x != nil {
		return x.SourceCodesystemId
	}
	return 0
}

func (x *CodeSystemConvertRequest) GetCorpId() string {
	if x != nil {
		return x.CorpId
	}
	return ""
}

type CodeSystemConvertResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetDataValueSoid int64 `protobuf:"varint,1,opt,name=target_data_value_soid,json=targetDataValueSoid,proto3" json:"target_data_value_soid,omitempty"` //目标soid
}

func (x *CodeSystemConvertResponse) Reset() {
	*x = CodeSystemConvertResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codeSystem_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CodeSystemConvertResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeSystemConvertResponse) ProtoMessage() {}

func (x *CodeSystemConvertResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codeSystem_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeSystemConvertResponse.ProtoReflect.Descriptor instead.
func (*CodeSystemConvertResponse) Descriptor() ([]byte, []int) {
	return file_codeSystem_proto_rawDescGZIP(), []int{1}
}

func (x *CodeSystemConvertResponse) GetTargetDataValueSoid() int64 {
	if x != nil {
		return x.TargetDataValueSoid
	}
	return 0
}

var File_codeSystem_proto protoreflect.FileDescriptor

var file_codeSystem_proto_rawDesc = []byte{
	0x0a, 0x10, 0x63, 0x6f, 0x64, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x63, 0x6f, 0x64, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x22, 0x79,
	0x0a, 0x18, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x30,
	0x0a, 0x14, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x6f, 0x72, 0x70, 0x49, 0x64, 0x22, 0x50, 0x0a, 0x19, 0x43, 0x6f, 0x64,
	0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x16, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x73, 0x6f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x53, 0x6f, 0x69, 0x64, 0x32, 0x67, 0x0a, 0x03, 0x52,
	0x70, 0x63, 0x12, 0x60, 0x0a, 0x11, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x12, 0x24, 0x2e, 0x63, 0x6f, 0x64, 0x65, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e,
	0x63, 0x6f, 0x64, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x07, 0x5a, 0x05, 0x2e, 0x2f, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_codeSystem_proto_rawDescOnce sync.Once
	file_codeSystem_proto_rawDescData = file_codeSystem_proto_rawDesc
)

func file_codeSystem_proto_rawDescGZIP() []byte {
	file_codeSystem_proto_rawDescOnce.Do(func() {
		file_codeSystem_proto_rawDescData = protoimpl.X.CompressGZIP(file_codeSystem_proto_rawDescData)
	})
	return file_codeSystem_proto_rawDescData
}

var file_codeSystem_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_codeSystem_proto_goTypes = []any{
	(*CodeSystemConvertRequest)(nil),  // 0: codeSystem.CodeSystemConvertRequest
	(*CodeSystemConvertResponse)(nil), // 1: codeSystem.CodeSystemConvertResponse
}
var file_codeSystem_proto_depIdxs = []int32{
	0, // 0: codeSystem.Rpc.CodeSystemConvert:input_type -> codeSystem.CodeSystemConvertRequest
	1, // 1: codeSystem.Rpc.CodeSystemConvert:output_type -> codeSystem.CodeSystemConvertResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_codeSystem_proto_init() }
func file_codeSystem_proto_init() {
	if File_codeSystem_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_codeSystem_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*CodeSystemConvertRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codeSystem_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*CodeSystemConvertResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_codeSystem_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_codeSystem_proto_goTypes,
		DependencyIndexes: file_codeSystem_proto_depIdxs,
		MessageInfos:      file_codeSystem_proto_msgTypes,
	}.Build()
	File_codeSystem_proto = out.File
	file_codeSystem_proto_rawDesc = nil
	file_codeSystem_proto_goTypes = nil
	file_codeSystem_proto_depIdxs = nil
}

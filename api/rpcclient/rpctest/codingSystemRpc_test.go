package rpctest

import (
	"context"
	"fmt"
	"kmbservice/api/rpcclient/codingSystem"
	"kmbservice/api/rpcclient/codingSystem/pb"
	"testing"
)

func TestRpcclient(t *testing.T) {
	conn := codingSystem.RpcClient()
	defer conn.Close()
	client := pb.NewRpcClient(conn)
	req := &pb.CodeSystemConvertRequest{
		SourceCodesystemId: 1655002557155770827,
		Code:               "g1",
		CorpId:             "0",
	}
	res, err := client.CodeSystemConvert(context.Background(), req)
	fmt.Println(res, err)
}

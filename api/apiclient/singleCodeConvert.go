package apiclient

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"
)

func SingleCodeConvert(orientation, code, codeSystemID string) (result string, err error) {
	baseUrl := "/api/code_system/convert_soid/soid/convert"
	url := fmt.Sprintf("%s:%s%s", "http://10.0.0.155", "9894", baseUrl)
	requestUid := CodeConvertSoidRequest{
		Orientation:  orientation,
		Code:         code,
		CodeSystemID: codeSystemID,
	}
	jsonToken, err := json.Marshal(requestUid)
	if err != nil {
		return "", err
	}
	if jsonToken == nil {
		return "", err
	}

	requests, err1 := http.NewRequest("POST", url, bytes.NewBuffer(jsonToken))
	if err1 != nil {
		return "", err
	}
	requests.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 5 * time.Second, // 设置超时时间为5秒
	}
	response, err2 := client.Do(requests)
	if err2 != nil {
		return "", err
	}
	defer response.Body.Close()

	body, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return "", err
	}
	ttt := string(body)
	fmt.Println(ttt)
	var apiResponse SingleCodeConvertResponse
	if err = json.Unmarshal([]byte(ttt), &apiResponse); err != nil {
		return "", err
	}
	return apiResponse.Data.ResSoid, err
}

type CodeConvertSoidRequest struct {
	Orientation  string `json:"orientation"` //转换方向（st/ts）
	Code         string `json:"code"`
	CodeSystemID string `json:"code_system_id"`
}
type CodeConvertSoidResponse struct {
	ResSoid         string `json:"res_soid"`
	ResCodeSystemID string `json:"res_code_system_id"`
}
type SingleCodeConvertResponse struct {
	Code int                     `json:"code"`
	Msg  string                  `json:"msg"`
	Data CodeConvertSoidResponse `json:"data,omitempty"`
}

package apiclient

//import (
//	"bytes"
//	"encoding/json"
//	"fmt"
//	"io/ioutil"
//	"kmbservice/pkg/logging"
//	"kmbservice/pkg/setting"
//	"log"
//	"net/http"
//	"runtime/debug"
//	"time"
//)
//
//func MasterDataCodeConvert(orientation string, id_list []string) {
//	defer func() {
//		if err := recover(); err != nil {
//			log.Printf("panic: %v\n%s", err, debug.Stack())
//		}
//	}()
//	baseUrl := "/api/code_system/convert_soid/master_data"
//	url := fmt.Sprintf("%s:%s%s", setting.CodingSystemServerSetting.Host, setting.CodingSystemServerSetting.Port, baseUrl)
//	//url := "http://localhost:9891/api/v1/others/treatment_service/get"
//	requestUid := masterDataCodeConvertRequest{
//		Orientation: orientation,
//		IDList:      id_list,
//	}
//	jsonToken, err := json.Marshal(requestUid)
//	if err != nil {
//		logging.Error("json转化失败")
//		return "", err
//	}
//	if jsonToken == nil {
//		logging.Error("请求体为空")
//		return "", err
//	}
//
//	requests, err1 := http.NewRequest("POST", url, bytes.NewBuffer(jsonToken))
//	if err1 != nil {
//		logging.Error("创建请求失败", err)
//		return "nil", err
//	}
//	requests.Header.Set("Content-Type", "application/json")
//
//	client := &http.Client{
//		Timeout: 5 * time.Second, // 设置超时时间为5秒
//	}
//	response, err2 := client.Do(requests)
//	if err2 != nil {
//		logging.Error("发送请求失败:%v", err)
//		return "nil", err
//	}
//	defer response.Body.Close()
//
//	body, err := ioutil.ReadAll(response.Body)
//	if err != nil {
//		logging.Error("读取响应失败:%v", err)
//		return "nil", err
//	}
//	ttt := string(body)
//	var testItems Response
//	if err := json.Unmarshal([]byte(ttt), &testItems); err != nil {
//		logging.Error(err)
//		return "nil", err
//	}
//	return testItems.Data, err
//}
//
//type masterDataCodeConvertRequest struct {
//	Orientation string   `json:"orientation"` //转换方向（st/ts）
//	IDList      []string `json:"id_list"`     //id列表
//}

package rule_maintain

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	ruleEnum "kmbservice/api/logic/enum"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
	"strings"
)

func RuleCreateNew(c *gin.Context) {
	var (
		reqobj apitypes.RuleCreateNewRequest
		rspobj apitypes.RuleCreateNewResponse
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 400, "入参错误", err)
		return
	}
	ruledetailmodel := models.RuleContentDetail{}
	ruledetail_obj_mapping := models.RuleContentDetailObjectMapping{}
	dmf_data_sets_model := models.DMFDataSets{}
	reqJsonData, err := json.MarshalIndent(reqobj, "", "    ")
	if err != nil {
		response.Response(c, 500, "入参结构解析失败:", err)
		return
	}
	ruleBody, err := json.MarshalIndent(reqobj.RuleBody, "", "    ")
	if err != nil {
		response.Response(c, 500, "规则体解析失败:", err)
		return
	}
	rule_header := fmt.Sprintf(`rule "%s" salience`, reqobj.RuleName)
	rulebody := buildConditionString(reqobj.RuleBody)
	rule_else := fmt.Sprintf(` %s.%s("%s","%s","%s")`, "patient", "AddMessage",
		reqobj.RuleResult.Code, reqobj.RuleResult.Content, "00001")
	//合体!
	finalRule := fmt.Sprintf(`%s 
begin
    if %s {
        %s
    }
end`, rule_header, rulebody, rule_else)
	db := models.GetDb()
	tx := db.Begin()
	if tx.Error != nil {
		response.Response(c, 500, "事务启动失败", tx.Error)
		return
	}
	//提示语
	p := reqobj.RuleResult.Content
	//提示类型
	if reqobj.RuleResult.Code != "" {
		wInfo, _ := dmf_data_sets_model.FindByDataValueCodeANDCodeSystemId(reqobj.RuleResult.Code, ruleEnum.WarringCode)
		if wInfo != nil {
			reqobj.RuleResult.Code = wInfo.DataValueCNMeaning
		}
	}
	w := reqobj.RuleResult.Code
	//处理状态符
	var intstatus int64
	intstatus = 1
	//处理规则详情
	var CnOperator string
	if reqobj.RuleBody.Operator != "" {
		switch reqobj.RuleBody.Operator {
		case "&&":
			CnOperator = "AND"
		case "||":
			CnOperator = "OR"

		}
	} else {
		response.Response(c, 500, "逻辑运算符不可为空", "")
		return
	}
	var ruleParticulars string
	if len(reqobj.RuleParticulars) > 0 {
		var builder strings.Builder
		for i, str := range reqobj.RuleParticulars {
			builder.WriteString(str)
			// 如果不是最后一个元素，在末尾添加 " AND "
			if i < len(reqobj.RuleParticulars)-1 {
				builder.WriteString("&nbsp&nbsp " + CnOperator)
			}
			builder.WriteString("<br/>")
			ruleParticulars = builder.String()
		}
	}
	ruleId, detailVersionId, err := ruledetailmodel.CreateRuleDetailTx(tx, reqobj.RuleName, finalRule, reqobj.RuleDesc, ruleParticulars, p, w, reqJsonData, ruleBody, intstatus)
	if err != nil {
		tx.Rollback()
		response.Response(c, 500, "规则数据创建失败", err)
		return
	}
	if ruleId == 0 {
		response.Response(c, 500, "规则数据创建失败", err)
		return
	}
	//规则与对象映射
	var intSlice []int64
	if len(reqobj.RuleClassficationID) != 0 {
		for _, str := range reqobj.RuleClassficationID {
			num, err := strconv.ParseInt(str, 10, 64)
			if err != nil {
				fmt.Printf("转换错误: %v\n", err)
				continue
			}
			intSlice = append(intSlice, num)
		}
	}
	errobj := ruledetail_obj_mapping.CreateMappingsTx(tx, ruleId, intSlice)
	if errobj != nil {
		tx.Rollback()
		response.Response(c, 500, "映射关系创建失败", err)
		return
	}
	if errtx := tx.Commit().Error; errtx != nil {
		response.Response(c, 500, "事务提交失败", errtx)
		return
	}
	ruleIdStr := strconv.FormatInt(ruleId, 10)
	rspobj.RuleID = ruleIdStr
	detailVersionIdStr := strconv.FormatInt(detailVersionId, 10)
	rspobj.DetailVersionId = detailVersionIdStr
	rspobj.RuleDesc = reqobj.RuleDesc
	rspobj.RuleContent = finalRule
	rspobj.RuleCname = string(reqJsonData)
	response.Response(c, 200, "success", rspobj)
	return
}

package rule_maintain

import (
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
)

func RuleMethodMaintain(c *gin.Context) {
	var (
		reqobj apitypes.RuleMethodMaintainRequest
		rspobj apitypes.RuleMethodMaintainResponse
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 400, "入参错误", err)
		return
	}
	dslpro := models.DksKnbDslProperty{}
	//查询属性是否存在
	intpid, _ := strconv.ParseInt(reqobj.PropertyID, 10, 64)
	prolist, err := dslpro.GetDksKnbDslPropertiesByPropertyID(intpid)
	if err != nil {
		response.Response(c, 500, "网络故障", err)
		return
	}
	if len(prolist) == 0 {
		response.Response(c, 200, "未查询到数据", rspobj)
		return
	}
	updateRequest := models.UpdatePropertyRequest{
		TenantID:     models.ToStringPtr(reqobj.TenantID),
		PropertyName: models.ToStringPtr(reqobj.PropertyName),
		PropertyDesc: models.ToStringPtr(reqobj.PropertyDesc),
		ParaDef:      models.ToJsonPtr(reqobj.ParaDef),
		OntoSlotID:   models.StrToInt64Ptr(reqobj.OntoSlotID),
		PropertyType: models.StrToInt64Ptr(reqobj.PropertyType),
		Status:       models.StrToInt(reqobj.Status),
	}
	uperr := dslpro.UpdatePropertyByID(intpid, updateRequest)
	if uperr != nil {
		response.Response(c, 500, "数据库更新操作失败", uperr)
		return
	}
	response.Response(c, 200, "success", rspobj)
	return
}

package rule_maintain

import (
	"encoding/json"
	"fmt"
	apitypes "kmbservice/api/types"
	"kmbservice/repository/models"
	"strconv"
	"testing"
)

func TestRuleCreateNew(t *testing.T) {
	//测试条件拼接
	jsonData := `{"status":"1","rule_name":"1","rule_desc":"2","rule_particulars":["qqqqqqq"],"rule_body":{"operator":"&&","detail":[{"detail":[{"object":"MedicalServices","property":"ExistsService","comparison":"＞","value":"1"}]}]},"rule_result":{"content":"1","code":"04"}}`
	var reqobj apitypes.RuleCreateNewRequest
	var rspobj apitypes.RuleCreateNewResponse

	err := json.Unmarshal([]byte(jsonData), &reqobj)
	if err != nil {
		fmt.Println("Error parsing JSON:", err)
		return
	}

	ruledetailmodel := models.RuleContentDetail{}
	ruledetail_obj_mapping := models.RuleContentDetailObjectMapping{}

	//处理请求参数结构并保存
	reqJsonData, err := json.MarshalIndent(reqobj, "", "    ")
	if err != nil {
		fmt.Println("Failed to marshal JSON:", err)
		return
	}
	//处理规则首行
	rule_header := fmt.Sprintf(`rule %s`, reqobj.RuleName)
	//处理规则主体
	rulebody := buildConditionString(reqobj.RuleBody)
	//处理警示信息
	rule_else := fmt.Sprintf(`{ %s.%s(%s,%s,%s)}`, reqobj.RuleResult.Noumenon, reqobj.RuleResult.Method,
		reqobj.RuleResult.Code, reqobj.RuleResult.Message, reqobj.RuleResult.Content)
	//合体!
	finalRule := fmt.Sprintf(`%s 
begin
    if %s {
        %s
    }
end`, rule_header, rulebody, rule_else)
	//保存处理
	//开启事务
	db := models.GetDb()
	tx := db.Begin()
	// 检查事务是否启动成功
	if tx.Error != nil {
		fmt.Println(tx.Error)
		return
	}
	//提示语
	p := reqobj.RuleResult.Content
	//提示类型
	w := reqobj.RuleResult.Code
	//处理状态符
	intstatus, _ := strconv.ParseInt(reqobj.Status, 10, 64)
	//将拼接好的单条规则存入数据库中
	ruleId, detailVersionId, err := ruledetailmodel.CreateRuleDetailTx(tx, reqobj.RuleName, finalRule, reqobj.RuleDesc, finalRule, p, w, reqJsonData, intstatus)
	if err != nil {
		tx.Rollback()
		fmt.Println(err)
		return
	}
	if ruleId == 0 {
		fmt.Println(err)
		return
	}
	//将规则与对象映射关系插入映射表中
	errobj := ruledetail_obj_mapping.CreateMappingsTx(tx, ruleId, reqobj.RuleClassficationID)
	if errobj != nil {
		tx.Rollback()
		fmt.Println(err)
		return
	}
	// 提交事务
	if errtx := tx.Commit().Error; errtx != nil {
		fmt.Println(err)
		return
	}
	//返回参数处理
	ruleIdStr := strconv.FormatInt(ruleId, 10)
	rspobj.RuleID = ruleIdStr
	detailVersionIdStr := strconv.FormatInt(detailVersionId, 10)
	rspobj.DetailVersionId = detailVersionIdStr
	rspobj.RuleDesc = reqobj.RuleDesc
	rspobj.RuleContent = finalRule
	rspobj.RuleCname = string(reqJsonData)
	fmt.Println(rspobj)
	return
}

package rule_maintain

import (
	"fmt"
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"sort"
	"strconv"
	"strings"
)

func Rulesundelete(c *gin.Context) {
	var (
		reqobj      apitypes.RulesundeleteRequest
		mainVersion int64
		subVersion  int64
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 200, "【1001】入参错误", err)
		return
	}
	rulemodel := models.NewRuleContent{}
	ruledetailmodel := models.RuleContentDetail{}
	rulemappingmodel := models.RuleContentMapping{}
	//查询当前规则组信息
	rulegroupid, _ := strconv.ParseInt(reqobj.RuleID, 10, 64)
	result, _ := rulemodel.FindByIDAndIsUse(rulegroupid, 1)
	mainVersion = result.MainVersion
	subVersion = result.SubVersion
	if result == nil {
		response.Response(c, 200, "2001", "未查询到结果")
		return
	}
	//查询当前规则组映射的 detail_id
	mappingmodel, _ := rulemappingmodel.FindByIDAndAllVersion(rulegroupid, mainVersion, subVersion)
	//按照优先级排序
	sort.Slice(mappingmodel, func(i, j int) bool {
		return mappingmodel[i].SerialNumber < mappingmodel[j].SerialNumber
	})
	var intdetailidSlice []int64
	if len(mappingmodel) != 0 {
		for _, str := range mappingmodel {
			intdetailidSlice = append(intdetailidSlice, str.RuleContentDetailID)
		}
	}
	remove, _ := strconv.ParseInt(reqobj.RuleDetailid, 10, 64)
	updatedSlice := removeIntFromSlice(intdetailidSlice, remove)
	detaillist, err := ruledetailmodel.FindActiveDetailsByIDs(updatedSlice, "")
	if err != nil {
		response.Response(c, 200, "【3001】网络故障", err)
		return
	}
	//mainVersion += 1
	subVersion += 1
	if len(detaillist) == 0 {
		db := models.GetDb()
		tx := db.Begin()
		// 检查事务是否启动成功
		if tx.Error != nil {
			response.Response(c, 200, "【4001】事务启动失败", tx.Error)
			return
		}
		//将之前正在使用的版本状态改为不可用
		errup := rulemodel.UpdateIsUseToZeroByruleID(tx, rulegroupid)
		if errup != nil {
			tx.Rollback()
		}
		//将打包好的规则保存
		_, errc := rulemodel.CreateRuleGroupTx(tx, rulegroupid, mainVersion, subVersion, result.RuleName, result.RuleCode, result.RuleDescription, "", result.UsageScenario, fmt.Sprintf("%d", result.CategoryTreeID), fmt.Sprintf("%d", result.CategoryId), fmt.Sprintf("%d", result.Status))
		if errc != nil {
			tx.Rollback()
			response.Response(c, 200, "【3001】网络故障", errc)
			return
		}
		// 提交事务
		if errtx := tx.Commit().Error; errtx != nil {
			response.Response(c, 200, "【4002】事务提交失败", errtx)
			return
		}
		response.Response(c, 200, "成功", "")
		return
	}
	//拼接规则内容
	var rulelists []string
	var mappingmodellist []models.RuleContentMapping
	for i, v := range detaillist {
		var mappingmodel models.RuleContentMapping
		mappingmodel.RuleGroupID = rulegroupid
		mappingmodel.RuleContentDetailID = v.DetailID
		mappingmodel.RuleGroupName = result.RuleName
		mappingmodel.RuleContentDetailName = v.RuleName
		mappingmodel.RuleMainVersion = mainVersion
		mappingmodel.RuleSubVersion = subVersion
		mappingmodel.RuleDetailMainVersion = v.MainVersion
		mappingmodel.RuleDetailSubVersion = v.SubVersion
		mappingmodel.SerialNumber = int64(i + 1)
		mappingmodellist = append(mappingmodellist, mappingmodel)
		rulelists = append(rulelists, v.RuleContent)
	}
	//给规则数据加上优先级
	for i, rule := range rulelists {
		firstLineEnd := strings.Index(rule, "\n")
		if firstLineEnd == -1 {
			firstLineEnd = len(rule)
		}
		numberToAdd := i + 1
		numberString := strconv.Itoa(numberToAdd)
		ruleWithNumber := rule[:firstLineEnd] + numberString + rule[firstLineEnd:]
		rulelists[i] = ruleWithNumber
	}
	//拼接打包
	packedRules := strings.Join(rulelists, "\n\n")
	//开启事务
	db := models.GetDb()
	tx := db.Begin()
	// 检查事务是否启动成功
	if tx.Error != nil {
		response.Response(c, 200, "【4001】事务启动失败", tx.Error)
		return
	}
	//将之前正在使用的版本状态改为不可用
	errup := rulemodel.UpdateIsUseToZeroByruleID(tx, rulegroupid)
	if errup != nil {
		tx.Rollback()
	}
	//向映射表中插入需要添加的映射关系
	err1 := rulemappingmodel.BatchInsertRuleContentMappingsTx(tx, mappingmodellist)
	if err1 != nil {
		tx.Rollback()
		response.Response(c, 200, "【3001】网络故障", err1)
		return
	}
	//将打包好的规则保存
	_, errc := rulemodel.CreateRuleGroupTx(tx, rulegroupid, mainVersion, subVersion, result.RuleName, result.RuleCode, result.RuleDescription, packedRules, result.UsageScenario, fmt.Sprintf("%d", result.CategoryTreeID), fmt.Sprintf("%d", result.CategoryId), fmt.Sprintf("%d", result.Status))
	if errc != nil {
		tx.Rollback()
		response.Response(c, 200, "【3001】网络故障", errc)
		return
	}
	// 提交事务
	if errtx := tx.Commit().Error; errtx != nil {
		response.Response(c, 200, "【4002】事务提交失败", errtx)
		return
	}
	response.Response(c, 200, "成功", "")
	return
}

func removeIntFromSlice(slice []int64, removeInt int64) []int64 {
	index := -1
	for i, v := range slice {
		if v == removeInt {
			index = i
			break
		}
	}
	if index != -1 {
		return append(slice[:index], slice[index+1:]...)
	}
	return slice
}

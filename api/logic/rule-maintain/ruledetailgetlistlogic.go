package rule_maintain

import (
	"fmt"
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"math"
	"strconv"
	"time"
)

func GetDetailList(c *gin.Context) {
	var (
		reqobj apitypes.GetDetailListRequest
		rspobj apitypes.GetDetailListResponse
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 400, "入参错误", err)
		return
	}
	ruledetail := models.RuleContentDetail{}
	mappingmodel := models.RuleContentDetailObjectMapping{}
	var detaillist []apitypes.Rule_detail_content
	// 分页入参处理
	var offset, limit int
	if reqobj.Limit != 0 {
		limit = reqobj.Limit
	} else {
		limit = 50
	}
	if reqobj.Page != 0 {
		offset = (reqobj.Page - 1) * reqobj.Limit
	} else {
		offset = 0
	}
	var intSlice []int64
	//查询对象id对应的详细规则
	if len(reqobj.RuleObj) != 0 {
		// 遍历原始的字符串数组，并将每个字符串转换为int64类型
		for _, str := range reqobj.RuleObj {
			num, err := strconv.ParseInt(str, 10, 64) // 将字符串转换为int64
			if err != nil {
				fmt.Printf("转换错误: %v\n", err)
				continue
			}
			intSlice = append(intSlice, num) // 添加转换后的int64到切片中
		}
		detailfilterid, _ := mappingmodel.GetDetailIDsByObjectIDsAND(intSlice)
		if len(detailfilterid) == 0 {
			rspobj.ResultDetailList = []apitypes.Rule_detail_content{}
			response.Response(c, 200, "未查询到数据", rspobj)
			return
		}
		//
		var excludeDetailIds []int64
		if len(reqobj.DetailList) != 0 {
			for _, str := range reqobj.DetailList {
				num, err := strconv.ParseInt(str, 10, 64) // 将字符串转换为int64
				if err != nil {
					fmt.Printf("转换错误: %v\n", err)
					continue
				}
				excludeDetailIds = append(excludeDetailIds, num) // 添加转换后的int64到切片中
			}
		}
		//获取除页数限制条件的数据数量
		listNolimit, err := ruledetail.QueryRuleDetailsThIfUse(reqobj.RuleName, reqobj.StartDate, reqobj.EndDate, detailfilterid, excludeDetailIds, reqobj.Status, reqobj.PublishingStatus, reqobj.IsUse)
		count := len(listNolimit)
		list, err := ruledetail.QueryRuleDetailsSeIfUse(reqobj.RuleName, reqobj.StartDate, reqobj.EndDate, detailfilterid, excludeDetailIds, offset, limit, reqobj.Status, reqobj.PublishingStatus, reqobj.IsUse)
		if err != nil {
			response.Response(c, 500, "网络故障", err)
			return
		}
		if len(list) == 0 {
			rspobj.ResultDetailList = []apitypes.Rule_detail_content{}
			response.Response(c, 201, "未查询到数据", rspobj)
			return
		}

		for _, v := range list {
			var detail apitypes.Rule_detail_content
			detail.RuleName = v.RuleName
			detail.RuleDesc = v.RuleDescription
			detail.Version = fmt.Sprintf("%d", v.MainVersion)
			t := time.Unix(v.UpdatedAt, 0)
			// 格式化为年月日时分秒格式
			formattedTime := t.Format("2006-01-02 15:04:05")
			detail.Updated = formattedTime
			detail.Status = strconv.FormatInt(v.Status, 10)
			detail.DetailID = strconv.FormatInt(v.DetailID, 10)
			detail.DetailVersionID = strconv.FormatInt(v.DetailVersionID, 10)
			detail.Using = v.ISUse
			detail.RuleDetailContent = v.RuleContent
			detail.PublishingStatus = v.PublishingStatus
			detail.ClassficationID = fmt.Sprintf("%d", v.ClassficationID)
			detaillist = append(detaillist, detail)
		}
		rspobj.ResultDetailList = detaillist
		numberFloat64 := math.Ceil(float64(count) / float64(limit))
		number := int(numberFloat64)
		rspobj.Count = count
		rspobj.Limit = limit
		rspobj.Page = reqobj.Page
		rspobj.Number = number

		response.Response(c, 200, "成功", rspobj)
		return
	}
	var excludeDetailIds []int64
	if len(reqobj.DetailList) != 0 {
		for _, str := range reqobj.DetailList {
			num, err := strconv.ParseInt(str, 10, 64) // 将字符串转换为int64
			if err != nil {
				fmt.Printf("转换错误: %v\n", err)
				continue
			}
			excludeDetailIds = append(excludeDetailIds, num) // 添加转换后的int64到切片中
		}
	}
	listNolimit, err := ruledetail.QueryRuleDetailsFoIfUse(reqobj.RuleName, reqobj.StartDate, reqobj.EndDate, reqobj.Status, reqobj.IsUse, excludeDetailIds)
	countNoobject := len(listNolimit)
	list, err := ruledetail.QueryRuleDetailsIfUse(reqobj.RuleName, reqobj.StartDate, reqobj.EndDate, reqobj.Status, reqobj.IsUse, excludeDetailIds, offset, limit)
	if err != nil {
		response.Response(c, 500, "网络故障", err)
		return
	}
	if len(list) == 0 {
		rspobj.ResultDetailList = []apitypes.Rule_detail_content{}
		response.Response(c, 201, "未查询到数据", rspobj)
		return
	}
	//按照更新时间倒排序
	//sort.Slice(list, func(i, j int) bool {
	//	return list[i].UpdatedAt > list[j].UpdatedAt
	//})
	//sort.Slice(list, func(i, j int) bool {
	//	if list[i].ISUse != list[j].ISUse {
	//		return list[i].ISUse > list[j].ISUse
	//	}
	//	if list[i].MainVersion != list[j].MainVersion {
	//		return list[i].MainVersion > list[j].MainVersion
	//	}
	//	return list[i].UpdatedAt > list[j].UpdatedAt
	//})
	//sort.Slice(list, func(i, j int) bool {
	//	return list[i].DetailID > list[j].DetailID
	//})
	for _, v := range list {
		var detail apitypes.Rule_detail_content
		detail.RuleName = v.RuleName
		detail.RuleDesc = v.RuleDescription
		detail.Version = fmt.Sprintf("%d", v.MainVersion)
		t := time.Unix(v.UpdatedAt, 0)
		// 格式化为年月日时分秒格式
		formattedTime := t.Format("2006-01-02 15:04:05")
		detail.Updated = formattedTime
		detail.Status = strconv.FormatInt(v.Status, 10)
		detail.DetailID = strconv.FormatInt(v.DetailID, 10)
		detail.DetailVersionID = strconv.FormatInt(v.DetailVersionID, 10)
		detail.Using = v.ISUse
		detail.RuleDetailContent = v.RuleContent
		detail.PublishingStatus = v.PublishingStatus
		detail.ClassficationID = fmt.Sprintf("%d", v.ClassficationID)
		detaillist = append(detaillist, detail)
	}
	//统计可用状态的数据总条数
	rspobj.ResultDetailList = detaillist
	numberFloat64 := math.Ceil(float64(countNoobject) / float64(limit))
	number := int(numberFloat64)
	rspobj.Count = countNoobject
	rspobj.Limit = limit
	rspobj.Page = reqobj.Page
	rspobj.Number = number

	response.Response(c, 200, "成功", rspobj)
	return
}

package rule_maintain

//
//import (
//	"fmt"
//	"github.com/gin-gonic/gin"
//	apitypes "kmbservice/api/types"
//	"kmbservice/tools/response"
//	"kmbservice/repository/models"
//	"math"
//	"sort"
//	"strconv"
//	"time"
//)
//
//func RuleGroupSelectable(c *gin.Context) {
//	var (
//		reqobj apitypes.RuleGroupSelectableRequest
//		rspobj apitypes.RuleGroupSelectableResponse
//	)
//	if err := c.ShouldBind(&reqobj); err != nil {
//		response.Response(c, 200, "【1001】入参错误", err)
//		return
//	}
//	ruledetailmodel := models.RuleContentDetail{}
//	rulemappingmodel := models.RuleContentMapping{}
//	objmappingmodel := models.RuleContentDetailObjectMapping{}
//
//	var offset, limit int
//	if reqobj.Limit != 0 {
//		limit = reqobj.Limit
//	} else {
//		limit = 50
//	}
//	if reqobj.Page != 0 {
//		offset = (reqobj.Page - 1) * reqobj.Limit
//	} else {
//		offset = 0
//	}
//
//	//入参为空
//	if reqobj.RuleID == "" && len(reqobj.DetailIDList) == 0 {
//		var detaillist []apitypes.Rule_detail_content
//		var intSlice []int64
//		var exlist []int64
//		//查询对象id对应的详细规则
//		if len(reqobj.RuleDetailObj) != 0 {
//			// 遍历原始的字符串数组，并将每个字符串转换为int64类型
//			for _, str := range reqobj.RuleDetailObj {
//				num, err := strconv.ParseInt(str, 10, 64) // 将字符串转换为int64
//				if err != nil {
//					fmt.Printf("转换错误: %v\n", err)
//					continue
//				}
//				intSlice = append(intSlice, num) // 添加转换后的int64到切片中
//			}
//
//			for _, str := range reqobj.DetailIDList {
//				num, err := strconv.ParseInt(str, 10, 64) // 将字符串转换为int64
//				if err != nil {
//					fmt.Printf("转换错误: %v\n", err)
//					continue
//				}
//				exlist = append(exlist, num) // 添加转换后的int64到切片中
//			}
//
//			//detailfilterid, errfliter := mappingmodel.GetDetailIDsByObjectIDs(intSlice)
//			detailfilterid, _ := objmappingmodel.GetDetailIDsByObjectIDsAND(intSlice)
//			if len(detailfilterid) == 0 {
//				response.Response(c, 200, "【2001】未查询到数据", "")
//				return
//			}
//			//获取除页数限制条件的数据数量
//			listNolimit, err := ruledetailmodel.QueryRuleDetailsThIfUseEX(reqobj.RuleDetailName, "", "", detailfilterid, exlist)
//			count := len(listNolimit)
//			list, err := ruledetailmodel.QueryRuleDetailsSeIfUseEX(reqobj.RuleDetailName, "", "", detailfilterid, exlist, offset, limit)
//			//按照更新时间倒排序
//			sort.Slice(list, func(i, j int) bool {
//				return list[i].UpdatedAt > list[j].UpdatedAt
//			})
//			if err != nil {
//				response.Response(c, 200, "【3001】网络故障", err)
//				return
//			}
//			if len(list) == 0 {
//				response.Response(c, 200, "【2001】未查询到数据", "")
//				return
//			}
//
//			for _, v := range list {
//				var detail apitypes.Rule_detail_content
//				detail.RuleName = v.RuleName
//				detail.RuleDesc = v.RuleDescription
//				strversion := strconv.FormatFloat(v.Version, 'f', -1, 64)
//				detail.Version = strversion
//				t := time.Unix(v.UpdatedAt, 0)
//				// 格式化为年月日时分秒格式
//				formattedTime := t.Format("2006-01-02 15:04:05")
//				detail.Updated = formattedTime
//				detail.Status = strconv.FormatInt(v.Status, 10)
//				detail.DetailID = strconv.FormatInt(v.DetailID, 10)
//				detail.DetailVersionID = strconv.FormatInt(v.DetailVersionID, 10)
//				detail.Using = v.ISUse
//				detail.RuleDetailContent = v.RuleContent
//				detail.ClassficationID = fmt.Sprintf("%d", v.ClassficationID)
//				detaillist = append(detaillist, detail)
//			}
//			var contentlist []apitypes.RuleDetailContent
//			for _, v := range detaillist {
//				contentd := convertToRuleDetailContent(v)
//				contentlist = append(contentlist, contentd)
//			}
//
//			rspobj.SelectableList = contentlist
//			numberFloat64 := math.Ceil(float64(count) / float64(limit))
//			number := int(numberFloat64)
//			rspobj.Count = count
//			rspobj.Limit = limit
//			rspobj.Page = reqobj.Page
//			rspobj.Number = number
//
//			response.Response(c, 200, "成功", rspobj)
//			return
//		}
//
//		listNolimit, err := ruledetailmodel.QueryRuleDetailsFoIfUse(reqobj.RuleDetailName, "", "", intSlice, 1)
//		countNoobject := len(listNolimit)
//		list, err := ruledetailmodel.QueryRuleDetailsIfUse(reqobj.RuleDetailName, "", "", intSlice, offset, limit, 1)
//		if err != nil {
//			response.Response(c, 200, "【3001】网络故障", err)
//			return
//		}
//		if len(list) == 0 {
//			response.Response(c, 200, "【2001】未查询到数据", "")
//			return
//		}
//		//按照更新时间倒排序
//		sort.Slice(list, func(i, j int) bool {
//			return list[i].UpdatedAt > list[j].UpdatedAt
//		})
//		for _, v := range list {
//			var detail apitypes.Rule_detail_content
//			detail.RuleName = v.RuleName
//			detail.RuleDesc = v.RuleDescription
//			strversion := strconv.FormatFloat(v.Version, 'f', -1, 64)
//			detail.Version = strversion
//			t := time.Unix(v.UpdatedAt, 0)
//			// 格式化为年月日时分秒格式
//			formattedTime := t.Format("2006-01-02 15:04:05")
//			detail.Updated = formattedTime
//			detail.Status = strconv.FormatInt(v.Status, 10)
//			detail.DetailID = strconv.FormatInt(v.DetailID, 10)
//			detail.DetailVersionID = strconv.FormatInt(v.DetailVersionID, 10)
//			detail.Using = v.ISUse
//			detail.RuleDetailContent = v.RuleContent
//			detail.ClassficationID = fmt.Sprintf("%d", v.ClassficationID)
//			detaillist = append(detaillist, detail)
//		}
//		var contentlist []apitypes.RuleDetailContent
//		for _, v := range detaillist {
//			contentd := convertToRuleDetailContent(v)
//			contentlist = append(contentlist, contentd)
//		}
//		rspobj.SelectableList = contentlist
//		numberFloat64 := math.Ceil(float64(countNoobject) / float64(limit))
//		number := int(numberFloat64)
//		rspobj.Count = countNoobject
//		rspobj.Limit = limit
//		rspobj.Page = reqobj.Page
//		rspobj.Number = number
//		response.Response(c, 200, "成功", rspobj)
//		return
//	}
//
//	if reqobj.RuleID == "" && len(reqobj.DetailIDList) != 0 {
//		var detaillist []apitypes.Rule_detail_content
//		var intSlice []int64
//		//查询对象id对应的详细规则
//		if len(reqobj.RuleDetailObj) != 0 {
//			// 遍历原始的字符串数组，并将每个字符串转换为int64类型
//			for _, str := range reqobj.RuleDetailObj {
//				num, err := strconv.ParseInt(str, 10, 64) // 将字符串转换为int64
//				if err != nil {
//					fmt.Printf("转换错误: %v\n", err)
//					continue
//				}
//				intSlice = append(intSlice, num) // 添加转换后的int64到切片中
//			}
//			//detailfilterid, errfliter := mappingmodel.GetDetailIDsByObjectIDs(intSlice)
//			detailfilterid, _ := objmappingmodel.GetDetailIDsByObjectIDsAND(intSlice)
//			if len(detailfilterid) == 0 {
//				response.Response(c, 200, "【2001】未查询到数据", "")
//				return
//			}
//			//获取除页数限制条件的数据数量
//			listNolimit, err := ruledetailmodel.QueryRuleDetailsThIfUse(reqobj.RuleDetailName, "", "", detailfilterid, detailfilterid, 1)
//			count := len(listNolimit)
//			list, err := ruledetailmodel.QueryRuleDetailsSeIfUse(reqobj.RuleDetailName, "", "", detailfilterid, detailfilterid, offset, limit, 1)
//			//按照更新时间倒排序
//			sort.Slice(list, func(i, j int) bool {
//				return list[i].UpdatedAt > list[j].UpdatedAt
//			})
//			if err != nil {
//				response.Response(c, 200, "【3001】网络故障", err)
//				return
//			}
//			if len(list) == 0 {
//				response.Response(c, 200, "【2001】未查询到数据", "")
//				return
//			}
//
//			for _, v := range list {
//				var detail apitypes.Rule_detail_content
//				detail.RuleName = v.RuleName
//				detail.RuleDesc = v.RuleDescription
//				strversion := strconv.FormatFloat(v.Version, 'f', -1, 64)
//				detail.Version = strversion
//				t := time.Unix(v.UpdatedAt, 0)
//				// 格式化为年月日时分秒格式
//				formattedTime := t.Format("2006-01-02 15:04:05")
//				detail.Updated = formattedTime
//				detail.Status = strconv.FormatInt(v.Status, 10)
//				detail.DetailID = strconv.FormatInt(v.DetailID, 10)
//				detail.DetailVersionID = strconv.FormatInt(v.DetailVersionID, 10)
//				detail.Using = v.ISUse
//				detail.RuleDetailContent = v.RuleContent
//				detail.ClassficationID = fmt.Sprintf("%d", v.ClassficationID)
//				detaillist = append(detaillist, detail)
//			}
//			var contentlist []apitypes.RuleDetailContent
//			for _, v := range detaillist {
//				contentd := convertToRuleDetailContent(v)
//				contentlist = append(contentlist, contentd)
//			}
//
//			rspobj.SelectableList = contentlist
//			numberFloat64 := math.Ceil(float64(count) / float64(limit))
//			number := int(numberFloat64)
//			rspobj.Count = count
//			rspobj.Limit = limit
//			rspobj.Page = reqobj.Page
//			rspobj.Number = number
//
//			response.Response(c, 200, "成功", rspobj)
//			return
//		}
//
//		listNolimit, err := ruledetailmodel.QueryRuleDetailsFoIfUse(reqobj.RuleDetailName, "", "", intSlice, 1)
//		countNoobject := len(listNolimit)
//		list, err := ruledetailmodel.QueryRuleDetailsIfUse(reqobj.RuleDetailName, "", "", intSlice, offset, limit, 1)
//		if err != nil {
//			response.Response(c, 200, "【3001】网络故障", err)
//			return
//		}
//		if len(list) == 0 {
//			response.Response(c, 200, "【2001】未查询到数据", "")
//			return
//		}
//		//按照更新时间倒排序
//		sort.Slice(list, func(i, j int) bool {
//			return list[i].UpdatedAt > list[j].UpdatedAt
//		})
//		for _, v := range list {
//			var detail apitypes.Rule_detail_content
//			detail.RuleName = v.RuleName
//			detail.RuleDesc = v.RuleDescription
//			strversion := strconv.FormatFloat(v.Version, 'f', -1, 64)
//			detail.Version = strversion
//			t := time.Unix(v.UpdatedAt, 0)
//			// 格式化为年月日时分秒格式
//			formattedTime := t.Format("2006-01-02 15:04:05")
//			detail.Updated = formattedTime
//			detail.Status = strconv.FormatInt(v.Status, 10)
//			detail.DetailID = strconv.FormatInt(v.DetailID, 10)
//			detail.DetailVersionID = strconv.FormatInt(v.DetailVersionID, 10)
//			detail.Using = v.ISUse
//			detail.RuleDetailContent = v.RuleContent
//			detail.ClassficationID = fmt.Sprintf("%d", v.ClassficationID)
//			detaillist = append(detaillist, detail)
//		}
//		var contentlist []apitypes.RuleDetailContent
//		for _, v := range detaillist {
//			contentd := convertToRuleDetailContent(v)
//			contentlist = append(contentlist, contentd)
//		}
//		rspobj.SelectableList = contentlist
//		numberFloat64 := math.Ceil(float64(countNoobject) / float64(limit))
//		number := int(numberFloat64)
//		rspobj.Count = countNoobject
//		rspobj.Limit = limit
//		rspobj.Page = reqobj.Page
//		rspobj.Number = number
//		response.Response(c, 200, "成功", rspobj)
//		return
//	}
//
//	//查出符合对象的id
//	var detailfilterid []int64
//	if len(reqobj.RuleDetailObj) != 0 {
//		var objdetailIdList []int64
//		// 遍历原始的字符串数组，并将每个字符串转换为int64类型
//		for _, str := range reqobj.RuleDetailObj {
//			num, err := strconv.ParseInt(str, 10, 64) // 将字符串转换为int64
//			if err != nil {
//				fmt.Printf("转换错误: %v\n", err)
//				continue
//			}
//			objdetailIdList = append(objdetailIdList, num) // 添加转换后的int64到切片中
//		}
//		detailfilterid, _ = objmappingmodel.GetDetailIDsByObjectIDsAND(objdetailIdList)
//		if len(detailfilterid) == 0 {
//			response.Response(c, 200, "【2001】未查询到数据", "")
//			return
//		}
//	}
//
//	if len(reqobj.DetailIDList) == 0 {
//		var detailIdList []int64
//		fRuleVersion, _ := strconv.ParseFloat(reqobj.RuleVersion, 64)
//		intrueid, _ := strconv.ParseInt(reqobj.RuleID, 10, 64)
//		mbody, err := rulemappingmodel.FindByIDAndVersion(intrueid, fRuleVersion)
//		if mbody == nil {
//			response.Response(c, 200, "【1002】映射查询失败", err)
//			return
//		}
//		for _, v := range mbody {
//			detailIdList = append(detailIdList, v.RuleContentDetailID)
//		}
//		//已经选择的数据
//		var templist []apitypes.RuleDetailContent
//		selectedList, _ := ruledetailmodel.FindActiveDetailsByIDs(detailIdList, "")
//		if len(selectedList) > 0 {
//			for _, v := range selectedList {
//				var temp apitypes.RuleDetailContent
//				temp.DetailID = fmt.Sprintf("%d", v.DetailID)
//				temp.DetailVersionID = fmt.Sprintf("%d", v.DetailVersionID)
//				temp.RuleName = v.RuleName
//				temp.RuleDescription = v.RuleDescription
//				temp.Version = strconv.FormatFloat(v.Version, 'f', -1, 64)
//				temp.Status = fmt.Sprintf("%d", v.Status)
//				templist = append(templist, temp)
//			}
//		}
//		rspobj.SelectedList = templist
//		//可选数据
//		//如果对象id不为空
//		if len(reqobj.RuleDetailObj) != 0 {
//			//符合对象筛选要求的id不在已选列表中即可
//			//outlist := filterOut(detailfilterid, detailIdList)
//			outlist := filterOut(detailIdList, detailfilterid)
//			selectableListCount := ruledetailmodel.ExcludeAndFilterByStatus(outlist, reqobj.RuleDetailName)
//			count := len(selectableListCount)
//			selectableList := ruledetailmodel.ExcludeAndFilterByStatusLimit(outlist, reqobj.RuleDetailName, offset, limit)
//			var selectabletemplist []apitypes.RuleDetailContent
//			if len(selectableList) > 0 {
//				for _, v := range selectableList {
//					var selectabletemp apitypes.RuleDetailContent
//					selectabletemp.DetailID = fmt.Sprintf("%d", v.DetailID)
//					selectabletemp.DetailVersionID = fmt.Sprintf("%d", v.DetailVersionID)
//					selectabletemp.RuleName = v.RuleName
//					selectabletemp.RuleDescription = v.RuleDescription
//					selectabletemp.Version = strconv.FormatFloat(v.Version, 'f', -1, 64)
//					selectabletemp.Status = fmt.Sprintf("%d", v.Status)
//					selectabletemplist = append(selectabletemplist, selectabletemp)
//				}
//			}
//			numberFloat64 := math.Ceil(float64(count) / float64(limit))
//			number := int(numberFloat64)
//			rspobj.Count = count
//			rspobj.Limit = limit
//			rspobj.Page = reqobj.Page
//			rspobj.Number = number
//			rspobj.SelectableList = selectabletemplist
//			response.Response(c, 200, "success", rspobj)
//			return
//		}
//		selectableListCount := ruledetailmodel.ExcludeAndFilterByStatus(detailIdList, reqobj.RuleDetailName)
//		count := len(selectableListCount)
//		selectableList := ruledetailmodel.ExcludeAndFilterByStatusLimit(detailIdList, reqobj.RuleDetailName, offset, limit)
//		var selectabletemplist []apitypes.RuleDetailContent
//		if len(selectableList) > 0 {
//			for _, v := range selectableList {
//				var selectabletemp apitypes.RuleDetailContent
//				selectabletemp.DetailID = fmt.Sprintf("%d", v.DetailID)
//				selectabletemp.DetailVersionID = fmt.Sprintf("%d", v.DetailVersionID)
//				selectabletemp.RuleName = v.RuleName
//				selectabletemp.RuleDescription = v.RuleDescription
//				selectabletemp.Version = strconv.FormatFloat(v.Version, 'f', -1, 64)
//				selectabletemp.Status = fmt.Sprintf("%d", v.Status)
//				selectabletemplist = append(selectabletemplist, selectabletemp)
//			}
//		}
//		numberFloat64 := math.Ceil(float64(count) / float64(limit))
//		number := int(numberFloat64)
//		rspobj.Count = count
//		rspobj.Limit = limit
//		rspobj.Page = reqobj.Page
//		rspobj.Number = number
//		rspobj.SelectableList = selectabletemplist
//		response.Response(c, 200, "success", rspobj)
//		return
//	}
//
//	//无规则数据入参的可选列表
//	var detailIdList []int64
//	for _, str := range reqobj.DetailIDList {
//		num, err := strconv.ParseInt(str, 10, 64)
//		if err != nil {
//			fmt.Printf("Failed to parse %s: %v\n", str, err)
//			continue
//		}
//		detailIdList = append(detailIdList, num)
//	}
//	//已经选择的数据
//	var templist []apitypes.RuleDetailContent
//	selectedList, _ := ruledetailmodel.FindActiveDetailsByIDs(detailIdList, "")
//	if len(selectedList) > 0 {
//		for _, v := range selectedList {
//			var temp apitypes.RuleDetailContent
//			temp.DetailID = fmt.Sprintf("%d", v.DetailID)
//			temp.DetailVersionID = fmt.Sprintf("%d", v.DetailVersionID)
//			temp.RuleName = v.RuleName
//			temp.RuleDescription = v.RuleDescription
//			temp.Version = strconv.FormatFloat(v.Version, 'f', -1, 64)
//			temp.Status = fmt.Sprintf("%d", v.Status)
//			templist = append(templist, temp)
//		}
//	}
//	rspobj.SelectedList = templist
//
//	if len(reqobj.RuleDetailObj) != 0 {
//		outlist := filterOut(detailIdList, detailfilterid)
//		selectableListCount := ruledetailmodel.ExcludeAndFilterByStatus(outlist, reqobj.RuleDetailName)
//		count := len(selectableListCount)
//		selectableList := ruledetailmodel.ExcludeAndFilterByStatusLimit(outlist, reqobj.RuleDetailName, offset, limit)
//		var selectabletemplist []apitypes.RuleDetailContent
//		if len(selectableList) > 0 {
//			for _, v := range selectableList {
//				var selectabletemp apitypes.RuleDetailContent
//				selectabletemp.DetailID = fmt.Sprintf("%d", v.DetailID)
//				selectabletemp.DetailVersionID = fmt.Sprintf("%d", v.DetailVersionID)
//				selectabletemp.RuleName = v.RuleName
//				selectabletemp.RuleDescription = v.RuleDescription
//				selectabletemp.Version = strconv.FormatFloat(v.Version, 'f', -1, 64)
//				selectabletemp.Status = fmt.Sprintf("%d", v.Status)
//				selectabletemplist = append(selectabletemplist, selectabletemp)
//			}
//		}
//		numberFloat64 := math.Ceil(float64(count) / float64(limit))
//		number := int(numberFloat64)
//		rspobj.Count = count
//		rspobj.Limit = limit
//		rspobj.Page = reqobj.Page
//		rspobj.Number = number
//		rspobj.SelectableList = selectabletemplist
//		response.Response(c, 200, "success", rspobj)
//		return
//	}
//	selectableListCount := ruledetailmodel.ExcludeAndFilterByStatus(detailIdList, reqobj.RuleDetailName)
//	count := len(selectableListCount)
//	selectableList := ruledetailmodel.ExcludeAndFilterByStatusLimit(detailIdList, reqobj.RuleDetailName, offset, limit)
//	var selectabletemplist []apitypes.RuleDetailContent
//	if len(selectableList) > 0 {
//		for _, v := range selectableList {
//			var selectabletemp apitypes.RuleDetailContent
//			selectabletemp.DetailID = fmt.Sprintf("%d", v.DetailID)
//			selectabletemp.DetailVersionID = fmt.Sprintf("%d", v.DetailVersionID)
//			selectabletemp.RuleName = v.RuleName
//			selectabletemp.RuleDescription = v.RuleDescription
//			selectabletemp.Version = strconv.FormatFloat(v.Version, 'f', -1, 64)
//			selectabletemp.Status = fmt.Sprintf("%d", v.Status)
//			selectabletemplist = append(selectabletemplist, selectabletemp)
//		}
//	}
//	numberFloat64 := math.Ceil(float64(count) / float64(limit))
//	number := int(numberFloat64)
//	rspobj.Count = count
//	rspobj.Limit = limit
//	rspobj.Page = reqobj.Page
//	rspobj.Number = number
//	rspobj.SelectableList = selectabletemplist
//	response.Response(c, 200, "success", rspobj)
//	return
//}
//
//func filterOut(sliceA []int64, sliceB []int64) []int64 {
//	bMap := make(map[int64]bool)
//	for _, v := range sliceB {
//		bMap[v] = true
//	}
//	filtered := []int64{}
//	for _, v := range sliceA {
//		if !bMap[v] {
//			filtered = append(filtered, v)
//		}
//	}
//	return filtered
//}
//
//func contains(slice []int64, value int64) bool {
//	for _, v := range slice {
//		if v == value {
//			return true
//		}
//	}
//	return false
//}
//
//func convertToRuleDetailContent(detail apitypes.Rule_detail_content) apitypes.RuleDetailContent {
//	return apitypes.RuleDetailContent{
//		DetailID:        detail.DetailID,
//		DetailVersionID: detail.DetailVersionID,
//		RuleName:        detail.RuleName,
//		RuleDescription: detail.RuleDesc,
//		Version:         detail.Version,
//		Status:          detail.Status,
//	}
//}

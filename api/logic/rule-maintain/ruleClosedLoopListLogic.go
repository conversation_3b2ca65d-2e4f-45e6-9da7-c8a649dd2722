package rule_maintain

import (
	"fmt"
	"kmbservice/api/types"
	"kmbservice/repository/models"
	"math"
	"sort"
	"strconv"
)

func RuleClosedLoopList(req types.RuleClosedLoopListRequest) (rsp types.RuleClosedLoopListResponse, err error) {
	rulemodel := models.NewRuleContent{}
	// 分页入参处理
	var offset, limit int
	if req.Limit != 0 {
		limit = req.Limit
	} else {
		limit = 50
	}
	if req.Page != 0 {
		offset = (req.Page - 1) * req.Limit
	} else {
		offset = 0
	}
	var (
		rulecontent []models.NewRuleContent
		count       int
	)
	intstatus, _ := strconv.ParseInt(req.Status, 10, 64)
	if req.RuleName != "" && req.Status == "" {
		countrulecontent, _ := rulemodel.CLQueryRuleByName(req.RuleName)
		count = len(countrulecontent)
		rulecontent, _ = rulemodel.CLQueryRuleByNameLimit(req.RuleName, offset, limit)
	}
	if req.RuleName == "" && req.Status != "" {
		countrulecontent, _ := rulemodel.FindByStatus(1, intstatus)
		count = len(countrulecontent)
		rulecontent, _ = rulemodel.FindByStatusLimit(1, intstatus, offset, limit)
	}
	if req.RuleName != "" && req.Status != "" {
		countrulecontent, _ := rulemodel.CLQueryRuleByNameOrDesc(req.RuleName, intstatus)
		count = len(countrulecontent)
		rulecontent, _ = rulemodel.CLQueryRuleByNameOrDescSe(req.RuleName, intstatus, offset, limit)
	}
	if req.RuleName == "" && req.Status == "" {
		countrulecontent, _ := rulemodel.FindByIsUse(1)
		count = len(countrulecontent)
		rulecontent, _ = rulemodel.FindByIsUseLimit(1, offset, limit)
	}

	sort.Slice(rulecontent, func(i, j int) bool {
		return rulecontent[i].RuleCode > rulecontent[j].RuleCode
	})
	var grouplist []types.RuleClosedLoopListContent

	for _, v := range rulecontent {
		if v.IsDeleted == 1 {
			continue
		}
		var group types.RuleClosedLoopListContent
		group.RuleCode = v.RuleCode
		group.RuleName = v.RuleName
		group.RuleID = fmt.Sprintf("%d", v.ID)
		group.RuleVersionID = fmt.Sprintf("%d", v.RuleVersionID)
		group.RuleDesc = v.RuleDescription
		group.Status = strconv.FormatInt(v.Status, 10)
		grouplist = append(grouplist, group)
	}
	rsp.GroupList = grouplist
	numberFloat64 := math.Ceil(float64(count) / float64(limit))
	number := int(numberFloat64)
	rsp.Count = count
	rsp.Limit = limit
	rsp.Page = req.Page
	rsp.Number = number
	return rsp, err
}

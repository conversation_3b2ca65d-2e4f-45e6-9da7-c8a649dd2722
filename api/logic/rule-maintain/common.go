package rule_maintain

import (
	"fmt"
	apitypes "kmbservice/api/types"
	"math"
	"strconv"
	"strings"
)

type Version struct {
	MainVersion int64
	SubVersion  int64
}

func SplitFloat(s string) (*Version, error) {
	parts := strings.Split(s, ".")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid format")
	}
	intPart, err := strconv.ParseInt(parts[0], 10, 64)
	if err != nil {
		return nil, err
	}
	fracPart, err := strconv.ParseInt(parts[1], 10, 64)
	if err != nil {
		return nil, err
	}
	return &Version{intPart, fracPart}, nil
}

func RemoveIntFromSlice(slice []int64, removeInt int64) []int64 {
	index := -1
	for i, v := range slice {
		if v == removeInt {
			index = i
			break
		}
	}
	if index != -1 {
		return append(slice[:index], slice[index+1:]...)
	}
	return slice
}

func IntEqual(a, b []int64) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

func AddQuotes(strs []string) []string {
	quotedStrs := make([]string, len(strs))
	for i, str := range strs {
		quotedStrs[i] = `"` + str + `"`
	}
	return quotedStrs
}

func buildConditionString(cond apitypes.Condition) string {
	//对值的类型进行判断
	switch cond.Value.(type) {
	case int64:
		if cond.Object != "" && cond.Property != "" && cond.Comparison != "" && cond.Value != 0 {
			s, ok := cond.Value.(int64)
			if !ok {
				fmt.Println("类型转换失败")
				return ""
			}
			return fmt.Sprintf("%s.%s%s%d", cond.Object, cond.Property, cond.Comparison, s)
		} else if cond.Object != "" && cond.Property != "" && cond.Comparison == "" && cond.Value != "" {
			return fmt.Sprintf("%s.%s(%s)", cond.Object, cond.Property, cond.Value)
		}
		if len(cond.Detail) > 0 {
			var parts []string
			for _, detail := range cond.Detail {
				part := buildConditionString(detail)
				parts = append(parts, part)
			}
			return fmt.Sprintf("(%s)", strings.Join(parts, fmt.Sprintf(" %s ", cond.Operator)))
		}
		return ""
	case float64:
		if cond.Object != "" && cond.Property != "" && cond.Comparison != "" && cond.Value != 0 {
			s, ok := cond.Value.(float64)
			if !ok {
				fmt.Println("类型转换失败")
				return ""
			}
			return fmt.Sprintf("%s.%s%s%f", cond.Object, cond.Property, cond.Comparison, s)
		} else if cond.Object != "" && cond.Property != "" && cond.Comparison == "" && cond.Value != "" {
			return fmt.Sprintf("%s.%s(%s)", cond.Object, cond.Property, cond.Value)
		}
		if len(cond.Detail) > 0 {
			var parts []string
			for _, detail := range cond.Detail {
				part := buildConditionString(detail)
				parts = append(parts, part)
			}
			return fmt.Sprintf("(%s)", strings.Join(parts, fmt.Sprintf(" %s ", cond.Operator)))
		}
		return ""
	case float32:
		if cond.Object != "" && cond.Property != "" && cond.Comparison != "" && cond.Value != 0 {
			s, ok := cond.Value.(float32)
			if !ok {
				fmt.Println("类型转换失败")
				return ""
			}
			return fmt.Sprintf("%s.%s%s%f", cond.Object, cond.Property, cond.Comparison, s)
		} else if cond.Object != "" && cond.Property != "" && cond.Comparison == "" && cond.Value != "" {
			return fmt.Sprintf("%s.%s(%s)", cond.Object, cond.Property, cond.Value)
		}
		if len(cond.Detail) > 0 {
			var parts []string
			for _, detail := range cond.Detail {
				part := buildConditionString(detail)
				parts = append(parts, part)
			}
			return fmt.Sprintf("(%s)", strings.Join(parts, fmt.Sprintf(" %s ", cond.Operator)))
		}
		return ""
	case string:
		if cond.Object != "" && cond.Property != "" && cond.Comparison != "" && cond.Value != "" {
			return fmt.Sprintf("%s.%s%s\"%s\"", cond.Object, cond.Property, cond.Comparison, cond.Value)
		} else if cond.Object != "" && cond.Property != "" && cond.Comparison == "" && cond.Value != "" {
			s, ok := cond.Value.(string)
			if !ok {
				fmt.Println("类型转换失败")
				return ""
			}
			trimmedInput := strings.Trim(s, "{}")
			// 调用 splitString 函数进行切割
			Result := splitString(trimmedInput)
			if len(Result) == 1 {
				Result = AddQuotes(Result)
				splitResult := strings.Join(Result, ",")
				return fmt.Sprintf("%s.%s(%s)", cond.Object, cond.Property, splitResult)
			}
			Result = AddQuotes(Result)
			splitResult := strings.Join(Result, ",")
			return fmt.Sprintf("%s.%s(%s)", cond.Object, cond.Property, splitResult)
		}
		if len(cond.Detail) > 0 {
			var parts []string
			for _, detail := range cond.Detail {
				part := buildConditionString(detail)
				parts = append(parts, part)
			}
			return fmt.Sprintf("(%s)", strings.Join(parts, fmt.Sprintf(" %s ", cond.Operator)))
		}
		return ""
	default:
		if len(cond.Detail) > 0 {
			var parts []string
			for _, detail := range cond.Detail {
				part := buildConditionString(detail)
				parts = append(parts, part)
			}
			return fmt.Sprintf("(%s)", strings.Join(parts, fmt.Sprintf(" %s ", cond.Operator)))
		}
		return ""
	}
}

func splitString(input string) []string {
	// 使用 strings.Split 函数按照分号进行切割
	result := strings.Split(input, ";")
	// 去除每个切割结果的前后引号
	for i, str := range result {
		result[i] = strings.Trim(str, `"`)
	}
	return result
}

// 小数点后全是0的float64转为int型
func convertIfWholeNumber64(input float64) interface{} {
	if math.Trunc(input) == input {
		return int64(input)
	}
	return input
}

// 小数点后全是0的float32转为int型
func convertIfWholeNumber32(input float32) interface{} {
	// 如果小数部分为0，将其转换为整型
	if math.Trunc(float64(input)) == float64(input) {
		return int32(input)
	}
	// 否则返回原浮点数
	return input
}

//func buildConditionString(cond apitypes.Condition) string {
//	//对值的类型进行判断
//	switch cond.Value.(type) {
//	case int64:
//		if cond.Object != "" && cond.Property != "" && cond.Comparison != "" && cond.Value != 0 {
//			return fmt.Sprintf("%s.%s%s%d", cond.Object, cond.Property, cond.Comparison, cond.Value)
//		} else if cond.Object != "" && cond.Property != "" && cond.Comparison == "" && cond.Value != "" {
//			return fmt.Sprintf("%s.%s(%s)", cond.Object, cond.Property, cond.Value)
//		}
//		if len(cond.Detail) > 0 {
//			var parts []string
//			for _, detail := range cond.Detail {
//				part := buildConditionString(detail)
//				parts = append(parts, part)
//			}
//			return fmt.Sprintf("(%s)", strings.Join(parts, fmt.Sprintf(" %s ", cond.Operator)))
//		}
//		return ""
//	case float64:
//		if cond.Object != "" && cond.Property != "" && cond.Comparison != "" && cond.Value != 0 {
//			return fmt.Sprintf("%s.%s%s%f", cond.Object, cond.Property, cond.Comparison, cond.Value)
//		} else if cond.Object != "" && cond.Property != "" && cond.Comparison == "" && cond.Value != "" {
//			return fmt.Sprintf("%s.%s(%s)", cond.Object, cond.Property, cond.Value)
//		}
//		if len(cond.Detail) > 0 {
//			var parts []string
//			for _, detail := range cond.Detail {
//				part := buildConditionString(detail)
//				parts = append(parts, part)
//			}
//			return fmt.Sprintf("(%s)", strings.Join(parts, fmt.Sprintf(" %s ", cond.Operator)))
//		}
//		return ""
//	case float32:
//		if cond.Object != "" && cond.Property != "" && cond.Comparison != "" && cond.Value != 0 {
//			return fmt.Sprintf("%s.%s%s%f", cond.Object, cond.Property, cond.Comparison, cond.Value)
//		} else if cond.Object != "" && cond.Property != "" && cond.Comparison == "" && cond.Value != "" {
//			return fmt.Sprintf("%s.%s(%s)", cond.Object, cond.Property, cond.Value)
//		}
//		if len(cond.Detail) > 0 {
//			var parts []string
//			for _, detail := range cond.Detail {
//				part := buildConditionString(detail)
//				parts = append(parts, part)
//			}
//			return fmt.Sprintf("(%s)", strings.Join(parts, fmt.Sprintf(" %s ", cond.Operator)))
//		}
//		return ""
//	case string:
//		if cond.Object != "" && cond.Property != "" && cond.Comparison != "" && cond.Value != "" {
//			return fmt.Sprintf("%s.%s%s%s", cond.Object, cond.Property, cond.Comparison, cond.Value)
//		} else if cond.Object != "" && cond.Property != "" && cond.Comparison == "" && cond.Value != "" {
//			s, ok := cond.Value.(string)
//			if !ok {
//				fmt.Println("类型转换失败")
//				return ""
//			}
//			trimmedInput := strings.Trim(s, "{}")
//			// 调用 splitString 函数进行切割
//			Result := splitString(trimmedInput)
//			if len(Result) == 1 {
//				Result = AddQuotes(Result)
//				splitResult := strings.Join(Result, ",")
//				return fmt.Sprintf("%s.%s(%s)", cond.Object, cond.Property, splitResult)
//			}
//			Result = AddQuotes(Result)
//			splitResult := strings.Join(Result, ",")
//			return fmt.Sprintf("%s.%s(%s)", cond.Object, cond.Property, splitResult)
//		}
//		if len(cond.Detail) > 0 {
//			var parts []string
//			for _, detail := range cond.Detail {
//				part := buildConditionString(detail)
//				parts = append(parts, part)
//			}
//			return fmt.Sprintf("(%s)", strings.Join(parts, fmt.Sprintf(" %s ", cond.Operator)))
//		}
//		return ""
//	default:
//		if len(cond.Detail) > 0 {
//			var parts []string
//			for _, detail := range cond.Detail {
//				part := buildConditionString(detail)
//				parts = append(parts, part)
//			}
//			return fmt.Sprintf("(%s)", strings.Join(parts, fmt.Sprintf(" %s ", cond.Operator)))
//		}
//		return ""
//	}
//}

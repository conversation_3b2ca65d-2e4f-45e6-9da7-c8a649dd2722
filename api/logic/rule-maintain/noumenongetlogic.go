package rule_maintain

import (
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
)

func GetNoumenon(c *gin.Context) {
	var reqobj apitypes.GetNoumenonRequest
	var rspobj apitypes.GetNoumenonResponse
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 400, "入参错误", err)
		return
	}
	noum := models.DksKnbNoumenon{}
	intid, _ := strconv.ParseInt(reqobj.GlobalID, 10, 64)
	result, _ := noum.FindByGlobalID(intid)
	if result == nil || result.Status == 0 || result.IsDelete == 1 {
		response.Response(c, 200, "未查询到数据", rspobj)
		return
	}
	rspobj.NoumenonName = result.NoumenonName
	rspobj.NoumenonKey = result.NoumenonKey
	response.Response(c, 200, "success", rspobj)
	return
}

package rule_maintain

//
//import (
//	"encoding/json"
//	"fmt"
//	"github.com/gin-gonic/gin"
//	apitypes "kmbservice/api/types"
//	"kmbservice/tools/response"
//	"kmbservice/repository/models"
//	"strconv"
//	"strings"
//)
//
//func RuleCreate(c *gin.Context) {
//	var (
//		reqobj apitypes.RuleCreateRequest
//		rspobj apitypes.RuleCreateResponse
//	)
//	if err := c.ShouldBind(&reqobj); err != nil {
//		response.Response(c, 200, "【1001】入参错误", err)
//		return
//	}
//	ruledetailmodel := models.RuleContentDetail{}
//	ruledetail_obj_mapping := models.RuleContentDetailObjectMapping{}
//
//	//处理请求参数
//	jsonData, err := json.MarshalIndent(reqobj, "", "    ")
//	if err != nil {
//		fmt.Println("Failed to marshal JSON:", err)
//		return
//	}
//	//拼接规则首行
//	rule_header := fmt.Sprintf(`ruleEnums %s`, reqobj.RuleName)
//	//拼接警告信息
//	rule_else := fmt.Sprintf(`{ %s.%s(%s,%s,%s)}`, reqobj.RuleResult[0].Noumenon, reqobj.RuleResult[0].Method,
//		reqobj.RuleResult[0].Code, reqobj.RuleResult[0].Message, reqobj.RuleResult[0].Content)
//	//拼接逻辑主体
//	var ruleBody string
//	//使用map转存层级逻辑运算符
//	var rulebody map[string]string
//	rulebody = make(map[string]string)
//
//	//处理
//	rulebody["tier_first"+strconv.Itoa(1)] = reqobj.RuleBody.LogicalOperator
//	var firsttierBuilder strings.Builder
//	var firsttierlist []string
//
//	for j, d := range reqobj.RuleBody.Content {
//		rulebody["tier_second"+strconv.Itoa(j)] = d.LogicalOperator
//		var secondtierlist []string
//		var secondtierBuilder strings.Builder
//
//		for _, n := range d.Metadata {
//			//拼接基本判断逻辑
//			operation_expression := fmt.Sprintf("%s.%s %s %s", n.Noumenon, n.Key, n.ComparisonOperator, n.Value)
//			secondtierlist = append(secondtierlist, operation_expression)
//		}
//		for i, str := range secondtierlist {
//			if i > 0 {
//				//获取对应层级的逻辑符
//				op := rulebody["tier_second"+strconv.Itoa(j)]
//				opstr := fmt.Sprintf(" %s ", op)
//				secondtierBuilder.WriteString(opstr)
//			}
//			secondtierBuilder.WriteString(str)
//		}
//		//完成单条判断条件拼接
//		secondtierresult := secondtierBuilder.String()
//		//外层加括号
//		secondtierresult = "(" + secondtierresult + ")"
//		firsttierlist = append(firsttierlist, secondtierresult)
//	}
//	//将单条判断拼接为块
//	for i, str := range firsttierlist {
//		if i > 0 {
//			//获取对应层级的逻辑符
//			firstop := rulebody["tier_first"+strconv.Itoa(1)]
//			firstopstr := fmt.Sprintf(" %s ", firstop)
//			firsttierBuilder.WriteString(firstopstr)
//		}
//		firsttierBuilder.WriteString(str)
//	}
//	ruleBody = firsttierBuilder.String()
//
//	//最外层加上if {}
//	ruleBody = fmt.Sprintf("if %s", ruleBody)
//	//拼接头尾
//	entirety := fmt.Sprintf("%s %s %s ", rule_header, ruleBody, rule_else)
//	//拼接为规则格式
//	// 将字符串拆分成不同的部分
//	ruleParts := strings.Split(entirety, " if ")
//	ruleHeader := ruleParts[0]
//	ruleCondition := ruleParts[1]
//
//	// 处理头部部分
//	ruleHeaderParts := strings.Split(ruleHeader, " ")
//	ruleHeaderFormatted := fmt.Sprintf(`ruleEnums "%s %s %s %s" "%s" salience %s`,
//		ruleHeaderParts[1], ruleHeaderParts[2], ruleHeaderParts[3], ruleHeaderParts[4], ruleHeaderParts[6], ruleHeaderParts[8])
//
//	// 处理条件部分
//	ruleCondition = strings.TrimSuffix(ruleCondition, " } ")
//	conditionParts := strings.Split(ruleCondition, "{ ")
//	condition := conditionParts[0]
//	action := strings.TrimSuffix(conditionParts[1], " )")
//
//	// todo:格式化条件和操作,条件块可能不影响，看情况可删除
//	conditionFormatted := strings.ReplaceAll(condition, "==", "== \"")
//	//conditionFormatted = strings.ReplaceAll(conditionFormatted, "&&", "&& patient.ageunit == \"00001\" && patient.ageunit == \"1\") || (medicines.ID == \"CE0001\" && medicines.dosage > \"100\"")
//	actionFormatted := strings.ReplaceAll(action, ",", "\", \"")
//	actionFormatted = strings.ReplaceAll(actionFormatted, "(", "(\"")
//	actionFormatted = strings.ReplaceAll(actionFormatted, ")", "\")")
//
//	// 拼接最终的规则
//	finalRule := fmt.Sprintf(`%s
//begin
//    if %s {
//        %s
//    }
//end`, ruleHeaderFormatted, conditionFormatted, actionFormatted)
//
//	//处理传入的详情信息
//	// 使用 逻辑运算符 连接
//	andJoined := strings.Join(reqobj.RuleParticulars, reqobj.LogicalOperation)
//
//	//开启事务
//	db := models.GetDb()
//	tx := db.Begin()
//	// 检查事务是否启动成功
//	if tx.Error != nil {
//		response.Response(c, 200, "【4001】事务启动失败", tx.Error)
//		return
//	}
//	//提示语
//	p := reqobj.RuleResult[0].Content
//	//提示类型
//	w := reqobj.RuleResult[0].Code
//	intstatus, _ := strconv.ParseInt(reqobj.Status, 10, 64)
//	//将拼接好的单条规则存入数据库中
//	ruleId, _, err := ruledetailmodel.CreateRuleDetailTx(tx, reqobj.RuleName, finalRule, reqobj.RuleDesc, andJoined, p, w, jsonData, intstatus)
//	if err != nil {
//		tx.Rollback()
//		response.Response(c, 200, "【3001】网络故障", err)
//		return
//	}
//	if ruleId == 0 {
//		response.Response(c, 200, "规则创建失败", err)
//		return
//	}
//	//将规则与对象映射关系插入映射表中
//	errobj := ruledetail_obj_mapping.CreateMappingsTx(tx, ruleId, reqobj.RuleClassficationID)
//	if errobj != nil {
//		tx.Rollback()
//		response.Response(c, 200, "映射关系创建失败", err)
//		return
//	}
//	// 提交事务
//	if errtx := tx.Commit().Error; errtx != nil {
//		response.Response(c, 200, "【4002】事务提交失败", errtx)
//		return
//	}
//	//返回参数处理
//	ruleIdStr := strconv.FormatInt(ruleId, 10)
//	rspobj.RuleID = ruleIdStr
//	rspobj.RuleDesc = reqobj.RuleDesc
//	rspobj.RuleContent = finalRule
//	response.Response(c, 200, "成功", rspobj)
//	return
//}
//
//// 定义条件的结构体
//type Detail struct {
//	Noumenon           string `json:"noumenon"`            // 本体
//	Key                string `json:"key"`                 // 左侧键
//	ComparisonOperator string `json:"comparison_operator"` // 运算操作符
//	Value              string `json:"value"`               // 值
//}
//
//// 定义规则内容的结构体
//type RuleContent struct {
//	LogicalOperation string   `json:"logical_operation"` // "AND" 或 "OR"
//	Detail           []Detail `json:"detail,omitempty"`
//}
//
//func stringBuilding() {
//	//var ruleString strings.Builder
//	//
//	//ruleString.WriteString(fmt.Sprintf("ruleEnums \"%s\" \"%s\" salience %d\n", reqobj.RuleName, reqobj.RuleName, reqobj.Priority))
//	//ruleString.WriteString("begin\n")
//	//
//	//// 处理逻辑运算符
//	//if reqobj.LogicalOperation == "||" {
//	//	ruleString.WriteString("    if (")
//	//} else if reqobj.LogicalOperation == "&&" {
//	//	ruleString.WriteString("    if ")
//	//}
//	//
//	//// 处理规则内容列表
//	//for i, ruleContent := range reqobj.RuleBody {
//	//	if i > 0 {
//	//		ruleString.WriteString(" ")
//	//		ruleString.WriteString(ruleContent.LogicalOperator)
//	//		ruleString.WriteString(" ")
//	//	}
//	//
//	//	ruleString.WriteString("(")
//	//	// 处理每个规则内容
//	//	for j, detail := range ruleContent.Metadata {
//	//		if j > 0 {
//	//			ruleString.WriteString(" && ")
//	//		}
//	//		ruleString.WriteString(detail.Noumenon)
//	//		ruleString.WriteString(".")
//	//		ruleString.WriteString(detail.Key)
//	//		ruleString.WriteString(" ")
//	//		ruleString.WriteString(detail.ComparisonOperator)
//	//		ruleString.WriteString(" ")
//	//		ruleString.WriteString(detail.Value)
//	//	}
//	//	ruleString.WriteString(")")
//	//}
//	//
//	//ruleString.WriteString(") {\n")
//	//
//	//// 处理结果内容列表
//	//for _, result := range reqobj.RuleResult {
//	//	ruleString.WriteString(fmt.Sprintf("\t\t%s.%s(\"%s\",\"%s\",\"%s\")\n", result.Noumenon, result.Method, result.Code, result.Content, result.Message))
//	//}
//	//
//	//ruleString.WriteString("}\nend\n")
//	//
//	//// 输出生成的规则条件字符串
//	//fmt.Println(ruleString.String())
//	//response.Response(c, http.StatusOK, "成功", ruleString.String())
//}

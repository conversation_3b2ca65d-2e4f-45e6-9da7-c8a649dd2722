package rule_maintain

import (
	"fmt"
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"sort"
	"strconv"
	"strings"
)

func RuleDetailVersionChangeLogic(c *gin.Context) {
	var reqobj apitypes.RuleDetailVersionChangerequest
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 400, "入参错误", err)
		return
	}
	rulemodel := models.NewRuleContent{}
	rule_detail_model := models.RuleContentDetail{}
	rule_mapping_model := models.RuleContentMapping{}
	intId, _ := strconv.ParseInt(reqobj.DetailVersionId, 10, 64)
	detailInfo, _ := rule_detail_model.FindByDetailVersionID(intId)
	if detailInfo.PublishingStatus == "PUBLISHED" {
		//查询发布的规则数据是否有在用版本
		rule_detail_use, _ := rule_detail_model.FindActiveRecordsByDetailID(detailInfo.DetailID, 1)
		if len(rule_detail_use) > 1 {
			response.Response(c, 500, "规则数据在用状态不唯一，需排查", nil)
			return
		}
		if len(rule_detail_use) == 0 {
			//直接变更可用状态和发布状态
			err := rule_detail_model.UpdateIsUseByVersionID(intId, 1)
			if err != nil {
				response.Response(c, 500, "状态变更失败", err)
				return
			}
			response.Response(c, 200, "success", nil)
			return
		}
		//查询同 detail_id 的在用规则数据是否有被规则使用
		rulelist, _ := rule_mapping_model.FindByDetailIDAndVersion(rule_detail_use[0].DetailID, rule_detail_use[0].MainVersion, rule_detail_use[0].SubVersion)
		if len(rulelist) == 0 { //没有被规则使用
			dbq := models.GetDb()
			tx := dbq.Begin()
			if tx.Error != nil {
				response.Response(c, 500, "事务启动失败", tx.Error)
				return
			}
			//将之前正在使用的版本状态改为非在用，状态也改为停用
			err := rule_detail_model.UpdateIsUseToZeroByDetailID(tx, rule_detail_use[0].DetailID)
			if err != nil {
				tx.Rollback()
				response.Response(c, 500, "发布状态更新失败", err)
				return
			}
			//修改发布状态与在用状态
			err = rule_detail_model.UpdatePublishingStatusANDIsUseByVersionIDTx(tx, detailInfo.DetailVersionID, "PUBLISHED", 1)
			if err != nil {
				tx.Rollback()
				response.Response(c, 500, "发布状态更新失败", err)
				return
			}
			// 提交事务
			if err = tx.Commit().Error; err != nil {
				response.Response(c, 500, "事务提交失败", err)
				return
			}
			response.Response(c, 200, "success", err)
			return
		}
		//规则数据有被正在使用的规则引用
		//去重并查询版本号最大的
		rulelist = deduplicateByMaxVersion(rulelist)
		//查询有哪些规则是正在用的
		var useingRuleList []models.NewRuleContent
		for _, v := range rulelist {
			temp, _ := rulemodel.FindByIDAndIsUse(v.RuleGroupID, 1)
			if temp != nil {
				useingRuleList = append(useingRuleList, *temp)
			}
		}
		//正在用的规则没有使用该规则数据的，直接变更状态即可
		if len(useingRuleList) == 0 {
			//直接变更可用状态和发布状态
			err := rule_detail_model.UpdateIsUseByVersionID(intId, 1)
			if err != nil {
				response.Response(c, 500, "状态变更失败", err)
				return
			}
		}
		for _, v := range useingRuleList {
			//处理映射表数据
			mapplist, _ := rule_mapping_model.FindByIDAndAllVersion(v.ID, v.MainVersion, v.SubVersion)
			sort.Slice(mapplist, func(i, j int) bool {
				return mapplist[i].SerialNumber < mapplist[j].SerialNumber
			})
			var mappinglist []models.RuleContentMapping
			for _, m := range mapplist {
				var mappingmodel models.RuleContentMapping
				if m.RuleContentDetailID != rule_detail_use[0].DetailID {
					mappingmodel.RuleGroupID = m.RuleGroupID
					mappingmodel.RuleContentDetailID = m.RuleContentDetailID
					mappingmodel.RuleGroupName = m.RuleGroupName
					mappingmodel.RuleContentDetailName = m.RuleContentDetailName
					mappingmodel.SerialNumber = m.SerialNumber
					mappingmodel.RuleMainVersion = m.RuleMainVersion + 1
					mappingmodel.RuleSubVersion = 0
					mappingmodel.RuleDetailMainVersion = m.RuleDetailMainVersion
					mappingmodel.RuleDetailSubVersion = m.RuleDetailSubVersion
					mappinglist = append(mappinglist, mappingmodel)
				}
				if m.RuleContentDetailID == detailInfo.DetailID {
					mappingmodel.RuleGroupID = m.RuleGroupID
					mappingmodel.RuleContentDetailID = m.RuleContentDetailID
					mappingmodel.RuleGroupName = m.RuleGroupName
					mappingmodel.RuleContentDetailName = m.RuleContentDetailName
					mappingmodel.SerialNumber = m.SerialNumber
					mappingmodel.RuleMainVersion = m.RuleMainVersion + 1
					mappingmodel.RuleSubVersion = 0
					mappingmodel.RuleDetailMainVersion = detailInfo.MainVersion
					mappingmodel.RuleDetailSubVersion = detailInfo.SubVersion
					mappinglist = append(mappinglist, mappingmodel)
				}
			}

			var mappingDetailIdList []int64
			for _, z := range mappinglist {
				mappingDetailIdList = append(mappingDetailIdList, z.RuleContentDetailID)
			}
			//更新规则内容（引用编辑）
			var (
				rulemainVersion int64
				rulesubVersion  int64
			)

			rulemainVersion = v.MainVersion + 1
			rulesubVersion = 0

			//切换规则数据的可用状态
			//TODO:事务可进行合并
			dbq := models.GetDb()
			tx := dbq.Begin()
			if tx.Error != nil {
				response.Response(c, 500, "事务启动失败", tx.Error)
				return
			}
			//将之前正在使用的版本状态改为非在用，状态也改为停用
			err := rule_detail_model.UpdateIsUseToZeroByDetailID(tx, rule_detail_use[0].DetailID)
			if err != nil {
				tx.Rollback()
				response.Response(c, 500, "发布状态更新失败", err)
				return
			}
			//修改发布状态与在用状态
			err = rule_detail_model.UpdatePublishingStatusANDIsUseByVersionIDTx(tx, detailInfo.DetailVersionID, "PUBLISHED", 1)
			if err != nil {
				tx.Rollback()
				response.Response(c, 500, "发布状态更新失败", err)
				return
			}
			// 提交事务
			if err = tx.Commit().Error; err != nil {
				response.Response(c, 500, "事务提交失败", err)
				return
			}

			detaillist, err := rule_detail_model.FindActiveDetailsByIDsEdit(mappingDetailIdList, "")
			if err != nil || len(detaillist) == 0 {
				response.Response(c, 500, "网络故障", err)
				return
			}
			//拼接规则内容
			var rulelists []string
			for _, m := range detaillist {
				rulelists = append(rulelists, m.RuleContent)
			}
			//给规则数据加上优先级
			for i, rule := range rulelists {
				firstLineEnd := strings.Index(rule, "\n")
				if firstLineEnd == -1 {
					firstLineEnd = len(rule)
				}
				numberToAdd := i + 1
				numberString := strconv.Itoa(numberToAdd)
				ruleWithNumber := rule[:firstLineEnd] + numberString + rule[firstLineEnd:]
				rulelists[i] = ruleWithNumber
			}
			//拼接打包
			packedRules := strings.Join(rulelists, "\n\n")
			//开启事务
			db2 := models.GetDb()
			tx2 := db2.Begin()
			// 检查事务是否启动成功
			if tx2.Error != nil {
				response.Response(c, 500, "事务启动失败", tx2.Error)
				return
			}
			//将之前正在使用的版本状态改为不可用
			errup := rulemodel.UpdateIsUseToZeroByruleID(tx2, v.ID)
			if errup != nil {
				tx2.Rollback()
			}
			//向映射表中插入需要添加的映射关系
			sort.Slice(mappinglist, func(i, j int) bool {
				return mappinglist[i].SerialNumber < mappinglist[j].SerialNumber
			})
			err = rule_mapping_model.BatchInsertRuleContentMappingsTx(tx2, mappinglist)
			if err != nil {
				tx2.Rollback()
				response.Response(c, 500, "映射关联失败", err)
				return
			}
			//将打包好的规则保存
			_, errc := rulemodel.CreateRuleGroupTx(tx2, v.ID, rulemainVersion, rulesubVersion, v.RuleName, v.RuleCode, v.RuleDescription, packedRules, v.UsageScenario, fmt.Sprintf("%d", v.CategoryTreeID), fmt.Sprintf("%d", v.CategoryId), fmt.Sprintf("%d", v.Status))
			if errc != nil {
				tx2.Rollback()
				response.Response(c, 500, "切换失败", errc)
				return
			}
			// 提交事务
			if errtx := tx2.Commit().Error; errtx != nil {
				response.Response(c, 500, "事务提交失败", errtx)
				return
			}
		}
		response.Response(c, 200, "success", nil)
		return
	} else {
		response.Response(c, 500, "当前数据不可切换！", fmt.Sprintf("%s", detailInfo.RuleName))
		return
	}
}

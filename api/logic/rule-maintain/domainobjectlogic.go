package rule_maintain

import (
	"fmt"
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
)

func GetDomainObject(c *gin.Context) {
	var (
		reqobj apitypes.GetDomainObjectRequest
		rspobj apitypes.GetDomainObjectResponse
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 400, "入参错误", err)
		return
	}
	dslobj := models.DksKnbDslObject{}
	dslPropertyModel := models.DksKnbDslProperty{}
	if reqobj.Name != "" {
		objlist, _ := dslobj.GetListByNameLike(reqobj.Name)
		if len(objlist) == 0 {
			response.Response(c, 200, "未查询到对应数据", rspobj)
			return
		}
		for _, v := range objlist {
			var content apitypes.GetDomainObjectResponseContent
			propert, _ := dslPropertyModel.GetDksKnbDslPropertiesByDslID(v.DslID)
			if v.IsDel == 1 || v.Status == 0 || v.OntoID == nil || len(propert) == 0 {
				continue
			}
			content.DslID = fmt.Sprintf("%d", v.DslID)
			content.DslName = v.DslName
			content.DslDesc = v.DslDesc
			rspobj.Rsplist = append(rspobj.Rsplist, content)
		}
		response.Response(c, 200, "success", rspobj)
		return
	} else {
		all, _ := dslobj.GetAllData()
		if len(all) == 0 {
			response.Response(c, 500, "未查询到对应数据", rspobj)
			return
		}
		for _, v := range all {
			propert, _ := dslPropertyModel.GetDksKnbDslPropertiesByDslID(v.DslID)
			if v.IsDel == 1 || v.Status == 0 || v.OntoID == nil || len(propert) == 0 {
				continue
			}
			var content apitypes.GetDomainObjectResponseContent
			content.DslID = fmt.Sprintf("%d", v.DslID)
			content.DslName = v.DslName
			content.DslDesc = v.DslDesc
			rspobj.Rsplist = append(rspobj.Rsplist, content)
		}
		response.Response(c, 200, "success", rspobj)
		return
	}
}

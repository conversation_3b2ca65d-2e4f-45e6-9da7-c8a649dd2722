package rule_maintain

import (
	"kmbservice/api/types"
	"kmbservice/repository/models"
)

func RuleClosedLoopDetail(req types.RuleClosedLoopDetailRequest) (rsp types.RuleClosedLoopDetailResponse, err error) {
	rulemodel := models.NewRuleContent{}
	ruleList, err := rulemodel.GetRuleContentsByIDs(req.RuleIDList)
	if len(ruleList) == 0 {
		return rsp, err
	}
	for _, v := range ruleList {
		var temp types.RuleClosedLoopDetailContent
		temp.RuleID = v.ID
		temp.RuleVersionID = v.RuleVersionID
		temp.Name = v.RuleName
		temp.Desc = v.RuleDescription
		rsp.RuleNameDesc = append(rsp.RuleNameDesc, temp)
	}
	return rsp, err
}

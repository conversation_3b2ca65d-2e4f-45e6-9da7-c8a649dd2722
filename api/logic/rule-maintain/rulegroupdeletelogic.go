package rule_maintain

import (
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
)

func RuleGroupDelete(c *gin.Context) {
	var reqobj apitypes.RuleGroupDeleteRequest
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 200, "【1001】入参错误", err)
		return
	}
	rulemodel := models.NewRuleContent{}
	intID, _ := strconv.ParseInt(reqobj.RuleGroupVersionID, 10, 64)
	err := rulemodel.UpdateIsDeletedByRuleVersionID(intID)
	if err != nil {
		response.Response(c, 200, "fail", err)
		return
	}
	response.Response(c, 200, "success", nil)
	return
}

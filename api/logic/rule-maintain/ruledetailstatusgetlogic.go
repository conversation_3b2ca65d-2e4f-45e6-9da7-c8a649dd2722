package rule_maintain

import (
	"fmt"
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"math"
	"sort"
	"strconv"
	"time"
)

func RuleDetailStatusGet(c *gin.Context) {
	var (
		reqobj apitypes.RuleDetailStatusGetRequest
		rspobj apitypes.GetDetailListResponse
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 200, "【1001】入参错误", err)
		return
	}
	ruledetailmodel := models.RuleContentDetail{}
	intstatus, _ := strconv.ParseInt(reqobj.Status, 10, 64)
	// 分页入参处理
	var offset, limit int
	if reqobj.Limit != 0 {
		limit = reqobj.Limit
	} else {
		limit = 50
	}
	if reqobj.Page != 0 {
		offset = (reqobj.Page - 1) * reqobj.Limit
	} else {
		offset = 0
	}
	detailcontentListcount, _ := ruledetailmodel.FindByStatus(intstatus)
	countNoLimit := len(detailcontentListcount)
	detailcontentList, _ := ruledetailmodel.FindByStatusLimit(intstatus, offset, limit)
	var detaillist []apitypes.Rule_detail_content
	//按照更新时间倒排序
	sort.Slice(detailcontentList, func(i, j int) bool {
		return detailcontentList[i].UpdatedAt > detailcontentList[j].UpdatedAt
	})
	for _, v := range detailcontentList {
		var detail apitypes.Rule_detail_content
		detail.RuleName = v.RuleName
		detail.RuleDesc = v.RuleDescription
		detail.Version = fmt.Sprintf("%d.%d", v.MainVersion, v.SubVersion)
		t := time.Unix(v.UpdatedAt, 0)
		// 格式化为年月日时分秒格式
		formattedTime := t.Format("2006-01-02 15:04:05")
		detail.Updated = formattedTime
		detail.Status = strconv.FormatInt(v.Status, 10)
		detail.DetailID = strconv.FormatInt(v.DetailID, 10)
		detail.DetailVersionID = strconv.FormatInt(v.DetailVersionID, 10)
		detail.Using = v.ISUse
		detail.RuleDetailContent = v.RuleContent
		detail.ClassficationID = fmt.Sprintf("%d", v.ClassficationID)
		detaillist = append(detaillist, detail)
	}
	//统计可用状态的数据总条数
	rspobj.ResultDetailList = detaillist
	numberFloat64 := math.Ceil(float64(countNoLimit) / float64(limit))
	number := int(numberFloat64)
	rspobj.Count = countNoLimit
	rspobj.Limit = limit
	rspobj.Page = reqobj.Page
	rspobj.Number = number

	response.Response(c, 200, "成功", rspobj)
	return
}

package rule_maintain

import (
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
)

func RuleDetailCopy(c *gin.Context) {
	var reqobj apitypes.RuleDetailCopyRequest
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 400, "入参错误", err)
		return
	}
	rule_detail_model := models.RuleContentDetail{}
	intversionid, _ := strconv.ParseInt(reqobj.DetailVersionID, 10, 64)
	rd, _ := rule_detail_model.FindByDetailVersionID(intversionid)
	detail_id, _ := rule_detail_model.FindMaxVersionByDetailID(rd.DetailID)
	maxversion := detail_id.MainVersion + 1
	err := rule_detail_model.FindAndDuplicateRecord(intversionid, maxversion)
	if err != nil {
		response.Response(c, 500, "规则数据克隆失败", err)
		return
	}
	response.Response(c, 200, "success", err)
	return
}

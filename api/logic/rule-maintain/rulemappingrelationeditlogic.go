package rule_maintain

import (
	"github.com/gin-gonic/gin"
)

func EditRuleContentMapping(c *gin.Context) {
	//var (
	//	reqobj apitypes.EditRuleContentMappingRequest
	//	rspobj apitypes.EditRuleContentMappingResponse
	//)
	//if err := c.ShouldBind(&reqobj); err != nil {
	//	response.Response(c, 200, "【1001】入参错误", err)
	//	return
	//}
	//rulemappingmodel := models.RuleContentMapping{}
	//rulemodel := models.NewRuleContent{}
	//修改映射表中的数据
}

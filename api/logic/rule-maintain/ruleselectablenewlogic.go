package rule_maintain

import (
	"fmt"
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
	"time"
)

func RuleGroupSelectableNew(c *gin.Context) {
	var (
		reqobj apitypes.RuleGroupSelectableNewRequest
		rspobj apitypes.RuleGroupSelectableNewResponse
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 200, "【1001】入参错误", err)
		return
	}
	ruledetailmodel := models.RuleContentDetail{}
	var intSlice []int64
	for _, str := range reqobj.DetailIdList {
		num, err := strconv.ParseInt(str, 10, 64) // 将字符串转换为int64
		if err != nil {
			fmt.Printf("转换错误: %v\n", err)
			continue
		}
		intSlice = append(intSlice, num) // 添加转换后的int64到切片中
	}
	var detaillist []apitypes.Rule_detail_content
	listNolimit, _ := ruledetailmodel.QueryDataExceptGivenDetailIDs(intSlice)
	for _, v := range listNolimit {
		var detail apitypes.Rule_detail_content
		detail.RuleName = v.RuleName
		detail.RuleDesc = v.RuleDescription
		strversion := strconv.FormatFloat(v.Version, 'f', -1, 64)
		detail.Version = strversion
		t := time.Unix(v.UpdatedAt, 0)
		// 格式化为年月日时分秒格式
		formattedTime := t.Format("2006-01-02 15:04:05")
		detail.Updated = formattedTime
		detail.Status = strconv.FormatInt(v.Status, 10)
		detail.DetailID = strconv.FormatInt(v.DetailID, 10)
		detail.DetailVersionID = strconv.FormatInt(v.DetailVersionID, 10)
		detail.Using = v.ISUse
		detail.RuleDetailContent = v.RuleContent
		detail.ClassficationID = fmt.Sprintf("%d", v.ClassficationID)
		detaillist = append(detaillist, detail)
	}
	rspobj.ResultDetailList = detaillist
	response.Response(c, 200, "【1001】入参错误", rspobj)
	return
}

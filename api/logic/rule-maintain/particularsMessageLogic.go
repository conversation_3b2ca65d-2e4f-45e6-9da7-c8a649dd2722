package rule_maintain

import (
	"fmt"
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
)

func GetParticularsMessage(c *gin.Context) {
	var (
		reqobj apitypes.GetParticularsMessageRequest
		rspobj apitypes.GetParticularsMessageResponse
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 400, "入参错误", err)
		return
	}
	detailmodel := models.RuleContentDetail{}
	numDetailVersionId, err := strconv.ParseInt(reqobj.DetailVersionId, 10, 64) // 将字符串转换为int64
	result, err := detailmodel.FindByDetailVersionID(numDetailVersionId)
	if err != nil {
		response.Response(c, 500, "查询失败", err)
		return
	}
	rspobj.RuleName = result.RuleName
	rspobj.RuleDescription = result.RuleDescription
	strStatus := strconv.FormatInt(result.Status, 10)
	rspobj.Status = strStatus
	rspobj.Particulars = result.Particulars
	rspobj.Version = fmt.Sprintf("%d", result.MainVersion)
	rspobj.Prompt = result.Prompt
	rspobj.WarningType = result.WarningType
	rspobj.ReqDataStruct = string(result.ReqDataStruct)
	rspobj.PublishingStatus = result.PublishingStatus
	response.Response(c, 200, "success", rspobj)
	return
}

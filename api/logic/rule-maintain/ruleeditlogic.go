package rule_maintain

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"kmbservice/api/enum"
	ruleEnum "kmbservice/api/logic/enum"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
	"strings"
	"time"
)

func RuleEdit(c *gin.Context) {
	var (
		reqobj apitypes.RuleEditRequest
		rspobj apitypes.RuleCreateNewResponse
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 400, "入参错误", err)
		return
	}
	ruledetailmodel := models.RuleContentDetail{}
	ruledetail_obj_mapping := models.RuleContentDetailObjectMapping{}
	dmf_data_sets_model := models.DMFDataSets{}
	detailVersionId, _ := strconv.ParseInt(reqobj.DetailVersionId, 10, 64)
	ruleDetailInfo, _ := ruledetailmodel.FindByDetailVersionID(detailVersionId)
	if ruleDetailInfo.PublishingStatus != enum.SAVED && ruleDetailInfo.PublishingStatus != enum.REFUSE {
		response.Response(c, 500, "当前规则数据不可编辑", nil)
		return
	}
	//处理请求参数结构并保存
	reqJsonData, err := json.MarshalIndent(reqobj, "", "    ")
	if err != nil {
		fmt.Println("Failed to marshal JSON:", err)
		return
	}
	editbody, err := json.MarshalIndent(reqobj.RuleBody, "", "    ")
	if err != nil {
		fmt.Println("Failed to marshal JSON:", err)
		return
	}
	//处理规则首行
	rule_header := fmt.Sprintf(`rule "%s" salience`, reqobj.RuleName)
	//处理规则主体
	rulebody := buildConditionString(reqobj.RuleBody)
	//处理警示信息
	rule_else := fmt.Sprintf(` %s.%s("%s","%s","%s")`, "patient", "AddMessage",
		reqobj.RuleResult.Code, reqobj.RuleResult.Content, "00001")
	//合体!
	finalRule := fmt.Sprintf(`%s 
begin
    if %s {
        %s
    }
end`, rule_header, rulebody, rule_else)
	//保存处理
	//开启事务
	db := models.GetDb()
	tx := db.Begin()
	// 检查事务是否启动成功
	if tx.Error != nil {
		response.Response(c, 500, "事务启动失败", tx.Error)
		return
	}
	//提示语
	p := reqobj.RuleResult.Content
	//提示类型
	if reqobj.RuleResult.Code != "" {
		wInfo, _ := dmf_data_sets_model.FindByDataValueCodeANDCodeSystemId(reqobj.RuleResult.Code, ruleEnum.WarringCode)
		if wInfo != nil {
			reqobj.RuleResult.Code = wInfo.DataValueCNMeaning
		}
	}
	w := reqobj.RuleResult.Code
	//状态处理
	//处理规则详情
	var CnOperator string
	if reqobj.RuleBody.Operator != "" {
		switch reqobj.RuleBody.Operator {
		case "&&":
			CnOperator = "AND"
		case "||":
			CnOperator = "OR"

		}
	} else {
		response.Response(c, 500, "逻辑运算符不可为空", "")
		return
	}
	var ruleParticulars string
	if len(reqobj.RuleParticulars) > 0 {
		var builder strings.Builder
		for i, str := range reqobj.RuleParticulars {
			builder.WriteString(str)
			// 如果不是最后一个元素，在末尾添加 " AND "
			if i < len(reqobj.RuleParticulars)-1 {
				builder.WriteString("&nbsp&nbsp" + CnOperator)
			}
			// 在每行末尾添加换行符
			builder.WriteString("<br/>")

			// 输出结果
			ruleParticulars = builder.String()
		}
	}
	//更新规则数据
	updates := map[string]interface{}{
		"detail_id":            ruleDetailInfo.DetailID,         // 明细ID
		"classfication_id":     ruleDetailInfo.ClassficationID,  // 分类ID
		"rule_name":            reqobj.RuleName,                 // 规则名称
		"rule_description":     reqobj.RuleDesc,                 // 规则描述
		"usage_scenario":       ruleDetailInfo.UsageScenario,    // 规则场景
		"status":               ruleDetailInfo.Status,           // 状态：1-可用；0-不可用
		"rule_content":         finalRule,                       // 规则内容
		"revision":             ruleDetailInfo.Revision,         // 乐观锁
		"created_at":           ruleDetailInfo.CreatedAt,        // 创建时间（Unix时间戳示例）
		"created_by":           ruleDetailInfo.CreatedBy,        // 创建人
		"updated_at":           time.Now().Unix(),               // 更新时间（Unix时间戳示例）
		"updated_by":           "User",                          // 更新人
		"deleted_at":           ruleDetailInfo.DeletedAt,        // 删除时间（Unix时间戳示例，0表示未删除）
		"deleted_by":           ruleDetailInfo.DeletedBy,        // 删除人
		"particulars":          ruleParticulars,                 // 规则详情
		"prompt":               p,                               // 提示语
		"warning_type":         w,                               // 警示类型
		"req_data_struct":      reqJsonData,                     // 入参结构
		"version":              ruleDetailInfo.Version,          // 版本号
		"is_use":               ruleDetailInfo.ISUse,            // 是否可用
		"main_version":         ruleDetailInfo.MainVersion,      // 主版本号
		"sub_version":          ruleDetailInfo.SubVersion,       // 次版本号
		"req_rule_body_struct": editbody,                        // 规则数据内容保存
		"publishing_status":    ruleDetailInfo.PublishingStatus, // 发布状态
		"publishing_reject":    ruleDetailInfo.PublishingReject, // 驳回标识
		"is_del":               ruleDetailInfo.IsDel,            // 是否删除
	}
	err = ruledetailmodel.UpdateRuleContentDetailTx(tx, detailVersionId, updates)
	if err != nil {
		tx.Rollback()
		response.Response(c, 500, "规则编辑失败", err)
		return
	}
	if ruleDetailInfo.PublishingStatus == enum.REFUSE {
		err = ruledetailmodel.UpdatePublishingStatusANDIsUseByVersionIDTx(tx, detailVersionId, enum.SAVED, 0)
		if err != nil {
			tx.Rollback()
			response.Response(c, 500, "发布状态变更失败", err)
			return
		}
	}
	//将规则与对象映射关系插入映射表中
	var intSlice []int64
	if len(reqobj.RuleClassficationID) != 0 {
		// 遍历原始的字符串数组，并将每个字符串转换为int64类型
		for _, str := range reqobj.RuleClassficationID {
			num, err := strconv.ParseInt(str, 10, 64) // 将字符串转换为int64
			if err != nil {
				fmt.Printf("转换错误: %v\n", err)
				continue
			}
			intSlice = append(intSlice, num) // 添加转换后的int64到切片中
		}
	}
	errobj := ruledetail_obj_mapping.DeleteByDetailIDTx(tx, ruleDetailInfo.DetailID)
	if errobj != nil {
		tx.Rollback()
		response.Response(c, 500, "映射更新失败", err)
		return
	}
	errobj = ruledetail_obj_mapping.CreateMappingsTx(tx, ruleDetailInfo.DetailID, intSlice)
	if errobj != nil {
		tx.Rollback()
		response.Response(c, 500, "映射关联失败", err)
		return
	}
	if errtx := tx.Commit().Error; errtx != nil {
		response.Response(c, 500, "事务执行失败", errtx)
		return
	}
	ruleIdStr := strconv.FormatInt(ruleDetailInfo.DetailID, 10)
	rspobj.RuleID = ruleIdStr
	detailVersionIdStr := strconv.FormatInt(detailVersionId, 10)
	rspobj.DetailVersionId = detailVersionIdStr
	rspobj.RuleDesc = reqobj.RuleDesc
	rspobj.RuleContent = finalRule
	rspobj.RuleCname = string(reqJsonData)
	response.Response(c, 200, "成功", rspobj)
	return
}

package rule_maintain

import (
	"fmt"
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"sort"
	"strconv"
	"strings"
)

func RuleGroupEdit(c *gin.Context) {
	var (
		reqobj      apitypes.RuleGroupEditRequest
		rspobj      apitypes.RuleGroupEditResponse
		mainVersion int64
		subVersion  int64
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 200, "【1001】入参错误", err)
		return
	}
	errparam := ValidateEditParams(reqobj)
	if errparam != nil {
		response.Response(c, 200, "规则名不可为空", errparam)
		return
	}
	rulemodel := models.NewRuleContent{}
	ruledetailmodel := models.RuleContentDetail{}
	rulemappingmodel := models.RuleContentMapping{}

	//获取规则内容
	rulegroupid, _ := strconv.ParseInt(reqobj.RuleGroupID, 10, 64)
	rulegroupcontent, _ := rulemodel.FindByIDAndIsUse(rulegroupid, 1)
	mainVersion = rulegroupcontent.MainVersion
	subVersion = rulegroupcontent.SubVersion
	//判断规则编码是否重复
	codepu, _ := rulemodel.IsRuleCodeExists(reqobj.RuleCode)
	if codepu == true && rulegroupcontent.RuleCode != reqobj.RuleCode {
		response.Response(c, 200, "规则编码已存在", "")
		return
	}
	//查询需要打包的规则内容
	var intdetailidSlice []int64
	if len(reqobj.RuleList) != 0 {
		for _, str := range reqobj.RuleList {
			num, err := strconv.ParseInt(str, 10, 64)
			if err != nil {
				fmt.Printf("Error converting string %s to int64: %v\n", str, err)
				continue
			}
			intdetailidSlice = append(intdetailidSlice, num)
		}
	}
	detaillist, err := ruledetailmodel.FindActiveDetailsByIDsEdit(intdetailidSlice, "")
	if err != nil {
		response.Response(c, 200, "【3001】网络故障", err)
		return
	}
	/*
		若不改变规则数据内容
	*/
	mappingdetaillist, _ := rulemappingmodel.FindByIDAndAllVersion(rulegroupid, mainVersion, subVersion)
	sort.Slice(mappingdetaillist, func(i, j int) bool {
		return mappingdetaillist[i].SerialNumber < mappingdetaillist[j].SerialNumber
	})
	var mappingdetailidSlice []int64
	for _, ma := range mappingdetaillist {
		mappingdetailidSlice = append(mappingdetailidSlice, ma.RuleContentDetailID)
	}
	var flag bool
	flag = IntEqual(intdetailidSlice, mappingdetailidSlice)
	if flag == true {
		//开启事务
		db := models.GetDb()
		tx := db.Begin()
		// 检查事务是否启动成功
		if tx.Error != nil {
			response.Response(c, 200, "【4001】事务启动失败", tx.Error)
			return
		}
		//将之前正在使用的版本状态改为不可用
		errup := rulemodel.UpdateIsUseToZeroByruleID(tx, rulegroupid)
		if errup != nil {
			tx.Rollback()
		}
		//向映射表中插入需要添加的映射关系
		realship, _ := rulemappingmodel.FindByIDAndAllVersion(rulegroupid, mainVersion, subVersion)
		subVersion += 1
		var mappingmodellist []models.RuleContentMapping
		for _, v := range realship {
			var mappingmodel models.RuleContentMapping
			mappingmodel.RuleGroupID = v.RuleGroupID
			mappingmodel.RuleGroupName = reqobj.RuleGroupName
			mappingmodel.RuleMainVersion = v.RuleMainVersion
			mappingmodel.RuleSubVersion = subVersion
			mappingmodel.SerialNumber = v.SerialNumber
			mappingmodel.RuleDetailMainVersion = v.RuleDetailMainVersion
			mappingmodel.RuleDetailSubVersion = v.RuleDetailSubVersion
			mappingmodel.RuleContentDetailID = v.RuleContentDetailID
			mappingmodel.RuleContentDetailName = v.RuleContentDetailName
			mappingmodellist = append(mappingmodellist, mappingmodel)
		}
		if len(mappingdetaillist) > 0 {
			err1 := rulemappingmodel.BatchInsertRuleContentMappingsTx(tx, mappingmodellist)
			if err1 != nil {
				tx.Rollback()
				response.Response(c, 200, "【3001】网络故障", err1)
				return
			}
		}
		//sort.Slice(mappingmodellist, func(i, j int) bool {
		//	return mappingmodellist[i].SerialNumber < mappingmodellist[j].SerialNumber
		//})
		//err1 := rulemappingmodel.BatchInsertRuleContentMappingsTx(tx, mappingmodellist)
		//if err1 != nil {
		//	tx.Rollback()
		//	response.Response(c, 200, "【3001】网络故障", err1)
		//	return
		//}
		//将打包好的规则保存
		_, errc := rulemodel.CreateRuleGroupTx(tx, rulegroupid, mainVersion, subVersion, reqobj.RuleGroupName, reqobj.RuleCode, reqobj.RuleDescription, rulegroupcontent.RuleContent, reqobj.UseScene, reqobj.RuleTreeID, reqobj.RuleCategoryID, reqobj.Status)
		if errc != nil {
			tx.Rollback()
			response.Response(c, 200, "【3001】网络故障", errc)
			return
		}
		// 提交事务
		if errtx := tx.Commit().Error; errtx != nil {
			response.Response(c, 200, "【4002】事务提交失败", errtx)
			return
		}
		response.Response(c, 200, "成功", "")
		return
	}
	//版本更新
	subVersion += 1
	//拼接规则内容
	var rulelists []string
	var mappingmodellist []models.RuleContentMapping

	for i, v := range detaillist {
		var mappingmodel models.RuleContentMapping
		mappingmodel.RuleGroupID = rulegroupid
		mappingmodel.RuleGroupName = reqobj.RuleGroupName
		mappingmodel.RuleMainVersion = mainVersion
		mappingmodel.RuleSubVersion = subVersion
		mappingmodel.SerialNumber = int64(i + 1)
		mappingmodel.RuleDetailMainVersion = v.MainVersion
		mappingmodel.RuleDetailSubVersion = v.SubVersion
		mappingmodel.RuleContentDetailID = v.DetailID
		mappingmodel.RuleContentDetailName = v.RuleName
		mappingmodellist = append(mappingmodellist, mappingmodel)
		rulelists = append(rulelists, v.RuleContent)
	}
	//给规则数据加上优先级
	for i, rule := range rulelists {
		firstLineEnd := strings.Index(rule, "\n")
		if firstLineEnd == -1 {
			firstLineEnd = len(rule)
		}
		numberToAdd := i + 1
		numberString := strconv.Itoa(numberToAdd)
		ruleWithNumber := rule[:firstLineEnd] + numberString + rule[firstLineEnd:]
		rulelists[i] = ruleWithNumber
	}
	//拼接打包
	packedRules := strings.Join(rulelists, "\n\n")
	rspobj.ResultGroup = packedRules

	//开启事务
	db := models.GetDb()
	tx := db.Begin()
	// 检查事务是否启动成功
	if tx.Error != nil {
		response.Response(c, 200, "【4001】事务启动失败", tx.Error)
		return
	}
	//将之前正在使用的版本状态改为不可用
	errup := rulemodel.UpdateIsUseToZeroByruleID(tx, rulegroupid)
	if errup != nil {
		tx.Rollback()
	}
	//向映射表中插入需要添加的映射关系
	sort.Slice(mappingmodellist, func(i, j int) bool {
		return mappingmodellist[i].SerialNumber < mappingmodellist[j].SerialNumber
	})
	err1 := rulemappingmodel.BatchInsertRuleContentMappingsTx(tx, mappingmodellist)
	if err1 != nil {
		tx.Rollback()
		response.Response(c, 200, "【3001】网络故障", err1)
		return
	}
	//将打包好的规则保存
	content, errc := rulemodel.CreateRuleGroupTx(tx, rulegroupid, mainVersion, subVersion, reqobj.RuleGroupName, reqobj.RuleCode, reqobj.RuleDescription, packedRules, reqobj.UseScene, reqobj.RuleTreeID, reqobj.RuleCategoryID, reqobj.Status)
	if errc != nil {
		tx.Rollback()
		response.Response(c, 200, "【3001】网络故障", errc)
		return
	}
	// 提交事务
	if errtx := tx.Commit().Error; errtx != nil {
		response.Response(c, 200, "【4002】事务提交失败", errtx)
		return
	}
	rspobj.ResultGroup = content
	response.Response(c, 200, "成功", rspobj)
	return
}

func ValidateEditParams(params apitypes.RuleGroupEditRequest) error {
	if params.RuleGroupID == "" {
		return fmt.Errorf("rule_group_id is required")
	}
	//if params.RuleGroupName == "" {
	//	return fmt.Errorf("rule_group_name is required")
	//}
	//if params.RuleCode == "" {
	//	return fmt.Errorf("rule_code is required")
	//}
	//if params.UseScene == "" {
	//	return fmt.Errorf("use_scene is required")
	//}
	//if params.RuleCategoryID == "" {
	//	return fmt.Errorf("rule_category_id is required")
	//}
	//if params.RuleTreeID == "" {
	//	return fmt.Errorf("rule_tree_id is required")
	//}
	//if params.RuleDescription == "" {
	//	return fmt.Errorf("rule_description is required")
	//}
	//if len(params.RuleList) == 0 {
	//	return fmt.Errorf("rule_list is required")
	//}
	//if params.Status == "" {
	//	return fmt.Errorf("status is required")
	//}
	return nil
}

package rule_maintain

import (
	"fmt"
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
)

func GetNoumenonSlot(c *gin.Context) {
	var (
		reqobj apitypes.GetNoumenonSlotRequest
		rspobj apitypes.GetNoumenonSlotResponse
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 400, "入参错误", err)
		return
	}
	noumslot := models.DksKnbNoumenonSlot{}
	intid, _ := strconv.ParseInt(reqobj.GlobalID, 10, 64)
	result, _ := noumslot.FindByGlobalID(intid)
	if result == nil || result.Status == 0 || result.IsDelete == 1 {
		response.Response(c, 200, "未查询到数据", rspobj)
		return
	}
	rspobj.SlotName = result.SlotName
	rspobj.CodingSystemSoid = fmt.Sprintf("%d", result.CodingSystemSoid)
	rspobj.MaxNumber = fmt.Sprintf("%.2f", result.MaxNumber)
	rspobj.MinNumber = fmt.Sprintf("%.2f", result.MinNumber)
	rspobj.DefaultNumber = result.DefaultNumber
	strDataType := strconv.FormatInt(result.DataType, 10)
	rspobj.DataType = strDataType
	rspobj.Unit = fmt.Sprintf("%.2f", result.Unit)
	rspobj.Basic = fmt.Sprintf("%.2f", result.Basic)
	rspobj.SlotOrder = fmt.Sprintf("%.2f", result.SlotOrder)
	rspobj.SlotKey = result.SlotKey
	rspobj.SlotCode = result.SlotCode
	//rspobj.Operator = result.Operator
	//rspobj.KeyRange = result.KeyRange
	response.Response(c, 200, "success", rspobj)
	return
}

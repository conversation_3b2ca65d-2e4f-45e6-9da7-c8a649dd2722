package rule_maintain

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"kmbservice/api/enum"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"sort"
	"strconv"
	"strings"
)

func RuleDeStatusChange(c *gin.Context) {
	var reqobj apitypes.RuleDeStatusChangeRequest
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 400, "入参错误", err)
		return
	}
	ruleDetailmodel := models.RuleContentDetail{}
	rulemodel := models.NewRuleContent{}
	rulemappingmodel := models.RuleContentMapping{}

	val1, _ := strconv.ParseInt(reqobj.RuleID, 10, 64) //版本id
	val2, _ := strconv.ParseInt(reqobj.Status, 10, 64) //状态
	switch reqobj.RuleType {
	case "0":
		//规则数据停用（使用到该规则数据的规则次版本+1）
		if val2 == 0 {
			//查询哪些规则有用到该规则数据
			detailinfo, _ := ruleDetailmodel.FindByDetailVersionID(val1)
			if detailinfo.PublishingStatus != enum.PUBLISHED {
				response.Response(c, 400, "该数据尚未发布！", nil)
				return
			}
			//查出规则数据信息
			rulelist, _ := rulemappingmodel.FindByDetailIDAndVersion(detailinfo.DetailID, detailinfo.MainVersion, detailinfo.SubVersion) //正在使用这个规则数据的规则映射数据
			//没有正在使用该规则数据的直接修改状态即可
			if len(rulelist) == 0 {
				err := ruleDetailmodel.UpdateStatusByDetailVersionID(val1, val2)
				if err != nil {
					response.Response(c, 500, "更新失败", err)
					return
				}
				response.Response(c, 200, "success", "")
				return
			}
			//去重并查询版本号最大的
			rulelist = deduplicateByMaxVersion(rulelist)
			//查询有哪些规则是正在用的
			var useingRuleList []models.NewRuleContent
			for _, v := range rulelist {
				temp, _ := rulemodel.FindByIDAndIsUse(v.RuleGroupID, 1)
				if temp != nil {
					useingRuleList = append(useingRuleList, *temp)
				}
			}
			//处理这些正在使用这条规则数据的规则
			for _, v := range useingRuleList {
				mapplist, _ := rulemappingmodel.FindByIDAndAllMainSubVersion(v.ID, v.MainVersion, v.SubVersion, detailinfo.MainVersion, detailinfo.SubVersion)
				if len(mapplist) == 0 {
					continue
				}
				sort.Slice(mapplist, func(i, j int) bool {
					return mapplist[i].SerialNumber < mapplist[j].SerialNumber
				})

				var mappingDetailIdList []int64
				for _, z := range mapplist {
					mappingDetailIdList = append(mappingDetailIdList, z.RuleContentDetailID)
				}
				//获得更新后的规则数据列表
				mappingDetailIdList = RemoveIntFromSlice(mappingDetailIdList, detailinfo.DetailID)
				//更新规则内容（引用编辑）
				var (
					mainVersion int64
					subVersion  int64
				)
				mainVersion = v.MainVersion + 1
				subVersion = 0
				//查询需要打包的规则内容
				//若不改变规则数据内容
				if len(mappingDetailIdList) == 0 {
					//开启事务
					db := models.GetDb()
					tx := db.Begin()
					// 检查事务是否启动成功
					if tx.Error != nil {
						response.Response(c, 500, "事务启动失败", tx.Error)
						return
					}
					//将之前正在使用的版本状态改为不可用
					errup := rulemodel.UpdateIsUseToZeroByruleID(tx, v.ID)
					if errup != nil {
						tx.Rollback()
					}
					//将打包好的规则保存
					_, errc := rulemodel.CreateRuleGroupTx(tx, v.ID, mainVersion, subVersion, v.RuleName, v.RuleCode, v.RuleDescription, "", v.UsageScenario, fmt.Sprintf("%d", v.CategoryTreeID), fmt.Sprintf("%d", v.CategoryId), fmt.Sprintf("%d", v.Status))
					if errc != nil {
						tx.Rollback()
						response.Response(c, 500, "事务执行失败", errc)
						return
					}
					// 提交事务
					if errtx := tx.Commit().Error; errtx != nil {
						response.Response(c, 500, "事务提交失败", errtx)
						return
					}
				} else {
					db := models.GetDb()
					tx := db.Begin()
					// 检查事务是否启动成功
					if tx.Error != nil {
						response.Response(c, 500, "事务启动失败", tx.Error)
						return
					}
					//创建映射表信息
					var mappinglist []models.RuleContentMapping
					for _, m := range mapplist {
						var mappingmodel models.RuleContentMapping
						if m.RuleContentDetailID == detailinfo.DetailID {
							continue
						}
						mappingmodel.RuleGroupID = m.RuleGroupID
						mappingmodel.RuleContentDetailID = m.RuleContentDetailID
						mappingmodel.RuleGroupName = m.RuleGroupName
						mappingmodel.RuleContentDetailName = m.RuleContentDetailName
						mappingmodel.SerialNumber = m.SerialNumber
						mappingmodel.RuleMainVersion = mainVersion
						mappingmodel.RuleSubVersion = subVersion
						mappingmodel.RuleDetailMainVersion = m.RuleDetailMainVersion
						mappingmodel.RuleDetailSubVersion = m.RuleDetailSubVersion
						mappinglist = append(mappinglist, mappingmodel)
					}
					detaillist, err := ruleDetailmodel.FindActiveDetailsByIDsEdit(mappingDetailIdList, "")
					if err != nil {
						response.Response(c, 500, "数据库错误", err)
						return
					}
					//拼接规则内容
					var rulelists []string

					for _, m := range detaillist {
						rulelists = append(rulelists, m.RuleContent)
					}
					//给规则数据加上优先级
					for i, rule := range rulelists {
						firstLineEnd := strings.Index(rule, "\n")
						if firstLineEnd == -1 {
							firstLineEnd = len(rule)
						}
						numberToAdd := i + 1
						numberString := strconv.Itoa(numberToAdd)
						ruleWithNumber := rule[:firstLineEnd] + numberString + rule[firstLineEnd:]
						rulelists[i] = ruleWithNumber
					}
					//拼接打包
					packedRules := strings.Join(rulelists, "\n\n")
					//将之前正在使用的版本状态改为不可用
					errup := rulemodel.UpdateIsUseToZeroByruleID(tx, v.ID)
					if errup != nil {
						tx.Rollback()
					}
					//向映射表中插入需要添加的映射关系
					sort.Slice(mappinglist, func(i, j int) bool {
						return mappinglist[i].SerialNumber < mappinglist[j].SerialNumber
					})
					errmapp := rulemappingmodel.BatchInsertRuleContentMappingsTx(tx, mappinglist)
					if errmapp != nil {
						tx.Rollback()
						response.Response(c, 500, "映射关联失败", errmapp)
						return
					}
					//将打包好的规则保存
					_, errc := rulemodel.CreateRuleGroupTx(tx, v.ID, mainVersion, subVersion, v.RuleName, v.RuleCode, v.RuleDescription, packedRules, v.UsageScenario, fmt.Sprintf("%d", v.CategoryTreeID), fmt.Sprintf("%d", v.CategoryId), fmt.Sprintf("%d", v.Status))
					if errc != nil {
						tx.Rollback()
						response.Response(c, 500, "更新后规则存储失败", errc)
						return
					}
					// 提交事务
					if errtx := tx.Commit().Error; errtx != nil {
						response.Response(c, 500, "事务提交失败", errtx)
						return
					}
				}
			}
			//修改表记录状态
			err := ruleDetailmodel.UpdateStatusByDetailVersionID(val1, val2)
			if err != nil {
				response.Response(c, 500, "变更状态失败", err)
				return
			}
			response.Response(c, 200, "success", "")
			return
		}
		err := ruleDetailmodel.UpdateStatusByDetailVersionID(val1, val2)
		if err != nil {
			response.Response(c, 500, "变更状态失败", err)
			return
		}
		response.Response(c, 200, "success", "")
		return

	case "1":
		err := rulemodel.UpdateStatusByRulelVersionID(val1, val2)
		if err != nil {
			response.Response(c, 200, "【3001】网络错误", err)
			return
		}
		response.Response(c, 200, "success", "")
		return
	default:
		response.Response(c, 200, "err", "无该类型，请重新选择")
		return
	}
}

func deduplicateByMaxVersion(rules []models.RuleContentMapping) []models.RuleContentMapping {
	// 创建一个 map 来存储每个 RuleGroupID 对应的最大版本的结构体
	maxVersionMap := map[int64]models.RuleContentMapping{}
	for _, rule := range rules {
		if existingRule, exists := maxVersionMap[rule.RuleGroupID]; !exists || rule.RuleGroupVersion > existingRule.RuleGroupVersion {
			maxVersionMap[rule.RuleGroupID] = rule
		}
	}
	// 将 map 中的值转为切片
	result := []models.RuleContentMapping{}
	for _, rule := range maxVersionMap {
		result = append(result, rule)
	}
	return result
}

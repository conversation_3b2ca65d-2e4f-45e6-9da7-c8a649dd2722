package rule_maintain

import (
	"fmt"
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"sort"
	"strconv"
	"strings"
)

func RuleSplicing(c *gin.Context) {
	var (
		reqobj apitypes.RuleSplicingRequest
		rspobj apitypes.RuleSplicingResponse
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 200, "【1001】入参错误", err)
		return
	}
	errparam := ValidateParams(reqobj)
	if errparam != nil {
		response.Response(c, 200, "规则名称不可为空", errparam)
		return
	}
	rulemodel := models.NewRuleContent{}
	ruledetailmodel := models.RuleContentDetail{}
	rulemappingmodel := models.RuleContentMapping{}
	//判断规则编码是否重复
	codepu, _ := rulemodel.IsRuleCodeExists(reqobj.RuleCode)
	if codepu == true && reqobj.RuleCode != "" {
		response.Response(c, 200, "规则编码已存在", "")
		return
	}
	//生成规则组ID
	rulegroupid := models.GetSnowflakeID()
	if len(reqobj.RuleList) == 0 {
		rulemodel.CreateRuleGroup(rulegroupid, 1, 0, reqobj.RuleGroupName, reqobj.RuleCode, reqobj.RuleDescription, "", reqobj.UseScene, reqobj.RuleTreeID, reqobj.RuleCategoryID, reqobj.Status)
		rspobj.ResultGroup = reqobj.RuleGroupName
		response.Response(c, 200, "success", rspobj)
		return
	}
	//查询需要打包的规则内容
	var intdetailidSlice []int64
	for _, str := range reqobj.RuleList {
		num, err := strconv.ParseInt(str, 10, 64)
		if err != nil {
			fmt.Printf("Error converting string %s to int64: %v\n", str, err)
			continue
		}
		intdetailidSlice = append(intdetailidSlice, num)
	}
	//detaillist, err := ruledetailmodel.FindActiveDetailsByIDs(intdetailidSlice, "")
	////排序
	//indexMap := make(map[int64]int, len(detaillist))
	//for i, detail := range detaillist {
	//	indexMap[detail.DetailID] = i
	//}
	//sort.Slice(detaillist, func(i, j int) bool {
	//	indexI := indexMap[detaillist[i].DetailID]
	//	indexJ := indexMap[detaillist[j].DetailID]
	//	return indexOf(intdetailidSlice, detaillist[indexI].DetailID) < indexOf(intdetailidSlice, detaillist[indexJ].DetailID)
	//})
	//if err != nil || len(detaillist) == 0 {
	//	response.Response(c, 200, "未查询到规则数据", err)
	//	return
	//}
	detaillist, err := ruledetailmodel.FindActiveDetailsByIDsEdit(intdetailidSlice, "")
	if err != nil || len(detaillist) == 0 {
		response.Response(c, 200, "未查询到规则数据", err)
		return
	}
	//拼接规则内容
	var rulelists []string
	var mappingmodellist []models.RuleContentMapping
	for i, v := range detaillist {
		var mappingmodel models.RuleContentMapping
		mappingmodel.RuleGroupID = rulegroupid
		mappingmodel.RuleGroupName = reqobj.RuleGroupName
		mappingmodel.RuleMainVersion = 1
		mappingmodel.RuleSubVersion = 0
		mappingmodel.SerialNumber = int64(i + 1)
		mappingmodel.RuleDetailMainVersion = v.MainVersion
		mappingmodel.RuleDetailSubVersion = v.SubVersion
		mappingmodel.RuleContentDetailID = v.DetailID
		mappingmodel.RuleContentDetailName = v.RuleName
		mappingmodellist = append(mappingmodellist, mappingmodel)
		rulelists = append(rulelists, v.RuleContent)
	}
	//给规则数据加上优先级
	for i, rule := range rulelists {
		firstLineEnd := strings.Index(rule, "\n")
		if firstLineEnd == -1 {
			firstLineEnd = len(rule)
		}
		numberToAdd := i + 1
		numberString := strconv.Itoa(numberToAdd)
		ruleWithNumber := rule[:firstLineEnd] + numberString + rule[firstLineEnd:]
		rulelists[i] = ruleWithNumber
	}
	//拼接打包
	packedRules := strings.Join(rulelists, "\n\n")
	rspobj.ResultGroup = packedRules

	//开启事务
	db := models.GetDb()
	tx := db.Begin()
	// 检查事务是否启动成功
	if tx.Error != nil {
		response.Response(c, 200, "【4001】事务启动失败", tx.Error)
		return
	}
	//向映射表中插入需要添加的映射关系
	sort.Slice(mappingmodellist, func(i, j int) bool {
		return mappingmodellist[i].SerialNumber < mappingmodellist[j].SerialNumber
	})
	err1 := rulemappingmodel.BatchInsertRuleContentMappingsTx(tx, mappingmodellist)
	if err1 != nil {
		tx.Rollback()
		response.Response(c, 200, "【3001】网络故障", err1)
		return
	}
	//将打包好的规则保存
	content, errc := rulemodel.CreateRuleGroupTx(tx, rulegroupid, 1, 0, reqobj.RuleGroupName, reqobj.RuleCode, reqobj.RuleDescription, packedRules, reqobj.UseScene, reqobj.RuleTreeID, reqobj.RuleCategoryID, reqobj.Status)
	if errc != nil {
		tx.Rollback()
		response.Response(c, 200, "【3001】网络故障", errc)
		return
	}
	// 提交事务
	if errtx := tx.Commit().Error; errtx != nil {
		response.Response(c, 200, "【4002】事务提交失败", errtx)
		return
	}
	rspobj.ResultGroup = content
	response.Response(c, 200, "成功", rspobj)
	return
}

// InsertWithTransaction 在事务中插入 RuleContentDetail 和 RuleContentMapping 数据
//func InsertWithTransaction(ruleContent models.NewRuleContent, mapping models.RuleContentMapping) error {
//	// 开始事务、
//	db := models.GetDb()
//	tx := db.Begin()
//	// 检查事务是否启动成功
//	if tx.Error != nil {
//		return tx.Error
//	}
//	// 插入 RuleContentDetail
//	if err := tx.Create(&ruleContent).Error; err != nil {
//		tx.Rollback() // 回滚事务
//		return err
//	}
//
//	// 插入 RuleContentMapping
//	mapping.RuleContentDetailID = ruleContent.ID // 设置关联的 RuleContentDetailID
//	if err := tx.Create(&mapping).Error; err != nil {
//		tx.Rollback() // 回滚事务
//		return err
//	}
//	// 提交事务
//	if err := tx.Commit().Error; err != nil {
//		return err
//	}
//	return nil
//}

func ValidateParams(params apitypes.RuleSplicingRequest) error {
	if params.RuleGroupName == "" {
		return fmt.Errorf("rule_group_name is required")
	}
	//if params.RuleCode == "" {
	//	return fmt.Errorf("rule_code is required")
	//}
	//if params.UseScene == "" {
	//	return fmt.Errorf("use_scene is required")
	//}
	//if params.RuleCategoryID == "" {
	//	return fmt.Errorf("rule_category_id is required")
	//}
	//if params.RuleTreeID == "" {
	//	return fmt.Errorf("rule_tree_id is required")
	//}
	//if params.RuleDescription == "" {
	//	return fmt.Errorf("rule_description is required")
	//}
	//if len(params.RuleList) == 0 {
	//	return fmt.Errorf("rule_list is required")
	//}
	//if params.Status == "" {
	//	return fmt.Errorf("status is required")
	//}
	return nil
}

// 排序
func indexOf(slice []int64, value int64) int {
	for i, v := range slice {
		if v == value {
			return i
		}
	}
	return -1
}

package rule_maintain

import (
	"fmt"
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
)

func GetDomainObjectStats(c *gin.Context) {
	var reqobj apitypes.GetDomainObjectStatsRequest
	var rspobj apitypes.GetDomainObjectStatsResponse
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 400, "入参错误", err)
		return
	}
	dslstatsobj := models.DksKnbDslProperty{}
	dslid, _ := strconv.ParseInt(reqobj.DslID, 10, 64)
	statsList, _ := dslstatsobj.GetDksKnbDslPropertiesByDslID(dslid)
	if len(statsList) == 0 {
		response.Response(c, 400, "未查询到对应数据", rspobj)
		return
	}
	var objsf apitypes.ObjectStats
	objsf.Value = "属性"
	var objsp apitypes.ObjectStats
	objsp.Value = "函数"
	for _, v := range statsList {
		var content apitypes.GetDomainObjectResponseStatsContent
		if v.IsDel == 1 || v.Status == 0 {
			continue
		}
		content.PropertyID = fmt.Sprintf("%d", v.PropertyID)
		content.PropertyName = v.PropertyName
		content.PropertyType = fmt.Sprintf("%d", v.PropertyType)
		content.PropertyDesc = v.PropertyDesc
		content.ParaDef = v.ParaDef
		content.OntoSoltID = fmt.Sprintf("%d", v.OntoSlotID)
		switch v.PropertyType {
		case 1:
			objsf.ObjectStatss = append(objsf.ObjectStatss, content)
		case 2:
			objsp.ObjectStatss = append(objsp.ObjectStatss, content)
		}
	}
	rspobj.ObjectFuncList = append(rspobj.ObjectFuncList, objsp, objsf)
	response.Response(c, 200, "success", rspobj)
	return
}

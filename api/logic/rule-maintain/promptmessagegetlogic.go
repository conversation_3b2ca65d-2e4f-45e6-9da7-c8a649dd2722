package rule_maintain

import (
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
)

func GetPromptMessage(c *gin.Context) {
	var (
		reqobj apitypes.GetPromptMessageRequeat
		rspobj apitypes.GetPromptMessageResponse
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 200, "【1001】入参错误", err)
		return
	}
	dmf_set := models.DMFDataSets{}
	num, err := strconv.ParseInt(reqobj.CodeSystemId, 10, 64)
	list, err := dmf_set.FindByCodeSystemID(num)
	if err != nil {
		response.Response(c, 200, "【3001】 网络故障", err)
		return
	}
	if len(list) == 0 {
		response.Response(c, 200, "【2001】 未查询到数据", rspobj)
		return
	}
	var msglist []apitypes.GetPromptMessageResponseContent
	for _, v := range list {
		if v.IsDelete == 1 || v.Status == 0 {
			continue
		}
		var msg apitypes.GetPromptMessageResponseContent
		soidstr := strconv.FormatInt(v.DataValueSOID, 10)
		msg.Soid = soidstr
		msg.Code = v.DataValueCode
		msg.Content = v.DataValueENMeaning
		msg.CMeaning = v.DataValueCNMeaning
		msglist = append(msglist, msg)
	}
	rspobj.GetPromptMessageResponseContentList = msglist
	response.Response(c, 200, "成功", rspobj)
	return
}

package rule_maintain

import (
	"github.com/gin-gonic/gin"
	"kmbservice/api/enum"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
)

func RuleDetailDelete(c *gin.Context) {
	var reqobj apitypes.RuleDetailDeleteRequest
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 400, "入参错误", err)
		return
	}
	rule_detail_model := models.RuleContentDetail{}
	intid, _ := strconv.ParseInt(reqobj.DetailVersionId, 10, 64)
	detail_info, _ := rule_detail_model.FindByDetailVersionID(intid)
	if detail_info.PublishingStatus != enum.SAVED {
		response.Response(c, 400, "该规则数据不可删除", nil)
		return
	}
	err := rule_detail_model.DeleteByVersionID(intid)
	if err != nil {
		response.Response(c, 500, "数据删除失败", err)
		return
	}
	response.Response(c, 200, "success", nil)
	return
}

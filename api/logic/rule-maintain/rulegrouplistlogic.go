package rule_maintain

import (
	"fmt"
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"math"
	"sort"
	"strconv"
)

func GetRuleGroupList(c *gin.Context) {
	var (
		reqobj apitypes.GetRuleGroupListRequest
		rspobj apitypes.GetRuleGroupListResponse
		count  int
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 200, "【1001】入参错误", err)
		return
	}
	rulemodel := models.NewRuleContent{}
	// 分页入参处理
	var offset, limit int
	if reqobj.Limit != 0 {
		limit = reqobj.Limit
	} else {
		limit = 50
	}
	if reqobj.Page != 0 {
		offset = (reqobj.Page - 1) * reqobj.Limit
	} else {
		offset = 0
	}
	var rulecontent []models.NewRuleContent
	intstatus, _ := strconv.ParseInt(reqobj.Status, 10, 64)
	if reqobj.RuleName != "" && reqobj.Status == "" {
		countrulecontent, _ := rulemodel.QueryRuleByName(reqobj.RuleName)
		count = len(countrulecontent)
		rulecontent, _ = rulemodel.QueryRuleByNameLimit(reqobj.RuleName, offset, limit)
	}
	if reqobj.RuleName == "" && reqobj.Status != "" {
		countrulecontent, _ := rulemodel.FindByStatus(1, intstatus)
		count = len(countrulecontent)
		rulecontent, _ = rulemodel.FindByStatusLimit(1, intstatus, offset, limit)
	}
	if reqobj.RuleName != "" && reqobj.Status != "" {
		countrulecontent, _ := rulemodel.QueryRuleByNameOrDesc(reqobj.RuleName, intstatus)
		count = len(countrulecontent)
		rulecontent, _ = rulemodel.QueryRuleByNameOrDescSe(reqobj.RuleName, intstatus, offset, limit)
	}
	if reqobj.RuleName == "" && reqobj.Status == "" {
		countrulecontent, _ := rulemodel.FindByIsUse(1)
		count = len(countrulecontent)
		rulecontent, _ = rulemodel.FindByIsUseLimit(1, offset, limit)
	}
	//countrulecontent, err := rulemodel.QueryRuleByNameOrCode(reqobj.RuleName, reqobj.Status, offset, limit)
	//if err != nil {
	//	response.Response(c, http.StatusNoContent, "无数据", err)
	//	return
	//}

	sort.Slice(rulecontent, func(i, j int) bool {
		return rulecontent[i].RuleCode > rulecontent[j].RuleCode
	})
	var grouplist []apitypes.RuleGroupListContent

	for _, v := range rulecontent {
		if v.IsDeleted == 1 {
			continue
		}
		var group apitypes.RuleGroupListContent
		group.RuleCode = v.RuleCode
		group.RuleName = v.RuleName
		group.RuleID = fmt.Sprintf("%d", v.ID)
		group.RuleVersionID = fmt.Sprintf("%d", v.RuleVersionID)
		group.RuleDesc = v.RuleDescription
		group.Status = strconv.FormatInt(v.Status, 10)
		grouplist = append(grouplist, group)
	}
	rspobj.GroupList = grouplist
	numberFloat64 := math.Ceil(float64(count) / float64(limit))
	number := int(numberFloat64)
	rspobj.Count = count
	rspobj.Limit = limit
	rspobj.Page = reqobj.Page
	rspobj.Number = number
	response.Response(c, 200, "成功", rspobj)
	return
}

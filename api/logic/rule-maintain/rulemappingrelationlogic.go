package rule_maintain

import (
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
)

func GetRuleContentMapping(c *gin.Context) {
	var (
		reqobj apitypes.GetRuleContentMappingRequest
		rspobj apitypes.GetRuleContentMappingResponse
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 400, "入参错误", err)
		return
	}
	rulemappingmodel := models.RuleContentMapping{}
	//入参为空则返回所有的数据
	if reqobj.RuleGroupId == "" {
		allrule, err := rulemappingmodel.GetAll()
		if err != nil {
			response.Response(c, 500, "网络故障", err)
			return
		}
		if len(allrule) == 0 {
			response.Response(c, 200, "未查询到数据", rspobj)
			return
		}
		var tempdetaillist []apitypes.RuleDetail
		for _, v := range allrule {
			var tempdetail apitypes.RuleDetail
			tempdetail.RuleGroupId = strconv.FormatInt(v.RuleGroupID, 10)
			tempdetail.RuleGroupName = v.RuleGroupName
			tempdetail.RuleDetailId = strconv.FormatInt(v.RuleContentDetailID, 10)
			tempdetail.RuleDetailName = v.RuleContentDetailName
			tempdetaillist = append(tempdetaillist, tempdetail)
		}
		rspobj.RuleDetailList = tempdetaillist
		response.Response(c, 200, "成功", rspobj)
		return
	} else {
		//返回规则组数据
		intruleid, _ := strconv.ParseInt(reqobj.RuleGroupId, 10, 64)
		rule_list, err := rulemappingmodel.GetByRuleGroupID(intruleid)
		if err != nil {
			response.Response(c, 500, "网络故障", err)
			return
		}
		if len(rule_list) == 0 {
			response.Response(c, 200, "未查询到数据", err)
			return
		}
		var tempdetaillist []apitypes.RuleDetail
		for _, v := range rule_list {
			var tempdetail apitypes.RuleDetail
			tempdetail.RuleGroupId = strconv.FormatInt(v.RuleGroupID, 10)
			tempdetail.RuleGroupName = v.RuleGroupName
			tempdetail.RuleDetailId = strconv.FormatInt(v.RuleContentDetailID, 10)
			tempdetail.RuleDetailName = v.RuleContentDetailName
			tempdetaillist = append(tempdetaillist, tempdetail)
		}
		rspobj.RuleDetailList = tempdetaillist
		response.Response(c, 200, "成功", rspobj)
		return
	}

}

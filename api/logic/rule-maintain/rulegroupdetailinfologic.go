package rule_maintain

import (
	"fmt"
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"sort"
	"strconv"
	"time"
)

func RuleGroupDetailInfo(c *gin.Context) {
	var (
		reqobj apitypes.RuleGroupDetailInfoRequest
		rspobj apitypes.RuleGroupDetailInfoResponse
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 200, "【1001】入参错误", err)
		return
	}
	rulemodel := models.NewRuleContent{}
	rulemappingmodel := models.RuleContentMapping{}
	ruledetailmodel := models.RuleContentDetail{}
	rulegroupid, _ := strconv.ParseInt(reqobj.RuleGroupID, 10, 64)
	//查询规则信息
	result, _ := rulemodel.FindByIDAndIsUse(rulegroupid, 1)
	if result == nil {
		response.Response(c, 200, "【2001】未查询到结果", result)
		return
	}
	strID := strconv.FormatInt(result.ID, 10)
	rspobj.ResultGroupID = strID
	rspobj.RuleCode = result.RuleCode
	rspobj.Version = fmt.Sprintf("%d.%d", result.MainVersion, result.SubVersion)
	strStatus := strconv.FormatInt(result.Status, 10)
	rspobj.Status = strStatus
	rspobj.RuleGroupName = result.RuleName
	strCategoryTreeID := strconv.FormatInt(result.CategoryTreeID, 10)
	rspobj.UseScene = result.UsageScenario
	rspobj.RuleGroupDescription = result.RuleDescription
	rspobj.RuleGroupContent = result.RuleContent
	if strCategoryTreeID == "0" {
		strCategoryTreeID = ""
	}
	rspobj.RuleGroupCategory = strCategoryTreeID
	strCategoryId := strconv.FormatInt(result.CategoryId, 10)
	if strCategoryId == "0" {
		strCategoryId = ""
	}
	rspobj.RuleCategoryId = strCategoryId
	//查询该规则对应的规则数据列表
	mappingcontentlist, _ := rulemappingmodel.FindByIDAndAllVersion(result.ID, result.MainVersion, result.SubVersion)
	if mappingcontentlist != nil {
		sort.Slice(mappingcontentlist, func(i, j int) bool {
			return mappingcontentlist[i].SerialNumber < mappingcontentlist[j].SerialNumber
		})
		var detaillist []apitypes.Rule_detail_content
		for _, v := range mappingcontentlist {
			dresult, err := ruledetailmodel.FindByDetailIdANDAllVersion(v.RuleContentDetailID, v.RuleDetailMainVersion, v.RuleDetailSubVersion)
			if err != nil {
				response.Response(c, 200, "无数据", err)
				return
			}
			var detail apitypes.Rule_detail_content
			detail.RuleName = dresult.RuleName
			detail.RuleDesc = dresult.RuleDescription
			detail.Version = fmt.Sprintf("%d.%d", dresult.MainVersion, dresult.SubVersion)
			t := time.Unix(dresult.UpdatedAt, 0)
			formattedTime := t.Format("2006-01-02 15:04:05")
			detail.Updated = formattedTime
			detail.Status = strconv.FormatInt(dresult.Status, 10)
			detail.DetailID = strconv.FormatInt(dresult.DetailID, 10)
			detail.DetailVersionID = strconv.FormatInt(dresult.DetailVersionID, 10)
			detail.Using = dresult.ISUse
			detail.RuleDetailContent = dresult.RuleContent
			detaillist = append(detaillist, detail)
		}
		rspobj.RuleDetailList = detaillist
	}

	response.Response(c, 200, "success", rspobj)
	return
}

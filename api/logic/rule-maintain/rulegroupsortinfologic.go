package rule_maintain

import (
	"fmt"
	"github.com/gin-gonic/gin"
	apitypes "kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
)

func RuleGroupSortInfo(c *gin.Context) {
	var (
		reqobj apitypes.RuleGroupSortInfoRequest
		rspobj apitypes.RuleGroupSortInfoResponse
	)
	if err := c.ShouldBind(&reqobj); err != nil {
		response.Response(c, 200, "【1001】入参错误", err)
		return
	}
	ruleTree := models.NewRuleTree{}
	ruleCategory := models.NewRuleCategory{}
	switch reqobj.SortType {
	case "0":
		treelist, _ := ruleTree.GetAllRuleTrees()
		if treelist == nil {
			response.Response(c, 200, "【2001】无数据", "")
			return
		}
		for _, v := range treelist {
			var listtemp apitypes.RuleGroupSortContetn

			listtemp.ID = fmt.Sprintf("%d", v.ID)
			listtemp.Name = v.RuleName
			rspobj.SortList = append(rspobj.SortList, listtemp)
		}
		response.Response(c, 200, "success", rspobj)
		return
	case "1":
		categorylist, _ := ruleCategory.GetAllRuleCategory()
		if categorylist == nil {
			response.Response(c, 200, "【2001】无数据", "")
			return
		}
		for _, v := range categorylist {
			var listtemp apitypes.RuleGroupSortContetn

			listtemp.ID = fmt.Sprintf("%d", v.ID)
			listtemp.Name = v.CategoryName
			rspobj.SortList = append(rspobj.SortList, listtemp)
		}
		response.Response(c, 200, "success", rspobj)
		return
	}
}

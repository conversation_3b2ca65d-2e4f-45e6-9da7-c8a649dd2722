package logic

import (
	"github.com/gin-gonic/gin"
	"kmbservice/api/rpcclient/codingSystem"
	"kmbservice/api/rpcclient/codingSystem/pb"
	"kmbservice/commons/response"
	"net/http"
)

func Test(c *gin.Context) {
	conn := codingSystem.RpcClient()
	if conn == nil {
		response.Response(c, 200, "test endpoint working (gRPC service unavailable)", nil)
		return
	}
	defer conn.Close()
	client := pb.NewRpcClient(conn)
	req := &pb.CodeSystemConvertRequest{
		SourceCodesystemId: 1234,
	}
	res, err := client.CodeSystemConvert(c, req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	response.Response(c, 200, "success", res)
}

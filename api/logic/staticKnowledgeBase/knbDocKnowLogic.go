package staticKnowledgeBase

import (
	"fmt"
	"kmbservice/api/types"
	"kmbservice/repository/models"
	"sort"
)

func KnbDocKnowLedge(req types.KnbDocKnowLedgeRequest) (types.KnbDocKnowLedgeResponse, error) {
	var rsp types.KnbDocKnowLedgeResponse
	tree := models.DasKnbDocTree{}
	treecontent := models.DasKnbDocContent{}
	result, err := tree.GetBySnowflakeID(req.ID)
	if err != nil {
		return rsp, err
	}
	if result.SnowflakeID == 0 {
		return rsp, nil
	}
	rsp.Contents = result.Contents
	rsp.TypeName = result.TypeName
	rsp.Description = result.Description
	parentID := result.SnowflakeID
	sonlist, err := tree.GetDrugSpecificationItems(parentID)
	if err != nil {
		return rsp, err
	}
	if len(sonlist) == 0 {
		return rsp, nil
	}
	sort.Slice(sonlist, byTypeNum(sonlist))
	for _, son := range sonlist {
		var nreson types.DasKnbDocTreeResponseContent
		nreson.Contents = son.Contents
		nreson.Name = son.TypeName

		contentraw, err := treecontent.GetByDocTeeToFind(son.SnowflakeID)
		if err != nil {
			return rsp, err
		}
		nreson.ID = fmt.Sprintf("%d", contentraw.DocTreeID)
		nreson.DocContent = contentraw.DocContent
		rsp.List = append(rsp.List, nreson)
	}
	return rsp, nil
}

func byTypeNum(sonlist []models.DasKnbDocTree) func(i, j int) bool {
	return func(i, j int) bool {
		return sonlist[i].TypeNum < sonlist[j].TypeNum
	}
}

//func KnbDocKnowLedge(c *gin.Context) {
//	var (
//		reqobj types.KnbDocKnowLedgeRequest
//		rsp types.KnbDocKnowLedgeResponse
//	)
//	err := c.ShouldBind(&reqobj)
//	if err != nil {
//		response.BadRequest(c, err)
//		return
//	}
//
//	tree := models.DasKnbDocTree{}
//	treecontent := models.DasKnbDocContent{}
//	result, _ := tree.GetBySnowflakeID(reqobj.ID)
//	if result.SnowflakeID == 0 {
//		response.NoRecord(c, rsp)
//		return
//	}
//	rsp.Contents = result.Contents
//	rsp.TypeName = result.TypeName
//	rsp.Description = result.Description
//	parent_id := result.SnowflakeID
//	//查询这条语句的所有子节点
//	sonlist, err := tree.GetDrugSpecificationItems(parent_id)
//	if err != nil {
//		response.InternalServerError(c, err)
//		return
//	}
//	if len(sonlist) == 0 {
//		response.NoRecord(c, rsp)
//		return
//	}
//	//按照type_num排序
//	sort.Slice(sonlist, byTypeNum(sonlist))
//	for _, son := range sonlist {
//		var nreson types.DasKnbDocTreeResponseContent
//		nreson.Contents = son.Contents
//		nreson.Name = son.TypeName
//		contentraw, err := treecontent.GetByDocTeeToFind(son.SnowflakeID)
//		if err != nil {
//			response.InternalServerError(c, err)
//			return
//		}
//		nreson.ID = fmt.Sprintf("%d", contentraw.DocTreeID)
//		nreson.DocContent = contentraw.DocContent
//		rsp.List = append(rsp.List, nreson)
//	}
//	response.Success(c, rsp)
//	return
//}

package staticKnowledgeBase

import (
	"github.com/gin-gonic/gin"
	"kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
	"sync"
)

func SynthesisSearch(c *gin.Context) {
	var (
		req types.SynthesisSearchRequest
		res types.SynthesisSearchResponse
		wg  sync.WaitGroup
	)
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "", err)
		return
	}
	searchers := []SearchTemplate{
		&DamInformationResourceSearch{model: models.DamInformationResource{}},
		&DasKnbDocTreeSearch{model: models.DasKnbDocTree{}},
		&DrugSpecificationSearch{model: models.DrugSpecification{}},
		&DmpMdmCMedicineSearch{model: models.DmpMdmCMedicine{}},
	}
	resultChan := make(chan func(), len(searchers))
	for _, searcher := range searchers {
		wg.Add(1)
		go func(searcher SearchTemplate) {
			defer wg.Done()
			result, _ := searcher.Search(req.Keyword)
			resultChan <- func() {
				switch searcher.(type) {
				case *DamInformationResourceSearch:
					res.DamInformationResourceList = append(res.DamInformationResourceList, result...)
				case *DasKnbDocTreeSearch:
					res.DasKnbDocList = append(res.DasKnbDocList, result...)
				case *DrugSpecificationSearch:
					res.DrugSpecificationList = append(res.DrugSpecificationList, result...)
				case *DmpMdmCMedicineSearch:
					res.DmpMdmCMedicineList = append(res.DmpMdmCMedicineList, result...)
				}
			}
		}(searcher)
	}
	go func() {
		wg.Wait()
		close(resultChan)
	}()
	for updateFunc := range resultChan {
		updateFunc()
	}
	response.Success(c, res)
}

type SearchTemplate interface {
	Search(keyword string) ([]types.SynthesisSearchResponseContent, error)
}

type DamInformationResourceSearch struct {
	model models.DamInformationResource
}

func (s *DamInformationResourceSearch) Search(keyword string) ([]types.SynthesisSearchResponseContent, error) {
	var result []types.SynthesisSearchResponseContent
	informationResourceList, err := s.model.FindByResourceNameLike(keyword)
	if err != nil {
		return result, err
	}

	for _, informationResource := range informationResourceList {
		result = append(result, types.SynthesisSearchResponseContent{
			ID:   strconv.FormatInt(informationResource.ResourceID, 10),
			Name: informationResource.ResourceName,
		})
	}
	return result, nil
}

type DasKnbDocTreeSearch struct {
	model models.DasKnbDocTree
}

func (s *DasKnbDocTreeSearch) Search(keyword string) ([]types.SynthesisSearchResponseContent, error) {
	var result []types.SynthesisSearchResponseContent
	dasKnbDoctreeList, err := s.model.FindByDescriptionLikeANDLevel(keyword, "4")
	if err != nil {
		return result, err
	}

	for _, dasKnbDoctree := range dasKnbDoctreeList {
		result = append(result, types.SynthesisSearchResponseContent{
			ID:   strconv.FormatInt(dasKnbDoctree.SnowflakeID, 10),
			Name: dasKnbDoctree.Description,
		})
	}
	return result, nil
}

type DrugSpecificationSearch struct {
	model models.DrugSpecification
}

func (s *DrugSpecificationSearch) Search(keyword string) ([]types.SynthesisSearchResponseContent, error) {
	var result []types.SynthesisSearchResponseContent
	drugList, err := s.model.GetSpecificationsByQueries([]string{keyword})
	if err != nil {
		return result, err
	}

	for _, drug := range drugList {
		result = append(result, types.SynthesisSearchResponseContent{
			ID:   strconv.FormatInt(drug.SpeciID, 10),
			Name: drug.Name,
		})
	}
	return result, nil
}

type DmpMdmCMedicineSearch struct {
	model models.DmpMdmCMedicine
}

func (s *DmpMdmCMedicineSearch) Search(keyword string) ([]types.SynthesisSearchResponseContent, error) {
	var result []types.SynthesisSearchResponseContent
	cmeList, err := s.model.FindByNameLike(keyword)
	if err != nil {
		return result, err
	}

	for _, cme := range cmeList {
		result = append(result, types.SynthesisSearchResponseContent{
			ID:   strconv.FormatInt(cme.TCMID, 10),
			Name: cme.Name,
		})
	}
	return result, nil
}

//package staticKnowledgeBase
//
//import (
//	"github.com/gin-gonic/gin"
//	"kmbservice/api/types"
//	"kmbservice/tools/response"
//	"kmbservice/repository/models"
//	"strconv"
//	"sync"
//)
//
//func SynthesisSearch(c *gin.Context) {
//	var (
//		req types.SynthesisSearchRequest
//		res types.SynthesisSearchResponse
//		wg  sync.WaitGroup
//	)
//	if err := c.ShouldBindJSON(&req); err != nil {
//		response.BadRequest(c, err)
//		return
//	}
//	keywordSlice := []string{req.Keyword}
//	checkInfoModel := models.DamInformationResource{}
//	dasKnbDocTreeModel := models.DasKnbDocTree{}
//	drugModel := models.DrugSpecification{}
//	cmeModel := models.DmpMdmCMedicine{}
//
//	wg.Add(1)
//	go func() {
//		defer wg.Done()
//		InformationResourceList, _ := checkInfoModel.FindByResourceNameLike(req.Keyword)
//		if len(InformationResourceList) != 0 {
//			for _, InformationResource := range InformationResourceList {
//				var temp types.SynthesisSearchResponseContent
//				strID := strconv.FormatInt(InformationResource.ResourceID, 10)
//				temp.ID = strID
//				temp.Name = InformationResource.ResourceName
//				res.DamInformationResourceList = append(res.DamInformationResourceList, temp)
//			}
//		}
//	}()
//
//	wg.Add(1)
//	go func() {
//		defer wg.Done()
//		dasKnbDoctreeList, _ := dasKnbDocTreeModel.FindByDescriptionLike(req.Keyword)
//		if len(dasKnbDoctreeList) != 0 {
//			for _, dasKnbDoctree := range dasKnbDoctreeList {
//				var temp types.SynthesisSearchResponseContent
//				strID := strconv.FormatInt(dasKnbDoctree.SnowflakeID, 10)
//				temp.ID = strID
//				temp.Name = dasKnbDoctree.Description
//				res.DasKnbDocList = append(res.DasKnbDocList, temp)
//			}
//		}
//	}()
//
//	wg.Add(1)
//	go func() {
//		defer wg.Done()
//		drugList, _ := drugModel.GetSpecificationsByQueries(keywordSlice)
//		if len(drugList) != 0 {
//			for _, drug := range drugList {
//				var temp types.SynthesisSearchResponseContent
//				strID := strconv.FormatInt(drug.SpeciID, 10)
//				temp.ID = strID
//				temp.Name = drug.Name
//				res.DrugSpecificationList = append(res.DrugSpecificationList, temp)
//			}
//		}
//	}()
//
//	wg.Add(1)
//	go func() {
//		defer wg.Done()
//		cmeList, _ := cmeModel.FindByNameLike(req.Keyword)
//		if len(cmeList) != 0 {
//			for _, cme := range cmeList {
//				var temp types.SynthesisSearchResponseContent
//				strID := strconv.FormatInt(cme.TCMID, 10)
//				temp.ID = strID
//				temp.Name = cme.Name
//				res.DmpMdmCMedicineList = append(res.DmpMdmCMedicineList, temp)
//			}
//		}
//	}()
//	wg.Wait()
//	response.Success(c, res)
//}

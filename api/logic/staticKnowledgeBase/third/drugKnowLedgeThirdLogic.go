package third

import (
	"github.com/gin-gonic/gin"
	"kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
)

func DrugKnowLedgeThird(c *gin.Context) {
	var (
		req types.DrugKnowLedgeThirdRequest
		res types.DrugKnowLedgeThirdResponse
	)
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "入参格式错误", err)
		return
	}
	medicineModel := models.CombinedMedicine{}
	dicMappingModel := models.DksKnbDicMapping{}
	serviceResourceUsage := models.DamServiceResourceUsage{}
	drugModel := models.DrugSpecification{}
	Item := models.DrugSpecificationItem{}
	drugSpecificationResourceRelationshipModel := models.DrugSpecificationResourceRelationship{}

	medicineInfo, err := medicineModel.GetCombinedMedicineByCodeAndHospital(req.Code, req.HospitalID)
	if err != nil {
		response.InternalServerError(c, err.Error(), res)
	}
	if medicineInfo == nil {
		response.NoRecord(c, "第三方编码未查询到数据", res)
		return
	}
	//查询己方的服务id
	mappingInfo, err := dicMappingModel.GetoneBySoidAndOrientation("st", medicineInfo.ID)
	if err != nil {
		response.InternalServerError(c, err.Error(), res)
		return
	}
	if mappingInfo == nil {
		response.NoRecord(c, "第三方编码与己方编码无映射关系", res)
		return
	}
	//查询服务对应的资源数据
	resourceInfo, err := serviceResourceUsage.GetRecordsByServiceID(mappingInfo.TargetDicID)
	if err != nil {
		response.InternalServerError(c, err.Error(), res)
		return
	}
	if len(resourceInfo) == 0 {
		response.NoRecord(c, "服务无对应资源", res)
		return
	}
	//查询服务对应的资源数据
	var resourceIdList []int64
	for _, resource := range resourceInfo {
		resourceIdList = append(resourceIdList, resource.ResourceID)
	}
	//查询映射关系
	relationList, _ := drugSpecificationResourceRelationshipModel.GetByResourceIDs(resourceIdList)
	if len(relationList) == 0 {
		response.NoRecord(c, "药品资源与药品知识库无对应关系", res)
		return
	}
	for _, relation := range relationList {
		var tempList types.DrugKnowLedgeThirdResList
		speciID := strconv.FormatInt(relation.SpeciID, 10)
		result, _ := drugModel.GetBySpeciID(speciID)
		if result == nil {
			response.NoRecord(c, "知识库无该药品详细信息", res)
			return
		}
		tempList.SpeciID = result.SpeciID
		tempList.Name = result.Name
		tempList.Content = result.Content
		tempList.Link = result.Link
		rlist, _ := Item.GetDrugSpecificationItems(relation.SpeciID)
		if len(rlist) == 0 {
			response.NoRecord(c, "知识库无该药品信息", res)
			return
		}
		for _, item := range rlist {
			var vv types.DrugSpecificationThirdItem
			vv.Name = item.Name
			vv.Jsonb = item.JSONB
			vv.Type = item.Type
			vv.ItemID = item.ItemID
			vv.Specification = item.Specification
			vv.SpeciID = item.SpeciID
			tempList.Items = append(tempList.Items, vv)
		}
		res.List = append(res.List, tempList)
	}
	if len(res.List) == 0 {
		response.NoRecord(c, "知识库无该药品信息", res)
		return
	}
	response.Success(c, res)
	return
}

package third

import (
	"github.com/gin-gonic/gin"
	"kmbservice/api/logic/staticKnowledgeBase"
	"kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
)

func PrescriptionKnowLedgeThird(c *gin.Context) {
	var (
		req types.PrescriptionKnowLedgeThirdRequest
		res types.PrescriptionKnowLedgeResponse
	)
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "入参格式错误", err)
		return
	}
	prescriptionModel := models.MdmCmedicinesPrescriptionRelationship{}
	instanceCodeInt, _ := strconv.ParseInt(req.InstanceCode, 10, 64)
	prescriptionMappingInfo, _ := prescriptionModel.GetByInstanceCode(instanceCodeInt)
	if prescriptionMappingInfo == nil {
		response.NoRecord(c, "该方剂与静态知识库无映射", res)
		return
	}
	//静态知识库id查询知识
	var tempReq types.PrescriptionKnowLedgeRequest
	tempReq.TcmId = strconv.FormatInt(prescriptionMappingInfo.TcmID, 10)
	result, _ := staticKnowledgeBase.GetPrescriptionKnowLedge(tempReq)
	res = result
	if res.TCMID == 0 {
		response.NoRecord(c, "知识库无该方剂信息", res)
		return
	}
	response.Success(c, res)
	return
}

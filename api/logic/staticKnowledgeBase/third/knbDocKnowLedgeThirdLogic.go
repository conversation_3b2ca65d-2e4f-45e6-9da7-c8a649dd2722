package third

import (
	"github.com/gin-gonic/gin"
	"kmbservice/api/apiclient"
	"kmbservice/api/logic/staticKnowledgeBase"
	"kmbservice/api/types"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"strconv"
)

func KnbDocKnowLedgeThird(c *gin.Context) {
	var (
		req types.KnbDocKnowLedgeThirdRequest
		res types.KnbDocKnowLedgeResponse
	)
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "入参格式错误", err)
		return
	}
	ddModel := models.DiseaseDiagnosisMapping{}
	ddkModel := models.DasKnbDocDiseaseRelationship{}
	//获取太翼诊断soid
	tysois, _ := apiclient.SingleCodeConvert("st", req.Code, "1831525289653388367")
	if tysois == "" {
		response.InternalServerError(c, "太翼soid获取失败", res)
		return
	}
	//查询诊断对应的病种
	tysoisInt, _ := strconv.ParseInt(tysois, 10, 64)
	mappingInfo, _ := ddModel.GetByDiagnosisSOID(tysoisInt)
	if mappingInfo == nil {
		response.NoRecord(c, "诊断与病种无关联信息", res)
		return
	}
	//标准病种查询映射
	ddkMappingInfo, _ := ddkModel.GetByDiseaseSoid(mappingInfo.DiseaseSOID)
	if ddkMappingInfo == nil {
		response.NoRecord(c, "标准病种与知识库病种无关联", res)
		return
	}
	//静态知识库病种id查询静态知识库
	var knowId types.KnbDocKnowLedgeRequest
	knowId.ID = strconv.FormatInt(ddkMappingInfo.DasKnbDocTreeID, 10)
	result, err := staticKnowledgeBase.KnbDocKnowLedge(knowId)
	if err != nil {
		response.InternalServerError(c, err.Error(), res)
		return
	}
	res = result
	if res.Contents == "" && res.Description == "" && len(res.List) == 0 && res.TypeName == "" {
		response.NoRecord(c, "知识库无该病种信息", res)
		return
	}
	response.Success(c, res)
	return
}

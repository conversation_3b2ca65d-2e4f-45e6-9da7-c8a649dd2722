package third

import (
	"kmbservice/api/logic/staticKnowledgeBase"
	"kmbservice/api/types"
	"kmbservice/repository/models"
	"strconv"
)

func GetLaboratoryTestExaminationThird(req types.LaboratoryTestExaminationThirdRequest) (rsp types.LaboratoryTestExaminationResponse, err error) {
	combinedMedicineModel := models.CombinedMedicine{}
	imageExamLabTestResourceRelationshipModel := models.DamImageExamLabTestResourceRelationship{}
	dicMappingModel := models.DksKnbDicMapping{}
	//查询第三方服务id
	medicineInfo, _ := combinedMedicineModel.GetCombinedMedicineByCodeAndHospital(req.BusinessCode, req.HospitalID)
	if medicineInfo == nil {
		return rsp, nil
	}
	//查询己方服务id
	mappingInfo, _ := dicMappingModel.GetoneBySoidAndOrientation("st", medicineInfo.ID)
	if mappingInfo == nil {
		return rsp, nil
	}
	//查询服务id与静态知识库映射表
	mappingResult, _ := imageExamLabTestResourceRelationshipModel.GetByServiceID(mappingInfo.TargetDicID)
	if mappingResult == nil {
		return rsp, nil
	}
	//查询静态知识库
	var tempReq types.LaboratoryTestExaminationRequest
	tempReq.ResourceID = strconv.FormatInt(mappingResult.ResourceID, 10)
	result, _ := staticKnowledgeBase.GetLaboratoryTestExamination(tempReq)
	rsp = result
	return rsp, nil
}

package staticKnowledgeBase

import (
	"kmbservice/api/types"
	"kmbservice/repository/models"
)

func GetDrugKnowLedge(req types.DrugKnowLedgeRequest) (rsp types.DrugKnowLedgeResponse, err error) {
	drugmodle := models.DrugSpecification{}
	Item := models.DrugSpecificationItem{}
	result, err := drugmodle.GetBySpeciID(req.SpeciID)
	if result == nil {
		return rsp, err
	}
	rsp.Name = result.Name
	rsp.Content = result.Content
	rsp.Link = result.Link
	rlist, err := Item.GetDrugSpecificationItems(result.SpeciID)
	if len(rlist) == 0 {
		return rsp, err
	}
	for _, item := range rlist {
		var vv types.DrugSpecificationItem
		vv.Name = item.Name
		vv.Jsonb = item.JSONB
		vv.Type = item.Type
		vv.Specification = item.Specification
		rsp.Items = append(rsp.Items, vv)
	}
	return rsp, err
}

package staticKnowledgeBase

import (
	"kmbservice/api/types"
	"kmbservice/repository/models"
)

func GetLaboratoryTestExamination(req types.LaboratoryTestExaminationRequest) (rsp types.LaboratoryTestExaminationResponse, err error) {
	checkInfoModel := models.DamInformationResource{}
	LaboratoryInfo, err := checkInfoModel.FindByResourceID(req.ResourceID)
	if err != nil {
		return rsp, err
	}
	if LaboratoryInfo == nil {
		return rsp, nil
	}
	rsp.ResourceName = LaboratoryInfo.ResourceName
	rsp.Contents = LaboratoryInfo.Content
	return rsp, nil
}

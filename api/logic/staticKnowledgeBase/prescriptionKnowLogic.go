package staticKnowledgeBase

import (
	"kmbservice/api/types"
	"kmbservice/repository/models"
)

func GetPrescriptionKnowLedge(req types.PrescriptionKnowLedgeRequest) (rsp types.PrescriptionKnowLedgeResponse, err error) {
	cmemodle := models.DmpMdmCMedicine{}
	cmeItem := models.DmpMdmCMedicineItem{}
	result, _ := cmemodle.GetByTCMID(req.TcmId)
	if result.TCMID == 0 {
		return rsp, nil
	}
	rsp.AdditionAndSubtraction = result.AdditionAndSubtraction
	rsp.ByStr = result.ByStr
	rsp.ClinicalApplication = result.ClinicalApplication
	rsp.Status = result.Status
	rsp.Name = result.Name
	rsp.DosageForm = result.DosageForm
	rsp.Effect = result.Effect
	rsp.Formulas = result.Formulas
	rsp.CreateUser = result.CreateUser
	rsp.TCMID = result.TCMID
	rsp.DecoctingMethod = result.DecoctingMethod
	rsp.EfficacyClassification = result.EfficacyClassification
	rsp.GroupOID = result.GroupOID
	cmelist, _ := cmeItem.GetDrugSpecificationItems(result.TCMID)
	if len(cmelist) == 0 {
		return rsp, nil
	}
	for _, item := range cmelist {
		var vv types.DmpMdmCMedicineItem
		vv.CName = item.CName
		vv.Unit = item.Unit
		vv.Compatibility = item.Compatibility
		vv.ItemID = item.ItemID
		vv.Qly = item.Qly
		vv.DescofDecoction = item.DescofDecoction
		vv.Description = item.Description
		vv.ItemOID = item.ItemOID
		vv.YpCode = item.YpCode
		rsp.ItemList = append(rsp.ItemList, vv)
	}
	return rsp, nil
}

package rule_exec

import (
	"errors"
	"github.com/bilibili/gengine/builder"
	"github.com/bilibili/gengine/context"
	"github.com/bilibili/gengine/engine"
	"kmbservice/api/types"
	"kmbservice/commons/enums"
	"kmbservice/commons/enums/enginEnums"
	"kmbservice/repository/models"
	"strconv"
)

func RuleExec(req types.RuleGengineRequest, initRsp types.RuleGengineResponse) (types.RuleGengineResponse, string, error) {
	ruleModel := models.NewRuleContent{}
	ruleIDList := req.InputData.Rule.RuleIDList
	patient := req.InputData.Patient
	medicineOrderList := req.InputData.WesternMedicineDoctorOrdersList
	examinationOrderList := req.InputData.ExaminationDoctorOrdersList
	surgicalOrderList := req.InputData.SurgicalDoctorOrdersList
	labExaminationOrderList := req.InputData.LaboratoryTestExaminationOrderList
	westernMedicineExecutionRecordList := req.InputData.WesternMedicineExecutionRecordList
	examinationExecutionRecordList := req.InputData.ExaminationExecutionRecordList
	labExaminationExecutionRecordList := req.InputData.LabExaminationExecutionRecordList
	surgicalExecutionRecordList := req.InputData.SurgicalExecutionRecordList
	diagnosisList := req.InputData.DiagnosisList
	allergicHistoryList := req.InputData.AllergicHistoryList
	laboratoryReportList := req.InputData.LaboratoryExaminationReport
	dataContext := context.NewDataContext()
	engineTranslatorInstance := enums.GetTranslator(enginEnums.EngineEnum)
	var allRulesContent string
	for _, r := range ruleIDList {
		rulID, _ := strconv.ParseInt(r, 10, 64)
		ruleInfo, _ := ruleModel.FindByIDAndIsUse(rulID, 1)
		if ruleInfo == nil {
			initRsp.Message = engineTranslatorInstance.GetRuleEnums(enginEnums.NotFoundRule)
			return initRsp, enginEnums.NotFoundRule, errors.New(engineTranslatorInstance.GetRuleEnums(enginEnums.NotFoundRule))
		}
		if ruleInfo.Status != 1 || ruleInfo.IsUse != 1 {
			initRsp.Message = engineTranslatorInstance.GetRuleEnums(enginEnums.RuleUnusable)
			return initRsp, enginEnums.RuleUnusable, errors.New(engineTranslatorInstance.GetRuleEnums(enginEnums.RuleUnusable))
		}
		ruleContent := ruleInfo.RuleContent
		if ruleContent == "" {
			initRsp.Message = engineTranslatorInstance.GetRuleEnums(enginEnums.RuleContentNoRecords)
			return initRsp, enginEnums.RuleContentNoRecords, errors.New(engineTranslatorInstance.GetRuleEnums(enginEnums.RuleContentNoRecords))
		}
		allRulesContent += ruleInfo.RuleContent + "\n\n"
		allRulesContent = RuleNameDeduplication(allRulesContent)
	}

	patient.CalculateAge()
	dataContext.Add(enginEnums.PatientDSL, patient)
	combineList := DSCombine(medicineOrderList, examinationOrderList, surgicalOrderList, labExaminationOrderList, westernMedicineExecutionRecordList,
		examinationExecutionRecordList, labExaminationExecutionRecordList, surgicalExecutionRecordList, diagnosisList, allergicHistoryList, laboratoryReportList)
	for _, combineDsl := range combineList {
		medicineOrder := combineDsl[enginEnums.WesternMedicineDoctorOrderSlice]
		examinationOrder := combineDsl[enginEnums.ExaminationOrderSlice]
		labTestExamOrder := combineDsl[enginEnums.LabTestExamDoctorOrderSlice]
		surgicalOrder := combineDsl[enginEnums.SurgicalDoctorOrderSlice]
		westernMedicineExecutionRecord := combineDsl[enginEnums.ExecutionRecordOfMedicineSlice]
		examinationExecutionRecord := combineDsl[enginEnums.ExecutionRecordOfExaminationOrderSlice]
		labExaminationExecutionRecord := combineDsl[enginEnums.ExecutionRecordOfLaboratoryExaminationOrderSlice]
		surgicalExecutionRecord := combineDsl[enginEnums.ExecutionRecordOfSurgicalOrderSlice]
		diagnosis := combineDsl[enginEnums.DiagnosisSlice]
		allergicHistory := combineDsl[enginEnums.AllergyHistorySlice]
		laboratoryReport := combineDsl[enginEnums.LaboratoryReportSlice]

		medicineStruct, _ := convertToWesternMedicineDoctorOrders(medicineOrder)
		examinationStruct, _ := convertExaminationOrder(examinationOrder)
		labTestExamOrderStruct, _ := convertToLaboratoryTestExamination(labTestExamOrder)
		surgicalOrderStruct, _ := convertSurgicalOrder(surgicalOrder)
		westernMedicineExecutionRecordStruct, _ := convertExecutionRecordOfMedicine(westernMedicineExecutionRecord)
		examinationExecutionRecordStruct, _ := convertExecutionRecordOfExaminationOrder(examinationExecutionRecord)
		labExaminationExecutionRecordStruct, _ := convertExecutionRecordOfLaboratoryExaminationOrder(labExaminationExecutionRecord)
		surgicalExecutionRecordStruce, _ := convertExecutionRecordOfSurgicalOrder(surgicalExecutionRecord)
		diagnosisStruct, _ := convertDiagnosis(diagnosis)
		allergicHistoryStruct, _ := convertAllergyHistory(allergicHistory)
		laboratoryReportStruct, _ := convertLaboratoryReport(laboratoryReport)

		medicineStruct.SetStore(medicineOrderList[0].Store)
		examinationStruct.SetStore(examinationOrderList[0].Store)
		labTestExamOrderStruct.SetStore(labExaminationOrderList[0].Store)
		surgicalOrderStruct.SetStore(surgicalOrderList[0].Store)
		westernMedicineExecutionRecordStruct.SetStore(westernMedicineExecutionRecordList[0].Store)
		examinationExecutionRecordStruct.SetStore(examinationExecutionRecordList[0].Store)
		labExaminationExecutionRecordStruct.SetStore(labExaminationExecutionRecordList[0].Store)
		surgicalExecutionRecordStruce.SetStore(surgicalExecutionRecordList[0].Store)
		diagnosisStruct.SetStore(diagnosisList[0].Store)
		allergicHistoryStruct.SetStore(allergicHistoryList[0].Store)
		laboratoryReportStruct.SetStore(laboratoryReportList[0].Store)

		dataContext.Add(enginEnums.WesternMedicineDoctorOrderDSL, medicineStruct)
		dataContext.Add(enginEnums.ExaminationOrderDSL, examinationStruct)
		dataContext.Add(enginEnums.LaboratoryExaminationOrderDSL, labTestExamOrderStruct)
		dataContext.Add(enginEnums.SurgicalOrderDSL, surgicalOrderStruct)
		dataContext.Add(enginEnums.ExecutionRecordOfMedicineOrderDSL, westernMedicineExecutionRecordStruct)
		dataContext.Add(enginEnums.ExecutionRecordOfExaminationOrderDSL, examinationStruct)
		dataContext.Add(enginEnums.ExecutionRecordOfLaboratoryExaminationOrderDSL, labExaminationExecutionRecordStruct)
		dataContext.Add(enginEnums.ExecutionRecordOfSurgicalOrderDSL, surgicalOrderStruct)
		dataContext.Add(enginEnums.Diagnosis, diagnosisStruct)
		dataContext.Add(enginEnums.AllergyHistory, allergicHistoryStruct)
		dataContext.Add(enginEnums.LaboratoryReport, laboratoryReportStruct)

		ruleBuilder := builder.NewRuleBuilder(dataContext)
		err := ruleBuilder.BuildRuleFromString(allRulesContent)
		if err != nil {
			initRsp.Message = engineTranslatorInstance.GetRuleEnums(enginEnums.FormattingError)
			initRsp.RecTime = GetNow()
			return initRsp, enginEnums.FormattingError, err
		}
		eng := engine.NewGengine()
		//err = eng.Execute(ruleBuilder, true)
		err = eng.ExecuteConcurrent(ruleBuilder)
		if err != nil {
			initRsp.Message = engineTranslatorInstance.GetRuleEnums(enginEnums.ExecFail)
			initRsp.RecTime = GetNow()
			return initRsp, enginEnums.ExecFail, err
		}
	}
	initRsp.OutputData = patient.Msg
	initRsp.Message = engineTranslatorInstance.GetRuleEnums(enginEnums.ExecSuccess)
	return initRsp, enginEnums.ExecSuccess, nil
	//switch ruleReq.RuleType {
	////病人与西药医嘱的关系
	//case "1":
	//	if len(medicineOrderList) != 0 {
	//		code, err := PatientAndWesternMedicineRuleExec(ruleContent, patient, medicineOrderList)
	//		if err != nil {
	//			initRsp.Message = err.Error()
	//			initRsp.RecTime = GetNow()
	//			return initRsp, code, err
	//		}
	//		initRsp.OutputData = patient.Msg
	//		initRsp.Message = engineTranslatorInstance.GetRuleEnums(enginEnums.ExecSuccess)
	//		initRsp.ResponseTime = GetNow()
	//		return initRsp, code, nil
	//	} else {
	//		initRsp.Message = engineTranslatorInstance.GetRuleEnums(enginEnums.MedicineDoctorOrdersListIsNil)
	//		initRsp.RecTime = GetNow()
	//		return initRsp, enginEnums.MedicineDoctorOrdersListIsNil, nil
	//	}
	//	//西药与实验室检查医嘱的关系
	//case "2":
	//	{
	//		if len(medicineOrderList) == 0 && len(labExaminationOrderList) == 0 {
	//			var builder strings.Builder
	//			builder.WriteString(engineTranslatorInstance.GetRuleEnums(enginEnums.LaboratoryTestExaminationDoctorOrdersListIsNil))
	//			builder.WriteString(";")
	//			builder.WriteString(engineTranslatorInstance.GetRuleEnums(enginEnums.MedicineDoctorOrdersListIsNil))
	//			initRsp.Message = builder.String()
	//			initRsp.RecTime = GetNow()
	//			return initRsp, enginEnums.MedicineDoctorOrdersListIsNil, nil
	//		} else if len(labExaminationOrderList) == 0 {
	//			initRsp.Message = engineTranslatorInstance.GetRuleEnums(enginEnums.LaboratoryTestExaminationDoctorOrdersListIsNil)
	//			initRsp.RecTime = GetNow()
	//			return initRsp, enginEnums.LaboratoryTestExaminationDoctorOrdersListIsNil, nil
	//		} else if len(medicineOrderList) == 0 {
	//			initRsp.Message = engineTranslatorInstance.GetRuleEnums(enginEnums.MedicineDoctorOrdersListIsNil)
	//			initRsp.RecTime = GetNow()
	//			return initRsp, enginEnums.MedicineDoctorOrdersListIsNil, nil
	//		}
	//		code, err := WesternMedicineAndLaboratoryTestRuleExec(ruleContent, patient, medicineOrderList, labExaminationOrderList)
	//		if err != nil {
	//			initRsp.Message = err.Error()
	//			initRsp.RecTime = GetNow()
	//			return initRsp, code, nil
	//		}
	//		initRsp.OutputData = patient.Msg
	//		initRsp.ResponseTime = GetNow()
	//	}
	//	//西药与影像学检查医嘱的关系
	//case "3":
	//	{
	//		if len(medicineOrderList) == 0 && len(imageDoctorOrders) == 0 {
	//			var builder strings.Builder
	//			builder.WriteString(engineTranslatorInstance.GetRuleEnums(enginEnums.ImageDoctorOrdersListIsNil))
	//			builder.WriteString(";")
	//			builder.WriteString(engineTranslatorInstance.GetRuleEnums(enginEnums.MedicineDoctorOrdersListIsNil))
	//			initRsp.Message = builder.String()
	//			initRsp.RecTime = GetNow()
	//			return initRsp, enginEnums.ImageDoctorOrdersListIsNil + "&" + enginEnums.MedicineDoctorOrdersListIsNil, nil
	//		} else if len(medicineOrderList) == 0 {
	//			initRsp.Message = engineTranslatorInstance.GetRuleEnums(enginEnums.MedicineDoctorOrdersListIsNil)
	//			initRsp.RecTime = GetNow()
	//			return initRsp, enginEnums.MedicineDoctorOrdersListIsNil, nil
	//		} else if len(imageDoctorOrders) == 0 {
	//			initRsp.Message = engineTranslatorInstance.GetRuleEnums(enginEnums.ImageDoctorOrdersListIsNil)
	//			initRsp.RecTime = GetNow()
	//			return initRsp, enginEnums.ImageDoctorOrdersListIsNil, nil
	//		}
	//		code, err := WesternMedicineAndImagingExaminationRuleExec(ruleContent, patient, medicineOrderList, imageDoctorOrders)
	//		if err != nil {
	//			initRsp.Message = err.Error()
	//			initRsp.RecTime = GetNow()
	//			return initRsp, code, nil
	//		}
	//		initRsp.OutputData = patient.Msg
	//		initRsp.ResponseTime = GetNow()
	//	}
	//
	//default:
	//	return initRsp, enginEnums.RuleTypeDoesNotExist, nil
	//
	//}
}

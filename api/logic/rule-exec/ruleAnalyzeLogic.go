package rule_exec

import (
	"github.com/bilibili/gengine/builder"
	"github.com/bilibili/gengine/context"
	"github.com/bilibili/gengine/engine"
	"kmbservice/commons/enums/enginEnums"
	"kmbservice/dsl"
	"kmbservice/dsl/doctororders"
)

// 患者与西药规则
func PatientAndWesternMedicineRuleExec(ruleContent string, patient *dsl.Patient, medicines []*doctororders.WesternMedicineDoctorOrders) (string, error) {
	var err error
	medicineStore := doctororders.NewMedicineDoctorOrdersStore()
	if len(medicines) > 0 {
		for _, med := range medicines {
			medicineStore.AddMedicalMap(med)
		}
	}
	medicineStore.AddMedicalList(medicines)
	for _, medicine := range medicines {
		medicine.SetStore(medicineStore)
		dataContext := context.NewDataContext()
		dataContext.Add("patient", patient)
		dataContext.Add("medicineOrder", medicine)
		ruleBuilder := builder.NewRuleBuilder(dataContext)
		err = ruleBuilder.BuildRuleFromString(ruleContent)
		if err != nil {
			return enginEnums.FormattingError, err
		}
		eng := engine.NewGengine()
		err = eng.Execute(ruleBuilder, true)
		if err != nil {
			return enginEnums.ExecFail, err
		}
	}
	return enginEnums.ExecSuccess, nil
}

// 西药与检验检查医嘱的关系
func WesternMedicineAndLaboratoryTestRuleExec(ruleContent string, patient *dsl.Patient,
	medicineList []*doctororders.WesternMedicineDoctorOrders,
	laboratoryList []*doctororders.LabTestExamDoctorOrder) (string, error) {
	var (
		err error
	)
	if patient != nil {
		dataContext := context.NewDataContext()
		dataContext.Add("patient", patient)
		dataContext.Add("medicines", medicineList[0])
		dataContext.Add("laboratory", laboratoryList[0])
		ruleBuilder := builder.NewRuleBuilder(dataContext)
		err = ruleBuilder.BuildRuleFromString(ruleContent)
		if err != nil {
			return enginEnums.FormattingError, err
		}
		eng := engine.NewGengine()
		err = eng.Execute(ruleBuilder, true)
		if err != nil {
			return enginEnums.ExecFail, err
		}
	}
	if len(medicineList) > 0 {
		medicineStore := doctororders.NewMedicineDoctorOrdersStore()
		for _, med := range medicineList {
			medicineStore.AddMedicalMap(med)
		}
		medicineStore.AddMedicalList(medicineList)
		for _, medicine := range medicineList {
			dataContext := context.NewDataContext()
			dataContext.Add("patient", patient)
			dataContext.Add("medicines", medicine)
			dataContext.Add("laboratory", laboratoryList[0])
			ruleBuilder := builder.NewRuleBuilder(dataContext)
			err = ruleBuilder.BuildRuleFromString(ruleContent)
			if err != nil {
				return enginEnums.FormattingError, err
			}
			eng := engine.NewGengine()
			err = eng.Execute(ruleBuilder, true)
			if err != nil {
				return enginEnums.ExecFail, err
			}
		}
	}
	if len(laboratoryList) > 0 {
		laboratoryStore := doctororders.NewLabTestExamDoctorOrderStore()
		for _, lab := range laboratoryList {
			laboratoryStore.AddLabTestExamOrderMap(lab)
		}
		laboratoryStore.AddLabTestExamOrderList(laboratoryList)
		for _, laboratory := range laboratoryList {
			dataContext := context.NewDataContext()
			dataContext.Add("patient", patient)
			dataContext.Add("medicines", medicineList[0])
			dataContext.Add("laboratory", laboratory)
			ruleBuilder := builder.NewRuleBuilder(dataContext)
			err = ruleBuilder.BuildRuleFromString(ruleContent)
			if err != nil {
				return enginEnums.FormattingError, err
			}
			eng := engine.NewGengine()
			err = eng.Execute(ruleBuilder, true)
			if err != nil {
				return enginEnums.ExecFail, err
			}
		}
	}
	return enginEnums.ExecSuccess, nil
}

// 西药与影像检查的关系
func WesternMedicineAndImagingExaminationRuleExec(ruleContent string, patient *dsl.Patient,
	medicineList []*doctororders.WesternMedicineDoctorOrders,
	imageList []*doctororders.ExaminationOrder) (string, error) {
	var err error
	medicineStore := doctororders.NewMedicineDoctorOrdersStore()
	imageStore := doctororders.NewExaminationOrderStore()
	for _, med := range medicineList {
		medicineStore.AddMedicalMap(med)
	}
	medicineStore.AddMedicalList(medicineList)
	for _, image := range imageList {
		imageStore.AddExaminationMap(image)
	}
	imageStore.AddExaminationList(imageList)

	combineDslList := CombineDsl(medicineList, imageList)
	for _, combineDsl := range combineDslList {
		medicine := combineDsl[0]
		image := combineDsl[1]
		medicineStruct, _ := convertToWesternMedicineDoctorOrders(medicine)
		imageStruct, _ := convertExaminationOrder(image)
		medicineStruct.SetStore(medicineStore)
		imageStruct.SetStore(imageStore)
		dataContext := context.NewDataContext()
		dataContext.Add("patient", patient)
		dataContext.Add("medicineOrder", medicineStruct)
		dataContext.Add("ExaminationOrder", imageStruct)
		ruleBuilder := builder.NewRuleBuilder(dataContext)
		err = ruleBuilder.BuildRuleFromString(ruleContent)
		if err != nil {
			return enginEnums.FormattingError, err
		}
		eng := engine.NewGengine()
		err = eng.Execute(ruleBuilder, true)
		if err != nil {
			return enginEnums.ExecFail, err
		}
	}
	return enginEnums.ExecSuccess, nil
}

//func WesternMedicineAndImagingExaminationRuleExec(ruleContent string, patient *dsl.Patient, medicineList []*doctororders.WesternMedicineDoctorOrders, imageList []*doctororders.ImageDoctorOrders) error {
//	var err error
//	var mu sync.Mutex  // 保护 err 的并发访问
//	medicineStore := doctororders.NewMedicineDoctorOrdersStore()
//	imageStore := doctororders.NewImageDoctorOrdersStore()
//
//	// 将 medicineList 和 imageList 加入各自的 Store
//	for _, med := range medicineList {
//		medicineStore.AddMedicalMap(med)
//	}
//	medicineStore.AddMedicalList(medicineList)
//	for _, image := range imageList {
//		imageStore.AddImageMap(image)
//	}
//	imageStore.AddImageList(imageList)
//
//	// Combine Dsl 列表
//	combineDslList := CombineDsl(medicineList, imageList)
//
//	// 创建一个等待组，用于等待所有 goroutine 完成
//	var wg sync.WaitGroup
//
//	// 使用 ants 创建一个 goroutine 池，限制最大并发数量
//	pool, _ := ants.NewPoolWithFunc(10, func(i interface{}) {
//		defer wg.Done()
//
//		// 类型断言，将传入的 i 转换回 combineDsl
//		combineDsl := i.([]interface{})
//		medicine := combineDsl[0]
//		image := combineDsl[1]
//
//		// 转换为具体的结构体
//		medicineStruct, _ := convertToWesternMedicineDoctorOrders(medicine)
//		imageStruct, _ := convertImagesExamination(image)
//
//		// 设置 Store
//		medicineStruct.SetStore(medicineStore)
//		imageStruct.SetStore(imageStore)
//
//		// 创建 DataContext
//		dataContext := context.NewDataContext()
//		dataContext.Add("patient", patient)
//		dataContext.Add("medicines", medicineStruct)
//		dataContext.Add("images", imageStruct)
//
//		// 构建规则
//		ruleBuilder := builder.NewRuleBuilder(dataContext)
//		buildErr := ruleBuilder.BuildRuleFromString(ruleContent)
//		if buildErr != nil {
//			// 锁定对 err 的访问
//			mu.Lock()
//			err = buildErr
//			mu.Unlock()
//			return
//		}
//
//		// 执行规则引擎
//		eng := engine.NewGengine()
//		execErr := eng.Execute(ruleBuilder, true)
//		if execErr != nil {
//			// 锁定对 err 的访问
//			mu.Lock()
//			err = execErr
//			mu.Unlock()
//			return
//		}
//	})
//	defer pool.Release()  // 使用完 goroutine 池后释放资源
//
//	// 遍历 combineDslList，使用 ants 池处理并发任务
//	for _, combineDsl := range combineDslList {
//		wg.Add(1)
//		// 将 combineDsl 提交到 ants 池中
//		_ = pool.Invoke(combineDsl)
//	}
//
//	// 等待所有 goroutine 完成
//	wg.Wait()
//
//	return err
//}

//combineDslList := CombineDsl(medicineList, laboratoryList)
//for _, combineDsl := range combineDslList {
//medicine := combineDsl[0]
//laboratory := combineDsl[1]
//medicineStruct, _ := convertToWesternMedicineDoctorOrders(medicine)
//laboratoryStruct, _ := convertToLaboratoryTestExamination(laboratory)
//medicineStruct.SetStore(medicineStore)
//laboratoryStruct.SetStore(laboratoryStore)
//dataContext := context.NewDataContext()
//dataContext.Add("patient", patient)
//dataContext.Add("medicines", medicineStruct)
//dataContext.Add("laboratory", laboratoryStruct)
//ruleBuilder := builder.NewRuleBuilder(dataContext)
//err = ruleBuilder.BuildRuleFromString(ruleContent)
//if err != nil {
//return err
//}
//eng := engine.NewGengine()
//err = eng.Execute(ruleBuilder, true)
//if err != nil {
//return err
//}
//}

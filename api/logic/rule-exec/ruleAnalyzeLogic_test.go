package rule_exec

import (
	"fmt"
	"kmbservice/dsl"
	"kmbservice/dsl/doctororders"
	"testing"
)

// 患者与西药
func TestPatientAndWesternMedicineRuleExec(t *testing.T) {
	ruleContent := `
	rule "rule test" "i can do it"  salience 0
	begin
	       if medicineOrder.MedicationPrescriptionItem == "1850713561494190754" {
	           patient.AddMessage("001","002","患者与药品测试判断结果")
	       }
	end
	`

	//	ruleContent := `
	//rule "患者年龄小于16岁，阿司匹林慎用。" salience 1
	//begin
	//    if ((patient.Age<16.000000) && (medicineOrder.MedicationPrescriptionItem=="1850713561494190754")) {
	//         patient.AddMessage("02","患者年龄小于16岁，阿司匹林慎用。","00001")
	//    }
	//end
	//
	//rule "阿司匹林增加老年人出血风险" salience 2
	//begin
	//    if ((patient.Age>70.000000) && (medicineOrder.MedicationPrescriptionItem=="1850713561494190754")) {
	//         patient.AddMessage("05","阿司匹林增加老年人出血风险","00001")
	//    }
	//end
	//`
	var patient *dsl.Patient
	patient = new(dsl.Patient)
	patient.PatientBirthday = "2011-10-23"

	medicines := []*doctororders.WesternMedicineDoctorOrders{
		{
			MedicationPrescriptionItem: "1850713561494190755",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
	}
	code, err := PatientAndWesternMedicineRuleExec(ruleContent, patient, medicines)
	if err != nil {
		fmt.Println(code)
		fmt.Println(err)
	}
}

// 西药与实验室检查
func TestWesternMedicineAndLaboratoryTestRuleExec(t *testing.T) {
	ruleContent := `
rule "rule test" "i can do it"  salience 0
begin
        if patient.Age < 20 && medicines.MedicalID == "89757" && laboratory.TestItemID == "109095"{
            patient.AddMessage("001","002","西药与实验室检查测试结果")
        }
end
`
	var patient *dsl.Patient
	patient = new(dsl.Patient)
	patient.Age = 18
	patient.PatientGender = "1"

	medicineList := []*doctororders.WesternMedicineDoctorOrders{
		{
			MedicationPrescriptionItem: "89756",
		},
		{
			MedicationPrescriptionItem: "89757",
		},
		{
			MedicationPrescriptionItem: "89758",
		},
	}
	laboratoryList := []*doctororders.LabTestExamDoctorOrder{
		{
			LaboratoryTestOrder: "109091",
		},
		{
			LaboratoryTestOrder: "109091",
		},
		{
			LaboratoryTestOrder: "109091",
		},
		{
			LaboratoryTestOrder: "109091",
		},
	}
	code, err := WesternMedicineAndLaboratoryTestRuleExec(ruleContent, patient, medicineList, laboratoryList)
	if err != nil {
		fmt.Println(code)
		fmt.Println(err)
	}
}

// 西药与影像学检查
func TestWesternMedicineAndImagingExaminationRuleExec(t *testing.T) {
	//	ruleContent := `
	//rule "rule test" "i can do it"  salience 0
	//begin
	//        if patient.Age < 20 && medicines.MedicalID == "89757" && images.ImageID == "89759"{
	//            patient.AddMessage("001","002","西药与影像学检查测试结果")
	//        }
	//end
	//`
	var patient *dsl.Patient
	patient = new(dsl.Patient)
	patient.Age = 18
	patient.PatientGender = "1"
	//medicineList := []*doctororders.WesternMedicineDoctorOrders{
	//	{
	//		MedicationPrescriptionItem: "89756",
	//		DosageID:                   "DCFD001",
	//	},
	//}
	//imageList := []*doctororders.ExaminationOrder{
	//	{
	//		ExaminationOrderItem: "89756",
	//	},
	//}
	//code, err := WesternMedicineAndLaboratoryTestRuleExec(ruleContent, patient, medicineList, )
	//if err != nil {
	//	fmt.Println(code)
	//	fmt.Println(err)
	//}
}

//channel测试
func TestChannel(t *testing.T) {
	channel := make(chan string)

}

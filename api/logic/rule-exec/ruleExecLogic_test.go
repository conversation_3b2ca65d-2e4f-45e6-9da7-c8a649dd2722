package rule_exec

import (
	"fmt"
	"kmbservice/api/types"
	"kmbservice/dsl"
	"kmbservice/dsl/doctororders"
	"testing"
)

func TestRuleExec(t *testing.T) {
	var (
		req     types.RuleGengineRequest
		initRsp types.RuleGengineResponse
		patient *dsl.Patient
		rule    *types.Rule
	)
	req.SystemCodeMapping = false
	req.CorpID = "1"
	rule = new(types.Rule)
	patient = new(dsl.Patient)
	patient.PatientBirthday = "2011-10-23"
	rule.RuleVersionID = "1850865082639055915"
	medicines := []*doctororders.WesternMedicineDoctorOrders{
		{
			MedicationPrescriptionItem: "1850713561494190754",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
		{
			MedicationPrescriptionItem: "1850713561494190790",
		},
	}
	examinationDoctorOrdersList := []*doctororders.ExaminationOrder{
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
		{
			ExaminationOrderItem: "345345",
		},
	}
	surgicalDoctorOrdersList := []*doctororders.SurgicalDoctorOrder{{}}

	laboratoryList := []*doctororders.LabTestExamDoctorOrder{
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
		{
			LabExamID: "109091",
			LabTypeID: "红细胞",
		},
	}
	input := types.InputData{
		Rule:                               rule,
		Patient:                            patient,
		WesternMedicineDoctorOrdersList:    medicines,
		ExaminationDoctorOrdersList:        examinationDoctorOrdersList,
		LaboratoryTestExaminationOrderList: laboratoryList,
		SurgicalDoctorOrdersList:           surgicalDoctorOrdersList,
	}
	req.InputData = &input
	result, code, _ := RuleExec(req, initRsp)
	fmt.Printf("执行结果：%s\n错误码：%s\n", result, code)
}

func TestCombineDsl(t *testing.T) {
	slice1 := []LabDoctorOrders{
		{LanID: "opop", LabName: "Lab Test A"},
		{LanID: "9090", LabName: "Lab Test B"},
	}
	slice2 := []WesternMedicineDoctorOrders{
		{MedicationPrescriptionItem: "med3", MedicalName: "Drug C"},
		{MedicationPrescriptionItem: "med4", MedicalName: "Drug D"},
	}
	slice3 := []ExamOrders{
		{Dosage: "Dose1"},
		{Dosage: "Dose2"},
	}
	combinations := Combine(slice1, slice2, slice3)
	for _, combo := range combinations {
		fmt.Println(combo[0])
		fmt.Println(combo[1])
		fmt.Println(combo[2])
		fmt.Println(combo[3])
	}
}

func Combine(slices ...interface{}) [][]interface{} {
	var result [][]interface{}
	if len(slices) == 0 {
		return result
	}
	interfaceSlices := make([][]interface{}, len(slices))
	for i, s := range slices {
		switch s := s.(type) {
		case []LabDoctorOrders:
			for _, item := range s {
				interfaceSlices[i] = append(interfaceSlices[i], item)
			}
		case []WesternMedicineDoctorOrders:
			for _, item := range s {
				interfaceSlices[i] = append(interfaceSlices[i], item)
			}
		case []ExamOrders:
			for _, item := range s {
				interfaceSlices[i] = append(interfaceSlices[i], item)
			}
		default:
			fmt.Println("Unsupported type:", s)
			return nil
		}
	}
	combinationHelper(interfaceSlices, []interface{}{}, 0, &result)
	return result
}

//func combinationHelper(slices [][]interface{}, current []interface{}, depth int, result *[][]interface{}) {
//	if depth == len(slices) {
//		combination := make([]interface{}, len(current))
//		copy(combination, current)
//		*result = append(*result, combination)
//		return
//	}
//
//	for i := 0; i < len(slices[depth]); i++ {
//		element := slices[depth][i]
//		combinationHelper(slices, append(current, element), depth+1, result)
//	}
//}

type LabDoctorOrders struct {
	LanID   string
	LabName string
}

type WesternMedicineDoctorOrders struct {
	MedicationPrescriptionItem string
	MedicalName                string
}

type ExamOrders struct {
	Dosage string
}

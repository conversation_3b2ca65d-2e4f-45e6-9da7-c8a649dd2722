package rule_exec

import (
	"encoding/json"
	"fmt"
	"kmbservice/api/types"
	"kmbservice/dsl"
	"kmbservice/dsl/doctororders"
	"kmbservice/dsl/executionrecord"
	"kmbservice/dsl/medicalhistory"
	"kmbservice/dsl/report"
	"regexp"
	"strings"
	"time"
)

func GetNow() string {
	ret := time.Now().Format("2006-01-02 15:04:05.000")
	return ret
}

func CombineDsl[T, U any](dsl1 []T, dsl2 []U) [][2]interface{} {
	var result [][2]interface{}
	for _, e1 := range dsl1 {
		for _, e2 := range dsl2 {
			pair := [2]interface{}{e1, e2}
			result = append(result, pair)
		}
	}
	return result
}

//func convertToWesternMedicineDoctorOrders(data interface{}) (*doctororders.WesternMedicineDoctorOrders, error) {
//	bytes, err := json.Marshal(data)
//	if err != nil {
//		return nil, fmt.Errorf("无法将数据序列化为JSON: %v", err)
//	}
//	var orders *doctororders.WesternMedicineDoctorOrders
//	if err := json.Unmarshal(bytes, &orders); err != nil {
//		return nil, fmt.Errorf("无法将数据反序列化为 WesternMedicineDoctorOrders: %v", err)
//	}
//	return orders, nil
//}
//
//func convertExaminationOrder(data interface{}) (*doctororders.ExaminationOrder, error) {
//	bytes, err := json.Marshal(data)
//	if err != nil {
//		return nil, fmt.Errorf("无法将数据序列化为JSON: %v", err)
//	}
//	var orders *doctororders.ExaminationOrder
//	if err := json.Unmarshal(bytes, &orders); err != nil {
//		return nil, fmt.Errorf("无法将数据反序列化为 ImageDoctorOrders: %v", err)
//	}
//	return orders, nil
//}
//
//// 手术医嘱转换
//func convertSurgicalOrder(data interface{}) (*doctororders.SurgicalDoctorOrder, error) {
//	bytes, err := json.Marshal(data)
//	if err != nil {
//		return nil, fmt.Errorf("无法将数据序列化为JSON: %v", err)
//	}
//	var orders *doctororders.SurgicalDoctorOrder
//	if err := json.Unmarshal(bytes, &orders); err != nil {
//		return nil, fmt.Errorf("无法将数据反序列化为 SurgicalDoctorOrders: %v", err)
//	}
//	return orders, nil
//}
//
//func convertToLaboratoryTestExamination(data interface{}) (*doctororders.LabTestExamDoctorOrder, error) {
//	bytes, err := json.Marshal(data)
//	if err != nil {
//		return nil, fmt.Errorf("无法将数据序列化为JSON: %v", err)
//	}
//	var orders *doctororders.LabTestExamDoctorOrder
//	if err := json.Unmarshal(bytes, &orders); err != nil {
//		return nil, fmt.Errorf("无法将数据反序列化为 LaboratoryTestExaminationDoctorOrders: %v", err)
//	}
//	return orders, nil
//}

// 通用转换函数
func convertOrder(data interface{}, target interface{}) error {
	bytes, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("无法将数据序列化为JSON: %v", err)
	}
	if err := json.Unmarshal(bytes, target); err != nil {
		return fmt.Errorf("无法将数据反序列化为目标类型: %v", err)
	}
	return nil
}

// 将不同类型的医嘱转换函数重用通用转换函数
func convertToWesternMedicineDoctorOrders(data interface{}) (*doctororders.WesternMedicineDoctorOrders, error) {
	var orders doctororders.WesternMedicineDoctorOrders
	if err := convertOrder(data, &orders); err != nil {
		return nil, fmt.Errorf("无法将数据反序列化为 WesternMedicineDoctorOrders: %v", err)
	}
	return &orders, nil
}

func convertExaminationOrder(data interface{}) (*doctororders.ExaminationOrder, error) {
	var orders doctororders.ExaminationOrder
	if err := convertOrder(data, &orders); err != nil {
		return nil, fmt.Errorf("无法将数据反序列化为 ExaminationOrder: %v", err)
	}
	return &orders, nil
}

func convertSurgicalOrder(data interface{}) (*doctororders.SurgicalDoctorOrder, error) {
	var orders doctororders.SurgicalDoctorOrder
	if err := convertOrder(data, &orders); err != nil {
		return nil, fmt.Errorf("无法将数据反序列化为 SurgicalDoctorOrder: %v", err)
	}
	return &orders, nil
}

func convertToLaboratoryTestExamination(data interface{}) (*doctororders.LabTestExamDoctorOrder, error) {
	var orders doctororders.LabTestExamDoctorOrder
	if err := convertOrder(data, &orders); err != nil {
		return nil, fmt.Errorf("无法将数据反序列化为 LabTestExamDoctorOrder: %v", err)
	}
	return &orders, nil
}

func convertExecutionRecordOfMedicine(data interface{}) (*executionrecord.WesternMedicineExecutionRecord, error) {
	var orders executionrecord.WesternMedicineExecutionRecord
	if err := convertOrder(data, &orders); err != nil {
		return nil, fmt.Errorf("无法将数据反序列化为 LabTestExamDoctorOrder: %v", err)
	}
	return &orders, nil
}

func convertExecutionRecordOfExaminationOrder(data interface{}) (*executionrecord.ExaminationExecutionRecord, error) {
	var orders executionrecord.ExaminationExecutionRecord
	if err := convertOrder(data, &orders); err != nil {
		return nil, fmt.Errorf("无法将数据反序列化为 LabTestExamDoctorOrder: %v", err)
	}
	return &orders, nil
}

func convertExecutionRecordOfLaboratoryExaminationOrder(data interface{}) (*executionrecord.LabExaminationExecutionRecord, error) {
	var orders executionrecord.LabExaminationExecutionRecord
	if err := convertOrder(data, &orders); err != nil {
		return nil, fmt.Errorf("无法将数据反序列化为 LabTestExamDoctorOrder: %v", err)
	}
	return &orders, nil
}

func convertExecutionRecordOfSurgicalOrder(data interface{}) (*executionrecord.SurgicalExecutionRecord, error) {
	var orders executionrecord.SurgicalExecutionRecord
	if err := convertOrder(data, &orders); err != nil {
		return nil, fmt.Errorf("无法将数据反序列化为 LabTestExamDoctorOrder: %v", err)
	}
	return &orders, nil
}

func convertDiagnosis(data interface{}) (*dsl.Diagnosis, error) {
	var orders dsl.Diagnosis
	if err := convertOrder(data, &orders); err != nil {
		return nil, fmt.Errorf("无法将数据反序列化为 LabTestExamDoctorOrder: %v", err)
	}
	return &orders, nil
}

func convertAllergyHistory(data interface{}) (*medicalhistory.AllergicHistory, error) {
	var orders medicalhistory.AllergicHistory
	if err := convertOrder(data, &orders); err != nil {
		return nil, fmt.Errorf("无法将数据反序列化为 LabTestExamDoctorOrder: %v", err)
	}
	return &orders, nil
}

func convertLaboratoryReport(data interface{}) (*report.LaboratoryExaminationReport, error) {
	var orders report.LaboratoryExaminationReport
	if err := convertOrder(data, &orders); err != nil {
		return nil, fmt.Errorf("无法将数据反序列化为 LabTestExamDoctorOrder: %v", err)
	}
	return &orders, nil
}

func convertRuleRspMsg(data interface{}) (*types.Message, error) {
	bytes, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("无法将数据序列化为JSON: %v", err)
	}
	var orders *types.Message
	if err := json.Unmarshal(bytes, &orders); err != nil {
		return nil, fmt.Errorf("无法将数据反序列化为 Msg: %v", err)
	}
	return orders, nil
}

// 对象内容合并
func DSCombine(slices ...interface{}) [][]interface{} {
	var result [][]interface{}
	if len(slices) == 0 {
		return result
	}
	interfaceSlices := make([][]interface{}, len(slices))
	for i, s := range slices {
		switch s := s.(type) {
		case []*doctororders.WesternMedicineDoctorOrders: //0
			for _, item := range s {
				interfaceSlices[i] = append(interfaceSlices[i], item)
			}
		case []*doctororders.ExaminationOrder: //1
			for _, item := range s {
				interfaceSlices[i] = append(interfaceSlices[i], item)
			}
		case []*doctororders.LabTestExamDoctorOrder: //2
			for _, item := range s {
				interfaceSlices[i] = append(interfaceSlices[i], item)
			}
		case []*doctororders.SurgicalDoctorOrder: //3
			for _, item := range s {
				interfaceSlices[i] = append(interfaceSlices[i], item)
			}
		case []*executionrecord.WesternMedicineExecutionRecord: //4
			for _, item := range s {
				interfaceSlices[i] = append(interfaceSlices[i], item)
			}
		case []*executionrecord.ExaminationExecutionRecord: //5
			for _, item := range s {
				interfaceSlices[i] = append(interfaceSlices[i], item)
			}
		case []*executionrecord.LabExaminationExecutionRecord: //6
			for _, item := range s {
				interfaceSlices[i] = append(interfaceSlices[i], item)
			}
		case []*executionrecord.SurgicalExecutionRecord: //7
			for _, item := range s {
				interfaceSlices[i] = append(interfaceSlices[i], item)
			}
		case []*dsl.Diagnosis: //8
			for _, item := range s {
				interfaceSlices[i] = append(interfaceSlices[i], item)
			}
		case []*medicalhistory.AllergicHistory: //9
			for _, item := range s {
				interfaceSlices[i] = append(interfaceSlices[i], item)
			}
		case []*report.LaboratoryExaminationReport: //10
			for _, item := range s {
				interfaceSlices[i] = append(interfaceSlices[i], item)
			}
		default:
			return nil
		}
	}
	combinationHelper(interfaceSlices, []interface{}{}, 0, &result)
	return result
}

func combinationHelper(slices [][]interface{}, current []interface{}, depth int, result *[][]interface{}) {
	if depth == len(slices) {
		combination := make([]interface{}, len(current))
		copy(combination, current)
		*result = append(*result, combination)
		return
	}
	for i := 0; i < len(slices[depth]); i++ {
		element := slices[depth][i]
		combinationHelper(slices, append(current, element), depth+1, result)
	}
}

func RuleNameDeduplication(rules string) string {
	ruleNameRegex := regexp.MustCompile(`rule "([^"]+)"`)
	ruleNameCount := make(map[string]int)
	var modifiedRules []string
	lines := strings.Split(rules, "\n")
	for _, line := range lines {
		matches := ruleNameRegex.FindStringSubmatch(line)
		if len(matches) > 0 {
			ruleName := matches[1]
			if ruleNameCount[ruleName] > 0 {
				uniqueRuleName := fmt.Sprintf("%s%d", ruleName, ruleNameCount[ruleName]+1)
				line = strings.Replace(line, fmt.Sprintf(`"%s"`, ruleName), fmt.Sprintf(`"%s"`, uniqueRuleName), 1)
			}
			ruleNameCount[ruleName]++
		}
		modifiedRules = append(modifiedRules, line)
	}
	return strings.Join(modifiedRules, "\n")
}

package response

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"strings"
)

type Body struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data,omitempty"`
}

type ResponseS interface {
	Execute(c *gin.Context, code int, msg string, data interface{})
}

type successResponse struct{}

func (s *successResponse) Execute(c *gin.Context, code int, msg string, data interface{}) {
	c.JSON(http.StatusOK, Body{
		Code: code,
		Msg:  msg,
		Data: data,
	})
}

type noRecordResponse struct{}

func (n *noRecordResponse) Execute(c *gin.Context, code int, msg string, data interface{}) {
	c.JSON(http.StatusOK, Body{
		Code: code,
		Msg:  msg,
		Data: data,
	})
}

type badRequestResponse struct{}

func (b *badRequestResponse) Execute(c *gin.Context, code int, msg string, data interface{}) {
	c.<PERSON>(http.StatusOK, Body{
		Code: code,
		Msg:  msg,
		Data: data,
	})
}

type internalErrorResponse struct{}

func (i *internalErrorResponse) Execute(c *gin.Context, code int, msg string, data interface{}) {
	c.JSON(http.StatusOK, Body{
		Code: code,
		Msg:  msg,
		Data: data,
	})
}

type DefaultResponse struct{}

func (d *DefaultResponse) Execute(c *gin.Context, code int, msg string, data interface{}) {
	c.JSON(http.StatusInternalServerError, Body{
		Code: code,
		Msg:  msg,
		Data: data,
	})
}

func AutoResponse(c *gin.Context, code int, msg string, data interface{}) {
	var res ResponseS
	switch code {
	case http.StatusOK:
		res = &successResponse{}
	case http.StatusNoContent:
		res = &noRecordResponse{}
	case http.StatusBadRequest:
		res = &badRequestResponse{}
	case http.StatusInternalServerError:
		res = &internalErrorResponse{}
	default:
		res = &DefaultResponse{}
	}
	res.Execute(c, code, msg, data)
}

func Success(c *gin.Context, data interface{}) {
	AutoResponse(c, http.StatusOK, "Success", data)
}

func NoRecord(c *gin.Context, msg string, data interface{}) {
	if msg == "" {
		AutoResponse(c, http.StatusNoContent, "No record", data)
	} else {
		var builder strings.Builder
		builder.WriteString("No record")
		builder.WriteString("【")
		builder.WriteString(msg)
		builder.WriteString("】")
		message := builder.String()
		AutoResponse(c, http.StatusNoContent, message, data)
	}
}

func BadRequest(c *gin.Context, msg string, data interface{}) {
	if msg == "" {
		AutoResponse(c, http.StatusBadRequest, "BadRequest", data)
	} else {
		var builder strings.Builder
		builder.WriteString("BadRequest")
		builder.WriteString("【")
		builder.WriteString(msg)
		builder.WriteString("】")
		message := builder.String()
		AutoResponse(c, http.StatusBadRequest, message, data)
	}
}

func InternalServerError(c *gin.Context, msg string, data interface{}) {
	if msg == "" {
		AutoResponse(c, http.StatusInternalServerError, "Internal Server Error", data)
	} else {
		var builder strings.Builder
		builder.WriteString("Internal Server Error")
		builder.WriteString("【")
		builder.WriteString(msg)
		builder.WriteString("】")
		message := builder.String()
		AutoResponse(c, http.StatusInternalServerError, message, data)
	}
}

package response

import (
	"github.com/gin-gonic/gin"
	"net/http"
)

type Body3 struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data,omitempty"`
}

type Body2 struct {
	Code   int         `json:"code"`
	Msg    string      `json:"msg"`
	Result interface{} `json:"result,omitempty"`
}

func Response(c *gin.Context, code int, msg string, data interface{}) {
	c.JSON(http.StatusOK, Body3{
		Code: code,
		Msg:  msg,
		Data: data,
	})
}

func Response2(c *gin.Context, code int, msg string, data interface{}) {
	c.JSON(http.StatusOK, Body2{
		Code:   code,
		Msg:    msg,
		Result: data,
	})
}

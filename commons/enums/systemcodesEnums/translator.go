package systemcodesEnums

import "sync"

const SystemCodesEnum = "systemCodeEnum"

type SystemCodesTranslator struct {
	codeMsg map[string]string
}

var techniqueTranslatorInstance *SystemCodesTranslator
var techniqueOnce sync.Once

func GetTechniqueTranslatorInstance() *SystemCodesTranslator {
	techniqueOnce.Do(func() {
		techniqueTranslatorInstance = &SystemCodesTranslator{
			codeMsg,
		}
	})
	return techniqueTranslatorInstance
}

func (t *SystemCodesTranslator) GetRuleEnums(code string) string {
	return t.codeMsg[code]
}

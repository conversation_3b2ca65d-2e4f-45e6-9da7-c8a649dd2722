package enginEnums

const (
	//规则错误码
	ExecSuccess          = "1000"
	NotFoundRule         = "1001"
	RuleContentNoRecords = "1002"
	FormattingError      = "1003"
	ExecFail             = "1004"
	RuleUnusable         = "1005"
	//映射
	PatientCodeMappingError         = "2001"
	WesternMedicineCodeMappingError = "2002"
	//对象数据校验
	MedicineDoctorOrdersListIsNil                  = "3001"
	LaboratoryTestExaminationDoctorOrdersListIsNil = "3002"
	ImageDoctorOrdersListIsNil                     = "3003"

	//非创建时写入的提示语
	SurgicalDoctorOrdersPrompt01 = "温馨提示：请核对是否签立各类手术相关文书。"
)

// 规则对象
const (
	PatientDSL = "patient"

	WesternMedicineDoctorOrderDSL = "medicineOrder"
	ExaminationOrderDSL           = "ExaminationOrder"
	LaboratoryExaminationOrderDSL = "LaboratoryExaminationOrder"
	SurgicalOrderDSL              = "SurgicalOrder"

	ExecutionRecordOfMedicineOrderDSL              = "ExecutionRecordOfMedicineOrder"              //药品医嘱执行记录
	ExecutionRecordOfSurgicalOrderDSL              = "ExecutionRecordOfSurgicalOrder"              //手术医嘱执行记录
	ExecutionRecordOfLaboratoryExaminationOrderDSL = "ExecutionRecordOfLaboratoryExaminationOrder" //检验医嘱执行记录
	ExecutionRecordOfExaminationOrderDSL           = "ExecutionRecordOfExaminationOrder"           //检查医嘱执行记录

	Diagnosis = "Diagnosis" //诊断

	AllergyHistory = "allergyHistory" //过敏史

	LaboratoryReport = "LaboratoryReport" //实验室报告
)

// 规则对象映射类型
const (
	CodeSystemMapping = "1" //编码体系映射
	MasterDataMapping = "2" //主数据映射
)

const (
	WesternMedicineDoctorOrderSlice                  = 0
	ExaminationOrderSlice                            = 1
	LabTestExamDoctorOrderSlice                      = 2
	SurgicalDoctorOrderSlice                         = 3
	ExecutionRecordOfMedicineSlice                   = 4
	ExecutionRecordOfExaminationOrderSlice           = 5
	ExecutionRecordOfLaboratoryExaminationOrderSlice = 6
	ExecutionRecordOfSurgicalOrderSlice              = 7
	DiagnosisSlice                                   = 8
	AllergyHistorySlice                              = 9
	LaboratoryReportSlice                            = 10
)

var codeMsg = map[string]string{
	ExecSuccess:          "规则调用成功",
	NotFoundRule:         "未查询到该规则",
	RuleContentNoRecords: "规则内容为空",
	FormattingError:      "规则格式错误",
	ExecFail:             "规则执行失败",
	RuleUnusable:         "规则不可用",

	PatientCodeMappingError:         "患者属性编码映射错误",
	WesternMedicineCodeMappingError: "西药医嘱编码映射错误",

	MedicineDoctorOrdersListIsNil:                  "西药医嘱列表为空",
	LaboratoryTestExaminationDoctorOrdersListIsNil: "实验室检查医嘱列表为空",
	ImageDoctorOrdersListIsNil:                     "影像学检查医嘱列表为空",
}

package enginEnums

import "sync"

const EngineEnum = "engineEnum"

type EngineTranslator struct {
	codeMsg map[string]string
}

var engineTranslatorInstance *EngineTranslator
var engineOnce sync.Once

func GetEngineTranslatorInstance() *EngineTranslator {
	engineOnce.Do(func() {
		engineTranslatorInstance = &EngineTranslator{
			codeMsg,
		}
	})
	return engineTranslatorInstance
}

func (t *EngineTranslator) GetRuleEnums(code string) string {
	return t.codeMsg[code]
}

package enums

import (
	"kmbservice/commons/enums/enginEnums"
	"kmbservice/commons/enums/systemcodesEnums"
	"sync"
)

type RuleTranslator interface {
	GetRuleEnums(code string) string
}

type CodeTranslatorRegistry struct {
	translators map[string]RuleTranslator
	mu          sync.Mutex
}

var codeTranslatorRegistry *CodeTranslatorRegistry

func init() {
	codeTranslatorRegistry = &CodeTranslatorRegistry{
		translators: make(map[string]RuleTranslator),
	}
}

func GetTranslator(translatorType string) RuleTranslator {
	codeTranslatorRegistry.mu.Lock()
	defer codeTranslatorRegistry.mu.Unlock()
	if translator, exists := codeTranslatorRegistry.translators[translatorType]; exists {
		return translator
	}
	switch translatorType {
	case systemcodesEnums.SystemCodesEnum:
		codeTranslatorRegistry.translators[translatorType] = systemcodesEnums.GetTechniqueTranslatorInstance()
	case enginEnums.EngineEnum:
		codeTranslatorRegistry.translators[translatorType] = enginEnums.GetEngineTranslatorInstance()
	default:
		return nil
	}
	return codeTranslatorRegistry.translators[translatorType]
}

package util

import (
	"github.com/dgrijalva/jwt-go"
	"kmbservice/pkg/setting"
	"time"
)

var jwtSecret = []byte(setting.JwtSecret)

type Claims struct {
	Userid   int8   `json:"userid"`
	Username string `json:"username"`
	Password string `json:"password"`
	jwt.StandardClaims
}

func GenerateToken(userid int8, password string) (string, error) {
	nowTime := time.Now()
	expireDuration, err := time.ParseDuration(setting.AppSetting.Token_Expire_Duration)
	if err != nil {
		return "", err
	}
	expireTime := nowTime.Add(expireDuration)
	claims := Claims{
		Userid:   userid,
		Username: "",
		Password: password,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expireTime.Unix(),
			Issuer:    "kmbservice",
		},
	}
	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := tokenClaims.SignedString(jwtSecret)
	if err != nil {
		return "", err
	}
	return token, nil

}
func ParseToken(token string) (*Claims, error) {

	tokenClaims, err := jwt.ParseWithClaims(token, &Claims{}, func(token *jwt.Token) (interface{}, error) {

		return jwtSecret, nil
	})
	if err != nil {

		return nil, err
	}
	if tokenClaims != nil {
		if claims, ok := tokenClaims.Claims.(*Claims); ok && tokenClaims.Valid {
			return claims, nil
		}
	}

	return nil, err
}

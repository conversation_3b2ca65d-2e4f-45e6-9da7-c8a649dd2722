package util

import (
	"kmbservice/pkg/setting"
	"testing"
)

var t_token string

func initSetup() {
	setting.Setup("/Users/<USER>/go/src/kmbservice/conf/app.ini")
}
func TestGenerateToken(t *testing.T) {
	initSetup()
	token, err := GenerateToken("123", "admin")
	if err != nil {
		t.Error(err)
	}
	t.Log(token)
	t_token = token
	ParseToken(token)
}
func TestParseToken(t *testing.T) {
	claims, err := ParseToken("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyaWQiOiIxMjMiLCJ1c2VybmFtZSI6IjEyMyIsInBhc3N3b3JkIjoiYWRtaW4iLCJleHAiOjE3MTU4NDEwOTYsImlzcyI6ImttYnNlcnZpY2UifQ.ABQfWrUb2nMnjgRQbS5Dg58bV6BwEQANvVBYhcfuD3A")
	if err != nil {
		t.Error(err)
	}
	t.Log(claims)
}

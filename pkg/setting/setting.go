package setting

import (
	"github.com/go-ini/ini"
	"log"
	"time"
)

type App struct {
	JwtSecret             string
	PageSize              int
	RuntimeRootPath       string
	ImagePrefixUrl        string
	ImageSavePath         string
	ImageMaxSize          int
	ImageAllowExts        []string
	LogSavePath           string
	LogSaveName           string
	LogFileExt            string
	TimeFormat            string
	Lang                  string
	EnableI18n            string
	I18nStaticDir         string
	Token_Expire_Duration string
}

var AppSetting = &App{}

type Server struct {
	RunMode      string
	HttpPort     int
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
}

var ServerSetting = &Server{}

type Database struct {
	Type        string
	User        string
	Password    string
	Host        string
	Name        string
	TablePrefix string
	Port        string
}

var DatabaseSetting = &Database{}

var (
	Cfg                 *ini.File
	RunMode             string
	HTTPPort            int
	ReadTimeout         time.Duration
	WriteTimeout        time.Duration
	PageSize            int
	JwtSecret           string
	TokenExpireDuration string
)

type Redis struct {
	Host        string
	Password    string
	MaxIdle     int
	MaxActive   int
	IdleTimeout time.Duration
}

var RedisSetting = &Redis{}

type DataService struct {
	Host string
	Port string
	Url  string
}

var DataServiceSetting = &DataService{}

type CodingSystemServer struct {
	Host string
	Port string
}

var CodingSystemServerSetting = &CodingSystemServer{}

var cfg *ini.File

func Setup(configFile string) {
	var err error
	cfg, err = ini.Load(configFile) //  conf/app.ini
	if err != nil {
		log.Fatalf("Fail to parse 'conf/app.ini': %v", err)
	}

	mapTo("app", AppSetting)
	mapTo("server", ServerSetting)
	mapTo("database", DatabaseSetting)
	mapTo("redis", RedisSetting)
	mapTo("data_service", DataServiceSetting)
	mapTo("coding_system_service", CodingSystemServerSetting)
	AppSetting.ImageMaxSize = AppSetting.ImageMaxSize * 1024 * 1024
	ServerSetting.ReadTimeout = ServerSetting.ReadTimeout * time.Second
	ServerSetting.WriteTimeout = ServerSetting.ReadTimeout * time.Second
}
func mapTo(section string, v interface{}) {
	err := cfg.Section(section).MapTo(v)
	if err != nil {
		log.Fatalf("Cfg.MapTo %s err: %v", section, err)
	}
}

package main

import (
	"fmt"
)

// Message 定义消息结构
type Message struct {
	// 具体的消息字段定义可以根据需要添加
	Content string
}

// TestReportDetail 定义检验报告详细结构
type TestReportDetail struct {
	DetailID              int64  `json:"detail_id"`
	ReportID              int64  `json:"report_id"`
	TestItemID            int64  `json:"test_item_id"`
	TestItemName          string `json:"test_item_name"`
	TestResult            string `json:"test_result"`
	ReferenceRange        string `json:"reference_range"`
	Unit                  string `json:"unit"`
	HighLowFlag           string `json:"high_low_flag"`
	Msg                   []Message
	TestIndicatorID       string
	TestResultValue       float64
	InspectionresultsUnit string
}

// isReportValid 根据给定规则检查单个 TestReportDetail 是否符合条件
func isReportValid(report *TestReportDetail) bool {
	return ((report.TestIndicatorID == "1796078696373392480" && report.TestResultValue > 40 && report.InspectionresultsUnit == "1") ||
		(report.TestIndicatorID == "1796078868742509665" && report.TestResultValue > 50 && report.InspectionresultsUnit == "1") ||
		(report.TestIndicatorID == "1699639165613012651" && report.TestResultValue > 21 && report.InspectionresultsUnit == "2") ||
		(report.TestIndicatorID == "1699639165696900155" && report.TestResultValue > 6 && report.InspectionresultsUnit == "2") ||
		(report.TestIndicatorID == "1699639165604623898" && report.TestResultValue > 17 && report.InspectionresultsUnit == "2"))
}

// isAnyReportValid 检查切片中是否有任意一个 TestReportDetail 符合条件
func isAnyReportValid(reports []*TestReportDetail) bool {
	for _, report := range reports {
		if isReportValid(report) {
			return true
		}
	}
	return false
}

func main() {
	// 示例数据
	reports := []*TestReportDetail{
		{TestIndicatorID: "1796078696373392480", TestResultValue: 41, InspectionresultsUnit: "1"},
		{TestIndicatorID: "1796078868742509665", TestResultValue: 49, InspectionresultsUnit: "1"},
		{TestIndicatorID: "1699639165613012651", TestResultValue: 22, InspectionresultsUnit: "2"},
		{TestIndicatorID: "1699639165696900155", TestResultValue: 5, InspectionresultsUnit: "2"},
	}

	// 检查是否有任意一个报告符合条件
	result := isAnyReportValid(reports)

	// 输出结果
	fmt.Printf("Any report valid: %t\n", result)
}

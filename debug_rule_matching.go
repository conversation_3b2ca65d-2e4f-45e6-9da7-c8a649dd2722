package main

import (
	"encoding/json"
	"fmt"
	"kmbservice/api/types"
	"kmbservice/dsl"
	"kmbservice/dsl/doctororders"
	"kmbservice/repository/models"
	"kmbservice/pkg/setting"
	"strconv"
	"time"
)

func main() {
	// 初始化配置和数据库
	setting.Setup("conf/app.ini")
	models.Setup()

	fmt.Println("🔍 规则匹配调试分析")
	fmt.Println("=" * 50)

	// 模拟请求数据
	testData := types.RuleGengineRequest{
		SystemCodeMapping: true,
		CorpID:           "1",
		InputData: &types.InputData{
			Rule: &types.Rule{
				RuleIDList: []string{
					"1853333846653568186",
					"1853337716653995226",
					"1853339848736158943",
				},
			},
			Patient: &dsl.Patient{
				PatientID:       "",
				PatientName:     "^华^杏^妹^之^子",
				PatientGender:   "1",
				PatientBirthday: "2023-07-19",
			},
			WesternMedicineDoctorOrdersList: []*doctororders.WesternMedicineDoctorOrders{
				{
					MedicationPrescriptionItem: "72702",
					DrugDosage:                6,
					DrugDosageUnit:            "22",
					AdminisTraionRoute:        "口服",
					OrderCreateTime:           "1756448536000",
				},
			},
		},
	}

	// 1. 检查规则内容
	fmt.Println("📋 1. 检查规则内容:")
	ruleModel := models.NewRuleContent{}
	for i, ruleIDStr := range testData.InputData.Rule.RuleIDList {
		ruleID, _ := strconv.ParseInt(ruleIDStr, 10, 64)
		ruleInfo, err := ruleModel.FindByIDAndIsUse(ruleID, 1)
		if err != nil || ruleInfo == nil {
			fmt.Printf("   ❌ 规则 %d: 未找到 (ID: %s)\n", i+1, ruleIDStr)
			continue
		}
		fmt.Printf("   ✅ 规则 %d: %s\n", i+1, ruleInfo.RuleName)
		fmt.Printf("      状态: %d, 是否启用: %d\n", ruleInfo.Status, ruleInfo.IsUse)
		fmt.Printf("      规则内容: %s\n", ruleInfo.RuleContent[:min(100, len(ruleInfo.RuleContent))] + "...")
		fmt.Println()
	}

	// 2. 检查患者数据处理
	fmt.Println("👤 2. 检查患者数据:")
	patient := testData.InputData.Patient
	patient.CalculateAge() // 计算年龄
	fmt.Printf("   患者姓名: %s\n", patient.PatientName)
	fmt.Printf("   患者性别: %s\n", patient.PatientGender)
	fmt.Printf("   出生日期: %s\n", patient.PatientBirthday)
	fmt.Printf("   计算年龄: %d\n", patient.Age)
	fmt.Printf("   年龄单位: %s\n", patient.AgeUnit)
	fmt.Println()

	// 3. 检查药品数据处理
	fmt.Println("💊 3. 检查药品数据:")
	medicine := testData.InputData.WesternMedicineDoctorOrdersList[0]
	fmt.Printf("   原始数据:\n")
	fmt.Printf("     medication_prescription_item: %s\n", medicine.MedicationPrescriptionItem)
	fmt.Printf("     drug_dosage: %v\n", medicine.DrugDosage)
	fmt.Printf("     drug_dosage_unit: %s\n", medicine.DrugDosageUnit)
	fmt.Printf("     adminis_tration_route: %s\n", medicine.AdminisTraionRoute)
	fmt.Printf("     RouteOfAdministration: %s\n", medicine.RouteOfAdministration)

	// 应用字段兼容性修复
	if medicine.AdminisTraionRoute != "" && medicine.RouteOfAdministration == "" {
		medicine.RouteOfAdministration = medicine.AdminisTraionRoute
		fmt.Printf("   ✅ 字段映射: adminis_tration_route → RouteOfAdministration\n")
	}
	fmt.Printf("   修复后 RouteOfAdministration: %s\n", medicine.RouteOfAdministration)
	fmt.Println()

	// 4. 检查编码转换配置
	fmt.Println("🔄 4. 检查编码转换配置:")
	confModel := models.RuleCodeSystemConf{}
	
	// 检查给药途径编码配置
	routeConf, err := confModel.FindByDataValueCodeANDCodeSystemId("1", "WesternMedicineDoctorOrderDSL", "RouteOfAdministration")
	if err != nil || routeConf == nil {
		fmt.Printf("   ❌ 给药途径编码配置未找到\n")
	} else {
		fmt.Printf("   ✅ 给药途径编码配置: DicID=%d\n", routeConf.DicID)
	}

	// 检查药品编码配置
	drugConf, err := confModel.FindByDataValueCodeANDCodeSystemId("1", "WesternMedicineDoctorOrderDSL", "MedicationPrescriptionItem")
	if err != nil || drugConf == nil {
		fmt.Printf("   ❌ 药品编码配置未找到\n")
	} else {
		fmt.Printf("   ✅ 药品编码配置: DicID=%d\n", drugConf.DicID)
	}
	fmt.Println()

	// 5. 检查主数据映射
	fmt.Println("📊 5. 检查主数据映射:")
	combinedMedicineModel := models.CombinedMedicines{}
	drugInfo, err := combinedMedicineModel.FindByBusinessCodeAndItemType(medicine.MedicationPrescriptionItem, "1")
	if err != nil || drugInfo == nil {
		fmt.Printf("   ❌ 药品主数据未找到 (business_code: %s)\n", medicine.MedicationPrescriptionItem)
	} else {
		fmt.Printf("   ✅ 药品主数据找到: %s\n", drugInfo.ItemName)
		fmt.Printf("      业务编码: %s\n", drugInfo.BusinessCode)
		fmt.Printf("      项目类型: %s\n", drugInfo.ItemType)
	}
	fmt.Println()

	// 6. 模拟规则执行环境
	fmt.Println("⚙️  6. 模拟规则执行环境:")
	fmt.Printf("   规则执行时的数据上下文:\n")
	fmt.Printf("     patient.Age: %d\n", patient.Age)
	fmt.Printf("     patient.PatientGender: %s\n", patient.PatientGender)
	fmt.Printf("     medicineOrder.MedicationPrescriptionItem: %s\n", medicine.MedicationPrescriptionItem)
	fmt.Printf("     medicineOrder.RouteOfAdministration: %s\n", medicine.RouteOfAdministration)
	fmt.Printf("     medicineOrder.DrugDosage: %v\n", medicine.DrugDosage)
	fmt.Println()

	// 7. 分析可能的匹配问题
	fmt.Println("🔍 7. 可能的匹配问题分析:")
	fmt.Println("   可能原因:")
	fmt.Println("   1. 患者年龄不符合规则条件 (当前1岁)")
	fmt.Println("   2. 药品编码不匹配规则中的期望值")
	fmt.Println("   3. 给药途径编码转换后不匹配")
	fmt.Println("   4. 规则中的其他条件不满足")
	fmt.Println()

	fmt.Println("💡 建议:")
	fmt.Println("   1. 检查规则内容中的具体匹配条件")
	fmt.Println("   2. 验证编码转换是否正确")
	fmt.Println("   3. 确认测试数据是否符合规则预期")
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

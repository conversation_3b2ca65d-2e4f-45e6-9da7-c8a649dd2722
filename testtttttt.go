package main

import (
	"fmt"
	"math"
	"strconv"
)

func convertIfWholeNumber(input float64) interface{} {
	// 如果小数部分为0，将其转换为整型
	if math.Trunc(input) == input {
		return int64(input)
	}
	// 否则返回原浮点数
	return input
}

func main() {
	// 测试各种浮点数
	tests := []float64{
		123.000000, // 应该转换为整数
		456.789,    // 应该保持为浮点数
		789.0,      // 应该转换为整数
		0.0,        // 应该转换为整数
		1.000000,   // 应该转换为整数
		1805832179268081408.0000,
	}

	for _, test := range tests {
		result := convertIfWholeNumber(test)
		fmt.Printf("Input: %s, Output: %v (%T)\n", strconv.FormatFloat(test, 'f', -1, 64), result, result)
	}
	fmt.Println("\n")
}

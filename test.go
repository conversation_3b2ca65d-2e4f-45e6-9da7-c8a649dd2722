package main

import (
	"encoding/json"
	"fmt"
)

func ConvertToKeyValue(input string) (string, error) {
	// 解析 JSON 字符串
	var data []struct {
		Keys   []string `json:"Keys"`
		Values []string `json:"Values"`
	}
	if err := json.Unmarshal([]byte(input), &data); err != nil {
		return "", err
	}

	// 构建键值对 map
	kvMap := make(map[string]string)
	for _, keys := range data {
		for j, key := range keys.Keys {
			// 确保 Values 中有对应的值
			if j < len(keys.Values) {
				kvMap[key] = keys.Values[j]
			}
		}
	}
	// 将 map 编码为 JSON 格式字符串
	jsonData, err := json.Marshal(kvMap)
	if err != nil {
		return "", err
	}

	return string(jsonData), nil
}

func main() {
	input := "[{\"Keys\":[\"d.diseaseOrSyndrome\"],\"Values\":[\"失眠障碍\"]}]"
	output, err := ConvertToKeyValue(input)
	if err != nil {
		fmt.Println("转换失败：", err)
		return
	}
	fmt.Println(output)
}

package main

import (
	"context"
	"fmt"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"log"
)

func main() {
	// Neo4j connection details
	dbURL := "bolt://10.0.0.168:7687"
	username := "admin"
	password := "8j!G@fZ#4$"
	graph := "PsychiatricDiseases_schema_new"

	// Create a Neo4j driver instance
	driver, err := neo4j.NewDriverWithContext(dbURL, neo4j.BasicAuth(username, password, ""))
	if err != nil {
		log.Fatalf("Failed to create driver: %v", err)
	}
	defer driver.Close(context.Background())

	// Open a new session
	session := driver.NewSession(context.Background(), neo4j.SessionConfig{
		DatabaseName: graph,
	})
	defer session.Close(context.Background())

	// Define a Cypher query
	query := "MATCH (n) RETURN n LIMIT 5"

	// Execute the query
	result, err := session.Run(context.Background(), query, nil)
	if err != nil {
		log.Fatalf("Failed to execute query: %v", err)
	}

	// Iterate through the result set
	for result.Next(context.Background()) {
		record := result.Record()
		node, _ := record.Get("n")
		fmt.Printf("Node: %+v\n", node)
	}

	// Check for errors at the end of iteration
	if err = result.Err(); err != nil {
		log.Fatalf("Query execution error: %v", err)
	}
}

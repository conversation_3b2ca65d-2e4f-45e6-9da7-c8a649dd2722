[app]
PageSize = 10
JwtSecret = 233
Token_Expire_Duration = 3h
RuntimeRootPath = runtime/
ImagePrefixUrl = http://127.0.0.1:8100
ImageSavePath = upload/images/
# MB
ImageMaxSize = 5
ImageAllowExts = .jpg,.jpeg,.png
LogSavePath = logs/
LogSaveName = log
LogFileExt = log
TimeFormat = 20060102
# 设置默认语言
Lang = en-US
# 设置使用i18n
EnableI18n = true
# 设置语言文件路径
I18nStaticDir = conf/locale
[server]
#debug or release
RunMode = debug
HttpPort = 9191
ReadTimeout = 60
WriteTimeout = 60

;[database]
;Type = postgres
;User = postgres
;Password = ********************************************
;Host = www.hbi365.com.cn
;Port = 5432
;Name = HBIDB
;TablePrefix = kbs_
;Auto_table = 1

[database]
Type = postgres
User = postgres
Password = nWI7ftb/iRYH0eb3Y/lGbSWllYFapY+O8N+ntL7M0cQ=
Host = **********
Port = 5432
Name = HBIDB
TablePrefix =
Auto_table = 1


[redis]
Host = 127.0.0.1:6379
Password =
MaxIdle = 30
MaxActive = 30
IdleTimeout = 200
[data_service]
Host = http://**********
Port = 9891
Url = /api/v1/rep/rep_lab_report_detail/get

[coding_system_service]
Host = http://**********
Port = 9894

#token鉴权
[Auth]
AccessSecret = lHtmYcBwdLKOeODc5wRxQgYodw86AcQP
AccessExpire = 3600

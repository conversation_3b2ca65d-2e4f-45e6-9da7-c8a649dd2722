package kbservice

import (
	"context"
	"encoding/json"
	"fmt"
	"kmbservice/pkg/logging"
	"kmbservice/repository"
	"strconv"
	"strings"
)

type DiseaseCharacteristics struct {
	Gender                       string `json:"gender"`                       //性别
	AgeGroup                     string `json:"ageGroup"`                     //年龄组
	PhysiologicalFunctionalGroup string `json:"physiologicalFunctionalGroup"` //诊断组
	ChangesInWorkQuality         string `json:"changesInWorkQuality"`         //工作质量情况
	ChangesInSleepQuality        string `json:"changesInSleepQuality"`        //睡眠质量情况
	ImpactOnSocialFunction       string `json:"impactOnSocialFunction"`       //社会功能影响程度
	SleepDurationAtNight         string `json:"sleepDurationAtNight"`         //晚上睡眠时间
	TotalSleepTime               string `json:"totalSleepTime"`               //总睡眠时间
}
type Symptom struct {
	SignOrSymptom                           string `json:"signOrSymptom"`                           //征象|症状
	NatureOfPsychiatricSymptoms             string `json:"natureOfPsychiatricSymptoms"`             //精神类症状性质
	TemporalPatterns                        string `json:"temporalPatterns"`                        //时间模式
	StartTimeCharacterOfSignOrSymptom       string `json:"startTimeCharacterOfSignOrSymptom"`       //症状发生时间
	DurationOfSignOrSymptoms                string `json:"durationOfSignOrSymptoms"`                //症状持续时间
	SymptomFrequency                        string `json:"symptomFrequency"`                        //症状发作频率
	DurationOfSignOrSymptomsUpperLimit      int    `json:"durationOfSignOrSymptomsUpperLimit"`      //持续时间上限
	DurationOfSignOrSymptomsLowerLimit      int    `json:"durationOfSignOrSymptomsLowerLimit"`      //持续时间下限
	DurationOfSignOrSymptoms_UpperLimitUnit string `json:"durationOfSignOrSymptoms_UpperLimitUnit"` //上限单位
	DurationOfSignOrSymptoms_LowerLimitUnit string `json:"durationOfSignOrSymptoms_LowerLimitUnit"` //下限单位
	SymptomFrequencyUpperLimit              int    `json:"symptomFrequencyUpperLimit"`              //频率上限
	SymptomFrequencyLowerLimit              int    `json:"symptomFrequencyLowerLimit"`              //频率下限
	SymptomFrequency_UpperLimitUnit         string `json:"symptomFrequency_UpperLimitUnit"`         //上限单位
	SymptomFrequency_LowerLimitUnit         string `json:"symptomFrequency_LowerLimitUnit"`         //下限单位
}
type Simplesymn struct {
	SignOrSymptom     string `json:"signOrSymptom"`     //征象|症状
	SymptomIdentifier string `json:"symptomIdentifier"` //症状编码
}

func GetDiseaseBySymAndDisChar(character DiseaseCharacteristics, symptoms []Symptom) string {
	// 构建疾病特征部分的Cypher语句
	whereClauses := make([]string, 0)
	queryTemplate := ""
	characterCypher := fmt.Sprintf(`MATCH (d: Disease_or_Syndrome)-[:diseasesRelatedCharacteristics]->(c: Disease_Characteristics)-[:mainSymptomsRelatedToDiseases]->(s: Sign_Or_Symptom) `)
	groupql := GeneratecypherByDisCharactor(character, "c") //拼接诊断组特征没有Where
	if len(groupql) > 0 {
		whereClauses = append(whereClauses, groupql)
		fmt.Sprintf("诊断组QL：", characterCypher)
	}
	symptomql := GeneratecypherBySymptoms(symptoms, "s") //拼接症状特征
	if len(symptomql) > 0 {
		whereClauses = append(whereClauses, symptomql)
		fmt.Printf("症状分组QL：", characterCypher)
	}
	// 组合所有条件
	if len(whereClauses) > 0 {
		queryTemplate = strings.Join(whereClauses, " AND ")
	}
	//cypher := fmt.Sprintf(`MATCH %s RETURN DISTINCT d`, strings.Join(conditions, "\n"))
	cypher := characterCypher + "where " + queryTemplate + "RETURN DISTINCT d"
	fmt.Printf(cypher)
	return cypher
}
func ConvertToKeyValue(input string) (string, error) {
	// 解析 JSON 字符串
	var data []struct {
		Keys   []string `json:"Keys"`
		Values []string `json:"Values"`
	}
	if err := json.Unmarshal([]byte(input), &data); err != nil {
		return "", err
	}

	// 构建键值对 map
	kvMap := make(map[string]string)
	for _, keys := range data {
		for j, key := range keys.Keys {
			// 确保 Values 中有对应的值
			if j < len(keys.Values) {
				kvMap[key] = keys.Values[j]
			}
		}
	}
	// 将 map 编码为 JSON 格式字符串
	jsonData, err := json.Marshal(kvMap)
	if err != nil {
		return "", err
	}

	return string(jsonData), nil
}

// 根据疾病组特征生成where后面条件，ch代表诊断组别名
func GeneratecypherByDisCharactor(character DiseaseCharacteristics, ch string) string {
	// ch 判断作为诊断组别名
	whereClauses := make([]string, 0)
	if character.Gender != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("(%s.gender = '%s'", ch, character.Gender))
	}
	if character.AgeGroup != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("  %s.ageGroup = '%s'", ch, character.AgeGroup))
	}
	if character.PhysiologicalFunctionalGroup != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("  %s.physiologicalFunctionalGroup='%s'", ch, character.PhysiologicalFunctionalGroup))
	}
	if character.ChangesInWorkQuality != "" {
		whereClauses = append(whereClauses, fmt.Sprintf(" %s.changesInWorkQuality = '%s'", ch, character.ChangesInWorkQuality))
	}
	if character.ChangesInSleepQuality != "" {
		whereClauses = append(whereClauses, fmt.Sprintf(" %s.changesInSleepQuality = '%s'", ch, character.ChangesInSleepQuality))
	}
	if character.ImpactOnSocialFunction != "" {
		whereClauses = append(whereClauses, fmt.Sprintf(" %s.impactOnSocialFunction ='%s'", ch, character.ImpactOnSocialFunction))
	}
	if character.SleepDurationAtNight != "" {
		whereClauses = append(whereClauses, fmt.Sprintf(" %s.sleepDurationAtNight = '%s'", ch, character.SleepDurationAtNight))
	}
	if character.TotalSleepTime != "" {
		whereClauses = append(whereClauses, fmt.Sprintf(" %s.totalSleepTime = '%s'", ch, character.TotalSleepTime))
	}
	if len(whereClauses) > 0 {
		queryTemplate := strings.Join(whereClauses, " AND ") + ")"
		return queryTemplate
	}
	return ""
}
func GeneratecypherBySymptoms(symptoms []Symptom, ch string) string {
	var conditions []string
	for _, symptom := range symptoms {
		SignOrSymptom := fmt.Sprintf(`(%s.signOrSymptom='%s' AND `, ch, symptom.SignOrSymptom)
		durationConditions := generateDurationConditions(symptom.DurationOfSignOrSymptomsUpperLimit, symptom.DurationOfSignOrSymptomsLowerLimit, "月")
		frequencyConditions := generateFrequencyConditions(symptom.SymptomFrequencyUpperLimit, symptom.SymptomFrequencyLowerLimit, "次/周")
		symptomCypher := fmt.Sprintf(` %s AND %s`, durationConditions, frequencyConditions)
		conditions = append(conditions, SignOrSymptom+symptomCypher)
	}
	if len(conditions) > 0 {
		queryTemplate := "(" + strings.Join(conditions, " AND ") + ")"
		return queryTemplate
	}
	return ""
}
func generateFrequencyConditions(upperLimit, lowerLimit int, unit string) string {
	var conditions []string
	// 构建频率条件部分的Cypher语句
	if upperLimit >= 0 {
		conditions = append(conditions, fmt.Sprintf(`(s.symptomFrequency_UpperLimitUnit='%s' AND s.symptomFrequency_LowerLimitUnit<>'%s' AND s.symptomFrequency_UpperLimit>=%d)`, unit, unit, upperLimit))
	}
	if lowerLimit >= 0 {
		conditions = append(conditions, fmt.Sprintf(`(s.symptomFrequency_UpperLimitUnit='%s' AND s.symptomFrequency_LowerLimitUnit='%s' AND s.symptomFrequency_UpperLimit>=%d AND s.symptomFrequency_LowerLimit<%d)`, unit, unit, upperLimit, lowerLimit))
	}
	conditions = append(conditions, fmt.Sprintf(`(s.symptomFrequency_UpperLimitUnit<>'%s' AND s.symptomFrequency_LowerLimitUnit='%s' AND s.symptomFrequency_LowerLimit<%d)`, unit, unit, lowerLimit))
	return strings.Join(conditions, " OR ")
}
func generateDurationConditions(upperLimit, lowerLimit int, unit string) string {
	var conditions []string
	// 构建持续时间条件部分的Cypher语句
	if upperLimit >= 0 {
		conditions = append(conditions, fmt.Sprintf(`(s.durationOfSignOrSymptoms_UpperLimitUnit='%s' AND s.durationOfSignOrSymptoms_LowerLimitUnit<>'%s' AND s.durationOfSignOrSymptoms_UpperLimit>=%d)`, unit, unit, upperLimit))
	}
	if lowerLimit >= 0 {
		conditions = append(conditions, fmt.Sprintf(`(s.durationOfSignOrSymptoms_UpperLimitUnit='%s' AND s.durationOfSignOrSymptoms_LowerLimitUnit='%s' AND s.durationOfSignOrSymptoms_UpperLimit>=%d AND s.durationOfSignOrSymptoms_LowerLimit<%d)`, unit, unit, upperLimit, lowerLimit))
	}
	conditions = append(conditions, fmt.Sprintf(`(s.durationOfSignOrSymptoms_UpperLimitUnit<>'%s' AND s.durationOfSignOrSymptoms_LowerLimitUnit='%s' AND s.durationOfSignOrSymptoms_LowerLimit<%d)`, unit, unit, lowerLimit))
	return strings.Join(conditions, " OR ")
}

// 根据人的特征获取相关诊断信息
func GetDiseaseByDisChar(condition DiseaseCharacteristics) string {
	// 定义查询语句模板
	queryTemplate := "MATCH (d:Disease_or_Syndrome)-[:diseasesRelatedCharacteristics]->(c:Disease_Characteristics)"
	whereClauses := make([]string, 0)

	if condition.Gender != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("c.gender = '%s'", condition.Gender))
	}
	if condition.AgeGroup != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("c.ageGroup = '%s'", condition.AgeGroup))
	}
	if condition.PhysiologicalFunctionalGroup != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("c.PhysiologicalFunctionalGroup = '%s'", condition.PhysiologicalFunctionalGroup))
	}
	if condition.ChangesInWorkQuality != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("c.changesInWorkQuality = '%s'", condition.ChangesInWorkQuality))
	}
	if condition.ChangesInSleepQuality != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("c.changesInSleepQuality = '%s'", condition.ChangesInSleepQuality))
	}
	if condition.ImpactOnSocialFunction != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("c.ImpactOnSocialFunction ='%s'", condition.ImpactOnSocialFunction))
	}
	if condition.SleepDurationAtNight != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("c.sleepDurationAtNight = '%s'", condition.SleepDurationAtNight))
	}
	if condition.TotalSleepTime != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("c.totalSleepTime = '%s'", condition.TotalSleepTime))
	}
	// 生成查询语句
	if len(whereClauses) > 0 {
		queryTemplate += "WHERE " + strings.Join(whereClauses, " AND ")
	}
	query := queryTemplate + " return DISTINCT d.classificationOfAcuteDegreeOfDisease,d.code,d.diseaseClassification,d.diseaseOrSyndrome,d.name,d.tcmDisease"

	return query
}

/*
下面是新增加的内容，为精神专科知识图谱提供推理
*/
// 根据症状查询诊断 flag 1:主要症状 2 次要症状
func GetDiseases(symns []Simplesymn, flag int) string {
	ctx := context.Background()
	queryTemplate := ""
	whereTemplate := ""
	switch flag {
	case 1:
		{
			whereClauses := make([]string, 0)
			selectClauses := make([]string, 0)
			for i, symn := range symns {
				characterCypher := fmt.Sprintf(`(d:Disease_or_Syndrome)-[:mainSymptomsRelatedToDiseases]->(%s:Sign_Or_Symptom)`, "s"+strconv.Itoa(i))
				selectClauses = append(selectClauses, characterCypher)
				SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%s'`, "s"+strconv.Itoa(i), symn.SignOrSymptom)
				whereClauses = append(whereClauses, SignOrSymptom)
			}

			if len(selectClauses) > 0 {
				queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")

			}
			if len(whereClauses) > 0 {
				whereTemplate = strings.Join(whereClauses, " AND ")

			}
			queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, whereTemplate, " RETURN DISTINCT  d.diseaseOrSyndrome LIMIT 10")
			logging.Info("GetDiseases-1: ", queryTemplate)
			fmt.Println(queryTemplate)
			result, err := repository.Runresult(ctx, queryTemplate, nil)
			if !err {
				// 如果没有返回结果，返回空的 JSON 对象
				return "{}"
			} else {
				// 如果有记录返回，将 result 转换为 JSON 格式并返回

				logging.Info("result: ", result)
				var jsonData interface{}
				err := json.Unmarshal([]byte(result), &jsonData)
				if err != nil {
					// 解析失败，返回空的 JSON 对象
					logging.Info("解析 JSON 数据失败：", err)
					return "{}"
				}
				// 将 JSON 对象转换回 JSON 字符串
				jsonResult, err := json.Marshal(jsonData)
				if err != nil {
					// 转换失败，返回空的 JSON 对象
					logging.Info("转换 JSON 数据失败：", err)
					return "{}"
				}

				return string(jsonResult)
			}
		}
	case 2:
		{
			whereClauses := make([]string, 0)
			selectClauses := make([]string, 0)
			queryTemplate := ""

			for i, symn := range symns {
				characterCypher := fmt.Sprintf(`(d:Disease_or_Syndrome)-[:secondarySymptomsRelatedToTheDisease]->(%s:Sign_Or_Symptom)`, "s"+strconv.Itoa(i))
				selectClauses = append(selectClauses, characterCypher)
				SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%s'`, "s"+strconv.Itoa(i), symn.SignOrSymptom)
				whereClauses = append(whereClauses, SignOrSymptom)
			}

			if len(selectClauses) > 0 {
				queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")

			}
			if len(whereClauses) > 0 {
				whereTemplate = strings.Join(whereClauses, " AND ")

			}
			queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, whereTemplate, " RETURN DISTINCT  d.diseaseOrSyndrome LIMIT 10")
			logging.Info("GetDiseases-2: ", queryTemplate)
			result, err := repository.Runresult(ctx, queryTemplate, nil)
			if !err {
				// 如果没有返回结果，返回空的 JSON 对象
				return "{}"
			} else {
				// 如果有记录返回，将 result 转换为 JSON 格式并返回

				logging.Info("result: ", result)
				var jsonData interface{}
				err := json.Unmarshal([]byte(result), &jsonData)
				if err != nil {
					// 解析失败，返回空的 JSON 对象
					logging.Info("解析 JSON 数据失败：", err)
					return "{}"
				}
				// 将 JSON 对象转换回 JSON 字符串
				jsonResult, err := json.Marshal(jsonData)
				if err != nil {
					// 转换失败，返回空的 JSON 对象
					logging.Info("转换 JSON 数据失败：", err)
					return "{}"
				}

				return string(jsonResult)
			}
		}
	default:
		return "{}"
	}
}

// 根据分组和症状查询量表
func GetTables(diseasegroup string, symns []Simplesymn) string {
	ctx := context.Background()
	queryTemplate := ""
	characterCypher := fmt.Sprintf(`MATCH(ps:Psychologic_Rating_Scale)-[:scaleApplyToTheClinicalFinding]->(s:Sign_Or_Symptom),(ps)-[:scaleApplyToTheClinicalFinding]->(c:Disease_Characteristics)`)
	var values []string
	for _, symn := range symns {
		values = append(values, fmt.Sprintf("'%s'", symn.SignOrSymptom))
	}
	inClause := fmt.Sprintf("s.signOrSymptom IN [%s]", strings.Join(values, ","))
	agegrop := fmt.Sprintf("c.ageGroup = '%s'", diseasegroup)
	queryTemplate = fmt.Sprintf("%s WHERE %s AND %s %s", characterCypher, agegrop, inClause, " RETURN DISTINCT ps.typesOfScales ")
	logging.Info("GetTables: ", queryTemplate)
	result, err := repository.Runresult(ctx, queryTemplate, nil)
	if !err {
		// 如果没有返回结果，返回空的 JSON 对象
		return "{}"
	} else {
		// 如果有记录返回，将 result 转换为 JSON 格式并返回

		logging.Info("result: ", result)
		var jsonData interface{}
		err := json.Unmarshal([]byte(result), &jsonData)
		if err != nil {
			// 解析失败，返回空的 JSON 对象
			logging.Info("解析 JSON 数据失败：", err)
			return "{}"
		}
		// 将 JSON 对象转换回 JSON 字符串
		jsonResult, err := json.Marshal(jsonData)
		if err != nil {
			// 转换失败，返回空的 JSON 对象
			logging.Info("转换 JSON 数据失败：", err)
			return "{}"
		}

		return string(jsonResult)
	}
}

// 根据分组和疾病查询表
func GetTablesByDis(diseasegroup string, diseaseOrSyndrome string) string {
	ctx := context.Background()
	queryTemplate := ""
	characterCypher := fmt.Sprintf(`MATCH (ps:Psychologic_Rating_Scale)-[:scaleApplyToTheClinicalFinding]->(s:Sign_Or_Symptom), (ps)-[:scaleApplyToTheClinicalFinding]->(c:Disease_Characteristics)  `)
	agegrop := fmt.Sprintf("c.ageGroup = '%s'", diseasegroup)
	disease := fmt.Sprintf("s.signOrSymptom in %s", diseaseOrSyndrome)
	queryTemplate = fmt.Sprintf("%s WHERE %s AND %s %s", characterCypher, agegrop, disease, " RETURN DISTINCT ps.typesOfScales ")
	logging.Info("GetTablesByDis: ", queryTemplate)
	result, err := repository.Runresult(ctx, queryTemplate, nil)
	if !err {
		// 如果没有返回结果，返回空的 JSON 对象
		return "{}"
	} else {
		// 如果有记录返回，将 result 转换为 JSON 格式并返回

		logging.Info("result: ", result)
		var jsonData interface{}
		err := json.Unmarshal([]byte(result), &jsonData)
		if err != nil {
			// 解析失败，返回空的 JSON 对象
			logging.Info("解析 JSON 数据失败：", err)
			return "{}"
		}
		// 将 JSON 对象转换回 JSON 字符串
		jsonResult, err := json.Marshal(jsonData)
		if err != nil {
			// 转换失败，返回空的 JSON 对象
			logging.Info("转换 JSON 数据失败：", err)
			return "{}"
		}

		return string(jsonResult)
	}
}
func GettcmBydiseaseGroup(diseasegroup []Simplesymn) string {
	ctx := context.Background()
	queryTemplate := ""
	characterCypher := fmt.Sprintf(`MATCH(d:Disease_or_Syndrome) `)
	var values []string
	for _, symn := range diseasegroup {
		values = append(values, fmt.Sprintf("'%s'", symn.SignOrSymptom))
	}
	inClause := fmt.Sprintf("d.diseaseOrSyndrome IN [%s]", strings.Join(values, ","))
	queryTemplate = fmt.Sprintf("%s WHERE %s %s ", characterCypher, inClause, " RETURN DISTINCT d.tcmDisease  ")
	logging.Info("GettcmBydiseaseGroup: ", queryTemplate)
	result, err := repository.Runresult(ctx, queryTemplate, nil)
	if !err {
		// 如果没有返回结果，返回空的 JSON 对象
		return "{}"
	} else {
		// 如果有记录返回，将 result 转换为 JSON 格式并返回

		logging.Info("result: ", result)
		var jsonData interface{}
		err := json.Unmarshal([]byte(result), &jsonData)
		if err != nil {
			// 解析失败，返回空的 JSON 对象
			logging.Info("解析 JSON 数据失败：", err)
			return "{}"
		}
		// 将 JSON 对象转换回 JSON 字符串
		jsonResult, err := json.Marshal(jsonData)
		if err != nil {
			// 转换失败，返回空的 JSON 对象
			logging.Info("转换 JSON 数据失败：", err)
			return "{}"
		}

		return string(jsonResult)
	}
}

// 根据中医疾病获取证候
func GettcmSymBytcmDis(inClause string) string {
	ctx := context.Background()
	queryTemplate := ""
	characterCypher := fmt.Sprintf(`MATCH(d)-[:diseasesRelatedSyndrome]->(ts:TCM_Syndrome) `)
	inClause1 := fmt.Sprintf("d.tcmDisease IN %s", inClause)
	queryTemplate = fmt.Sprintf("%s WHERE %s  %s", characterCypher, inClause1, " RETURN DISTINCT ts.tcmSyndrome ")
	logging.Info("GettcmSymBytcmDis: ", queryTemplate)
	result, err := repository.Runresult(ctx, queryTemplate, nil)
	if !err {
		// 如果没有返回结果，返回空的 JSON 对象
		return "{}"
	} else {
		// 如果有记录返回，将 result 转换为 JSON 格式并返回
		logging.Info("result: ", result)
		var jsonData interface{}
		err := json.Unmarshal([]byte(result), &jsonData)
		if err != nil {
			// 解析失败，返回空的 JSON 对象
			logging.Info("解析 JSON 数据失败：", err)
			return "{}"
		}
		// 将 JSON 对象转换回 JSON 字符串
		jsonResult, err := json.Marshal(jsonData)
		if err != nil {
			// 转换失败，返回空的 JSON 对象
			logging.Info("转换 JSON 数据失败：", err)
			return "{}"
		}
		return string(jsonResult)
	}
}

func GetRecomendation(diseaseOrSyndrome string, flag int) string {
	ctx := context.Background()
	queryTemplate := ""
	inClause1 := ""
	retstr := ""
	characterCypher := ""
	switch flag {
	case 1: //检查
		{
			characterCypher = fmt.Sprintf(`MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedImagingExamination]->(ie:Imaging_Examination_Items) `)
			inClause1 = fmt.Sprintf("d.diseaseOrSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN DISTINCT ie `)
		}
	case 2: //检验
		{
			characterCypher = fmt.Sprintf(`MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedLaboratoryExamination]->(lp:Laboratory_Procedure) `)
			inClause1 = fmt.Sprintf("d.diseaseOrSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN DISTINCT lp `)
		}
	case 3: //推荐电生理检查
		{
			characterCypher = fmt.Sprintf(`MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedElectrophysiologicStudy]->(e:Electrophysiologic_Study_Item) `)
			inClause1 = fmt.Sprintf("d.diseaseOrSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN DISTINCT e `)
		}
	case 4: //物理治疗
		{
			characterCypher = fmt.Sprintf(`MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedPhysicalTherapy]->(pt:Physical_Therapy) `)
			inClause1 = fmt.Sprintf("d.diseaseOrSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN DISTINCT pt `)
		}
	case 5: //心理治疗
		{
			characterCypher = fmt.Sprintf(`MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedPsychotherapy]->(ps:Psychotherapy)`)
			inClause1 = fmt.Sprintf("d.diseaseOrSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN DISTINCT ps `)
		}
	case 6: //西药
		{
			characterCypher = fmt.Sprintf(`MATCH(m:Western_Medicine)-[:treat]->(d:Disease_or_Syndrome)`)
			inClause1 = fmt.Sprintf("d.diseaseOrSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN DISTINCT m `)
		}
	case 7: //中成药
		{
			characterCypher = fmt.Sprintf(`MATCH(ts:TCM_Syndrome)-[:methodOfTreatment]->(tc:Treatment_With_Chinese_Herbs)-[:use]->(pd:Chinese_Patent_Drug)`)
			inClause1 = fmt.Sprintf("ts.tcmSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN DISTINCT pd `)
		}
	case 8: //方剂
		{
			characterCypher = fmt.Sprintf(`MATCH(ts:TCM_Syndrome)-[:methodOfTreatment]->(tc:Treatment_With_Chinese_Herbs)-[:use]->(p:Prescription)-[:hasPart]->(fp:Fomulations_Of_Prescription)-[:hasMedicines]->(cp:Chinese_Herb_Pieces)`)
			inClause1 = fmt.Sprintf("ts.tcmSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN  p, fp, cp `)
		}
	default:
		{
			return "{}"
		}
	}
	queryTemplate = fmt.Sprintf("%s WHERE %s  %s", characterCypher, inClause1, retstr)
	logging.Info("GetRecomendation-", flag, queryTemplate)
	result, err := repository.Runresult(ctx, queryTemplate, nil)

	if !err {
		// 如果没有返回结果，返回空的 JSON 对象
		return "{}"
	} else {
		// 如果有记录返回，将 result 转换为 JSON 格式并返回
		logging.Info("result: ", result)
		var jsonData interface{}
		err := json.Unmarshal([]byte(result), &jsonData)
		if err != nil {
			// 解析失败，返回空的 JSON 对象
			logging.Info("解析 JSON 数据失败：", err)
			return "{}"
		}
		// 将 JSON 对象转换回 JSON 字符串
		jsonResult, err := json.Marshal(jsonData)
		if err != nil {
			// 转换失败，返回空的 JSON 对象
			logging.Info("转换 JSON 数据失败：", err)
			return "{}"
		}
		return string(jsonResult)
	}
}

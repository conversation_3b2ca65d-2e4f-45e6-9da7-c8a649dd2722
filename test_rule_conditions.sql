-- 查询具体的规则内容，了解匹配条件
-- 规则ID: 1853333846653568186, 1853337716653995226, 1853339848736158943

-- 1. 查询规则内容
SELECT 
    id,
    rule_name,
    rule_content,
    status,
    is_use
FROM rule_content 
WHERE id IN (1853333846653568186, 1853337716653995226, 1853339848736158943)
AND is_use = 1;

-- 2. 查询药品主数据信息
SELECT 
    business_code,
    item_name,
    item_type,
    status
FROM combined_medicines 
WHERE business_code = '72702' 
AND item_type = '1';

-- 3. 查询编码系统配置
SELECT 
    conf_id,
    corp_id,
    obj_name,
    dic_name,
    dic_id
FROM rule_code_system_conf 
WHERE corp_id = '1' 
AND obj_name = 'WesternMedicineDoctorOrderDSL'
AND dic_name IN ('RouteOfAdministration', 'MedicationPrescriptionItem');

-- 4. 查询给药途径编码映射
SELECT 
    snowflake_id,
    source_dic_id,
    target_dic_id,
    source_code,
    target_code
FROM dks_knb_dic_mapping 
WHERE source_code = '口服'
OR target_code = '口服';

-- 5. 查询最近的规则执行日志
SELECT 
    log_id,
    req_in,
    res_out,
    opt_date
FROM rule_logs 
ORDER BY opt_date DESC 
LIMIT 5;

-- 6. 如果有DMF数据集，查询编码值
-- SELECT 
--     data_value_soid,
--     data_value_code,
--     data_value_cn_meaning,
--     data_value_en_meaning
-- FROM dmf_data_sets 
-- WHERE data_value_code = '口服' 
-- OR data_value_cn_meaning LIKE '%口服%';

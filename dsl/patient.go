package dsl

import (
	"fmt"
	"time"
)

type Patient struct {
	ID                       string `json:"id"`                          // 病人ID
	PatientID                string `json:"patient_id"`                  //病人外部HISID
	AgeGroup                 string `json:"ageGroup"`                    //人群分组
	Age                      int    `json:"age"`                         //年龄
	AgeUnit                  string `json:"age_unit"`                    //年龄单位
	PlaceOfParticipationCode string `json:"place_of_participation_code"` //参保地编码
	InsuranceType            string `json:"insurance_type"`              //参保类型
	PatientName              string `json:"patient_name"`
	PatientGender            string `json:"patient_gender"`
	PatientBirthday          string `json:"patient_birthday"` //出生日期
	MaritalStatus            string `json:"marital_status"`
	Msg                      []Message
}

const (
	InsuranceType = "InsuranceType"
	PatientGender = "PatientGender"
	MaritalStatus = "MaritalStatus"
)

type AgeCalculator interface {
	CalculateAge() error
}

func (p *Patient) AddMessage(id string, name string, messagetype string) {
	message := Message{
		MessageID:   id,
		MessageDesc: name,
		MessageType: messagetype,
	}
	p.Msg = append(p.Msg, message)
}

// 患者生日解析年龄段
func (p *Patient) CalculateAge() {
	now := time.Now()
	layouts := []string{
		time.RFC3339,          // "2006-01-02T15:04:05Z07:00"
		"2006-01-02",          // "2006-01-02"
		"20060102",            // "20060102"
		"2006/01/02",          // "2006/01/02"
		"02-Jan-2006",         // "02-Jan-2006"
		"2006-01-02 15:04:05", // "2006-01-02 15:04:05"
		"2006-01-02T15:04:05", // "2006-01-02T15:04:05"
		"2006/01/02 15:04:05", // "2006/01/02 15:04:05"
		"2006-01-02 15:04",    // "2006-01-02 15:04"
		"2006/01/02 15:04",    // "2006/01/02 15:04"
		"2006-01-02T15:04",    // "2006-01-02T15:04"
	}
	var parsedTime time.Time
	var err error

	// 尝试解析日期字符串
	for _, layout := range layouts {
		parsedTime, err = time.Parse(layout, p.PatientBirthday)
		if err == nil {
			break
		}
	}

	if err != nil {
		fmt.Println("Error parsing date:", err)
		return
	}

	duration := now.Sub(parsedTime)
	years := int(duration.Hours() / 24 / 365)
	months := int(duration.Hours()/24/30) % 12
	days := int(duration.Hours()/24) % 30
	hours := int(duration.Hours()) % 24
	minutes := int(duration.Minutes()) % 60

	// 返回最前面的数字和单位
	if years > 0 {
		p.Age = years
		p.AgeUnit = "3"
		return
	} else if months > 0 {
		p.Age = months
		p.AgeUnit = "2"
		return
	} else if days > 0 {
		p.Age = months
		p.AgeUnit = "1"
		return
	} else if hours > 0 {
		p.Age = months
		p.AgeUnit = "4"
		return
	} else {
		p.Age = minutes
		p.AgeUnit = "5"
		return
	}
}

func NewPatient(id int, name, gender string, patientid string, dateOfBirth string, address, contact string) *Patient {

	p := &Patient{
		ID:              fmt.Sprintf("%d", id),
		PatientName:     name,
		PatientID:       patientid,
		PatientGender:   gender,
		PatientBirthday: dateOfBirth,
	}
	// 设置默认属性
	//p.setDefaultAttributes()
	return p
}

func calculateAge(dateOfBirth time.Time) int {
	now := time.Now()
	age := now.Year() - dateOfBirth.Year()
	// Check if the birthday has occurred this year
	if now.Month() < dateOfBirth.Month() || (now.Month() == dateOfBirth.Month() && now.Day() < dateOfBirth.Day()) {
		age--
	}
	return age
}

// 设置默认属性
//func (p *Patient) setDefaultAttributes() {
//	cfg, err := ini.Load("ruleEnums.ini")
//	if err != nil {
//		panic(err)
//	}
//	pregnancyConditions := cfg.Section("default_attributes").Key("pregnancy_conditions").String()
//	// 设置默认属性值
//	if p.PatientGender == "女" && pregnancyConditions != "" {
//		p.IsPregnant = true
//	}
//	switch pregnancyConditions {
//	case "ultrasound_report":
//		// Query ultrasound report
//		p.IsPregnant = queryUltrasoundReport(p)
//	case "obstetric_records":
//		// Query obstetric records
//		p.IsPregnant = queryObstetricRecords(p)
//	default:
//		p.IsPregnant = false
//	}
//
//}

// TODO:患者是否存在过敏反应（通过患者过敏史排查）
func (p *Patient) ExistsAllergicReaction(allergyHistory, drugs []string) bool { //传入患者的过敏史与接口传入的药物列表
	// 创建一个map来存储第一个切片中的字符串
	elementMap := make(map[string]bool)
	// 遍历第一个切片，将其中的字符串存入map
	for _, str := range allergyHistory {
		elementMap[str] = true
	}
	// 遍历第二个切片，检查其中的字符串是否在map中
	for _, str := range drugs {
		if elementMap[str] {
			return true
		}
	}
	// 如果没有找到相同的字符串，返回false
	return false
}

func queryObstetricRecords(p *Patient) bool {
	return true
}

func queryUltrasoundReport(p *Patient) bool {
	return true
}

package dsl

import (
	"strconv"
)

type Order struct {
	EncountID           string `json:"encountId"`           //就诊ID
	PatId               string `json:"patId"`               //患者ID
	OrderType           string `json:"orderType"`           //医嘱类型
	OrderTime           string `json:"orderTime"`           //医嘱开立时间
	HospitalCode        string `json:"hospitalCode"`        //院区代码
	DepartmentCode      string `json:"departmentCode"`      //执行科室代码
	ProgramCode         string `json:"programCode"`         //项目编码
	ClinicalService     string `json:"clinicalService"`     //临床服务
	Frequency           string `json:"frequency"`           //频次
	SingleDoseQuantity  string `json:"singleDoseQuantity"`  //单次剂量：数量
	SingleDoseUnit      string `json:"singleDoseUnit"`      //单次剂量：单位
	Numberofdays        string `json:"numberofdays"`        //天数
	TotalnumberQuantity string `json:"totalnumberQuantity"` //总数量：数量
	TotalQuantityUnit   string `json:"totalQuantityUnit"`   //总数量：单位
	AmountQuantity      string `json:"amountQuantity"`      //金额：数量
	AmountUnit          string `json:"amountUnit"`          //金额：单位
}

type OrderStore struct {
	order map[string]*Order
}

// 用药时长是否超过指定天数
func (o *Order) IsExceedDays(days string) bool {
	num1, _ := strconv.ParseFloat(o.Numberofdays, 64)
	num2, _ := strconv.ParseFloat(days, 64)
	if num1 < num2 {
		//超出
		return true
	} else if num1 > num2 {
		return false
	}
	return false
}

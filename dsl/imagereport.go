package dsl

import "time"

type ImageReport struct {
	ID               int          `json:"id"`                  // 报告ID
	EncountId        string       `json:"encountId"`           //就诊ID
	PatId            string       `json:"patId"`               //患者ID
	MedtechRptTypeID string       `json:"medtech_rpt_type_id"` //检查报告类别id
	ReportDate       time.Time    `json:"reportDate"`          // 报告日期
	ReportedBy       string       `json:"reportedBy"`          // 报告医生
	Modality         string       `json:"modality"`            // 影像学模态
	Findings         []Finding    `json:"findings"`            // 发现列表
	Impressions      []Impression `json:"impressions"`         // 结论列表
	Recommendations  string       `json:"recommendations"`     // 建议
}

type Finding struct {
	ID          int    `json:"id"`          // 发现ID
	Description string `json:"description"` // 发现描述
}

type Impression struct {
	ID          int    `json:"id"`          // 结论ID
	Description string `json:"description"` // 结论描述
}

type ImageReportMapStore struct {
	imageMapReportsMedtechRptTypeID map[string]*ImageReport
	imageMapID                      map[int]*ImageReport
}

func NewImageReportMapStore() *ImageReportMapStore {
	return &ImageReportMapStore{
		imageMapReportsMedtechRptTypeID: make(map[string]*ImageReport),
		imageMapID:                      make(map[int]*ImageReport),
	}
}

func (store *ImageReportMapStore) AddImageReportForID(medicine *ImageReport) {
	store.imageMapID[medicine.ID] = medicine
}

func (store *ImageReportMapStore) AddImageReportForMedtechRptTypeID(medicine *ImageReport) {
	store.imageMapReportsMedtechRptTypeID[medicine.MedtechRptTypeID] = medicine
}

// 检查报告内容是否存在指定的报告类别
func (i *ImageReportMapStore) CheckFeature(feature string) bool {
	_, exists := i.imageMapReportsMedtechRptTypeID[feature]
	if !exists {
		return false
	}
	return true
}

// 检查是否存在报告
func (i *ImageReportMapStore) CheckID(id int) bool {
	_, exists := i.imageMapID[id]
	if !exists {
		return false
	}
	return true
}

// 检查报告内容是否存在指定的报告类别
func (i *ImageReportMapStore) CheckMedtechRptTypeID(medtech_rpt_type_id string) bool {
	_, exists := i.imageMapReportsMedtechRptTypeID[medtech_rpt_type_id]
	if !exists {
		return false
	}
	return true
}

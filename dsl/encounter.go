package dsl

type OutEncounter struct {
	EncountId             string `json:"encountId"`               //就诊ID
	PatId                 string `json:"patId"`                   //患者ID
	HospitalCode          string `json:"hospital_code"`           //院区代码
	DepartmentCode        string `json:"department_code"`         //科室代码
	OutpatientDiagnosis   string `json:"outpatient_diagnosis"`    //门诊诊断
	Symptoms              string `json:"symptoms"`                //主诉症状
	ParticipatingAreaCode string `json:"participating_area_code"` //参保地编码
	InsuranceType         string `json:"insurance_type"`          //参保类型
}
type Hospitalization struct {
	EncountId              string `json:"encountId"`              // 就诊ID
	PatId                  string `json:"patId"`                  //病人ID
	Hospitalcode           string `json:"hospitalcode"`           //院区代码
	DepartmentCode         string `json:"departmentCode"`         //科室代码
	Diagnosis              string `json:"diagnosis"`              //出院诊断
	DateOfAdmission        string `json:"dateOfAdmission"`        //入院日期
	DateOfDischarge        string `json:"dateOfDischarge"`        //出院日期
	NumberOfDaysInhospital string `json:"numberOfDaysInhospital"` //住院天数
	ParticipatingAreaCode  string `json:"participatingAreaCode"`  //参保地编码
	InsuranceType          string `json:"insuranceType"`          //参保类型
}

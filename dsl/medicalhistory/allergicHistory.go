package medicalhistory

type AllergicHistory struct {
	AllergicHistory string                `json:"allergic_history"` //过敏类型
	Store           *AllergicHistoryStore `json:"-"`
}

type AllergicHistoryStore struct {
	AllergicHistoryMap  map[string]*AllergicHistory // 使用 map 存储药品，键为 medical_id
	AllergicHistoryList []*AllergicHistory
}

func NewAllergicHistoryStore() *AllergicHistoryStore {
	return &AllergicHistoryStore{
		AllergicHistoryMap:  make(map[string]*AllergicHistory),
		AllergicHistoryList: make([]*AllergicHistory, 0),
	}
}

func (a *AllergicHistory) SetStore(store *AllergicHistoryStore) {
	a.Store = store
}

func (a *AllergicHistoryStore) AddAllergicHistoryMap(surgical *AllergicHistory) {
	a.AllergicHistoryMap[surgical.AllergicHistory] = surgical
}

func (a *AllergicHistoryStore) AddAllergicHistoryList(surgicalList []*AllergicHistory) {
	a.AllergicHistoryList = surgicalList
}

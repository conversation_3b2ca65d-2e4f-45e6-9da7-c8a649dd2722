package dsl

type Diagnosis struct {
	DiagnosisCode                         string          `json:"diagnosis_code"`                            //西医临床诊断
	ChineseTraditionalMedicinesyndrome    string          `json:"chinese_traditional_medicine_syndrome"`     //中医证候
	TraditionalChineseMedicineDiseaseName string          `json:"traditional_chinese_medicine_disease_name"` //中医病名
	Store                                 *DiagnosisStore `json:"-"`
}
type DiagnosisStore struct {
	diagnosisMap  map[string]*Diagnosis // 使用 map 存储药品，键为 medical_id
	diagnosisList []*Diagnosis
}

func NewDiagnosisStore() *DiagnosisStore {
	return &DiagnosisStore{
		diagnosisMap:  make(map[string]*Diagnosis),
		diagnosisList: make([]*Diagnosis, 0),
	}
}

func (d *DiagnosisStore) AddDiagnosisMap(medical *Diagnosis) {
	d.diagnosisMap[medical.DiagnosisCode] = medical
}

func (d *DiagnosisStore) AddDiagnosisList(medicalList []*Diagnosis) {
	d.diagnosisList = medicalList
}

func (d *Diagnosis) SetStore(store *DiagnosisStore) {
	d.Store = store
}

func (d *DiagnosisStore) DiagnosisCodeExistStore(codes []string) bool {
	for _, code := range codes {
		if _, exists := d.diagnosisMap[code]; exists {
			return true
		}
	}
	return false
}

// TODO:判断诊断是否存在
func (d *Diagnosis) DiagnosisCodeIsExist(codes ...string) bool {
	if d.Store == nil {
		return false
	}
	return d.Store.DiagnosisCodeExistStore(codes)
}

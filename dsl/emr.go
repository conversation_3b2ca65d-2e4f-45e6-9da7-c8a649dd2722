package dsl

import (
	"strconv"
	"time"
)

type OutpEmr struct {
	OutpatientEmrID        int64     `json:"outpatient_emr_id" gorm:"primaryKey;column:outpatient_emr_id;comment:电子病历记录标识"`
	OutpatientEmrNo        string    `json:"outpatient_emr_no" gorm:"column:outpatient_emr_no;comment:电子病历记录序号"`
	EmrClassID             string    `json:"emr_class_id" gorm:"column:emr_class_id;comment:病历分类标识"`
	EmrClassNo             string    `json:"emr_class_no" gorm:"column:emr_class_no;comment:病历分类编码"`
	EmrClassName           string    `json:"emr_class_name" gorm:"column:emr_class_name;comment:病历分类名称"`
	EmrStatus              string    `json:"emr_status" gorm:"column:emr_status;comment:病历状态"`
	EmrStatusNo            string    `json:"emr_status_no" gorm:"column:emr_status_no;comment:病历状态编码-冗"`
	EmrStatusName          string    `json:"emr_status_name" gorm:"column:emr_status_name;comment:病历状态名称-冗"`
	RecordAt               time.Time `json:"record_at" gorm:"column:record_at;comment:文档记录时间"`
	CdTypeCode             string    `json:"cd_type_code" gorm:"column:cd_type_code;comment:文档类型代码"`
	CdConceptID            string    `json:"cd_concept_id" gorm:"column:cd_concept_id;comment:文档概念标识"`
	CdName                 string    `json:"cd_name" gorm:"column:cd_name;comment:文档名称"`
	EmrSetTitle            string    `json:"emr_set_title" gorm:"column:emr_set_title;comment:病历标题"`
	TextSectionExistFlag   string    `json:"text_section_exist_flag" gorm:"column:text_section_exist_flag;comment:有无文本段结构标志"`
	StructuredDataFlag     string    `json:"structured_data_flag" gorm:"column:structured_data_flag;comment:有无结构化数据标志"`
	PdfExistFlag           string    `json:"pdf_exist_flag" gorm:"column:pdf_exist_flag;comment:有无PDF标志"`
	FileStoreStatus        string    `json:"file_store_status" gorm:"column:file_store_status;comment:文件存储状态(保留)"`
	EmrResourceLocation    string    `json:"emr_resource_location" gorm:"column:emr_resource_location;comment:电子病历资源位置"`
	PdfPageIndex           string    `json:"pdf_page_index" gorm:"column:pdf_page_index;comment:当前病历在PDF中的位置"`
	TemplateHead           string    `json:"template_head" gorm:"column:template_head;comment:病历模板头信息"`
	InteractionRuleContent string    `json:"interaction_rule_content" gorm:"column:interaction_rule_content;comment:病历规则内容"`
	MajorVersion           string    `json:"major_version" gorm:"column:major_version;comment:主版本号"`
	MinorVersion           string    `json:"minor_version" gorm:"column:minor_version;comment:次版本号"`
	FixVersion             string    `json:"fix_version" gorm:"column:fix_version;comment:修订号"`
	PersonID               string    `json:"person_id" gorm:"column:person_id;comment:个人信息标识"`
	EncounterID            string    `json:"encounter_id" gorm:"column:encounter_id;comment:就诊标识"`
	EncounterTypeCode      string    `json:"encounter_type_code" gorm:"column:encounter_type_code;comment:就诊类型代码"`
	EncounterTypeNo        string    `json:"encounter_type_no" gorm:"column:encounter_type_no;comment:就诊类型编码-冗"`
	EncounterTypeName      string    `json:"encounter_type_name" gorm:"column:encounter_type_name;comment:就诊类型名称-冗"`
	EncRegSeqNo            string    `json:"enc_reg_seq_no" gorm:"column:enc_reg_seq_no;comment:就诊登记流水号"`
	Omrn                   string    `json:"omrn" gorm:"column:omrn;comment:门诊病历号"`
	DeptID                 string    `json:"dept_id" gorm:"column:dept_id;comment:科室标识"`
	DeptNo                 string    `json:"dept_no" gorm:"column:dept_no;comment:就诊科室编码"`
	DeptName               string    `json:"dept_name" gorm:"column:dept_name;comment:就诊科室名称"`
	EmrDoctorID            string    `json:"emr_doctor_id" gorm:"column:emr_doctor_id;comment:病历医师标识"`
	EmrDoctorNo            string    `json:"emr_doctor_no" gorm:"column:emr_doctor_no;comment:病历医师编码"`
	EmrDoctorName          string    `json:"emr_doctor_name" gorm:"column:emr_doctor_name;comment:病历医师名称"`
	EsID                   int       `json:"es_id" gorm:"column:es_id;comment:电子签名标识"`
	MrModifiedBy           string    `json:"mr_modified_by" gorm:"column:mr_modified_by;comment:修改人员标识"`
	MrModifiedNo           string    `json:"mr_modified_no" gorm:"column:mr_modified_no;comment:修改人员编码"`
	MrModifiedName         string    `json:"mr_modified_name" gorm:"column:mr_modified_name;comment:修改人员名称"`
	MrModifiedAt           time.Time `json:"mr_modified_at" gorm:"column:mr_modified_at;comment:修改日期时间"`
	ArchivedBy             string    `json:"archived_by" gorm:"column:archived_by;comment:归档人员标识"`
	ArchivedNo             string    `json:"archived_no" gorm:"column:archived_no;comment:归档人员编码"`
	ArchivedName           string    `json:"archived_name" gorm:"column:archived_name;comment:归档人员名称"`
	ArchivedAt             time.Time `json:"archived_at" gorm:"column:archived_at;comment:归档时间"`
	ReviewedBy             string    `json:"reviewed_by" gorm:"column:reviewed_by;comment:审核人员标识"`
	ReviewedNo             string    `json:"reviewed_no" gorm:"column:reviewed_no;comment:审核人员编码"`
	ReviewedName           string    `json:"reviewed_name" gorm:"column:reviewed_name;comment:审核人员名称"`
	ReviewedAt             time.Time `json:"reviewed_at" gorm:"column:reviewed_at;comment:审核日期时间"`
	WithdrawnBy            string    `json:"withdrawn_by" gorm:"column:withdrawn_by;comment:撤销人员标识"`
	WithdrawnNo            string    `json:"withdrawn_no" gorm:"column:withdrawn_no;comment:撤销人员编码"`
	WithdrawnName          string    `json:"withdrawn_name" gorm:"column:withdrawn_name;comment:撤销人员名称"`
	WithdrawnAt            time.Time `json:"withdrawn_at" gorm:"column:withdrawn_at;comment:撤销日期时间"`
	ServedAt               time.Time `json:"served_at" gorm:"column:served_at;comment:服务时间"`
	EmrEditorTypeCode      string    `json:"emr_editor_type_code" gorm:"column:emr_editor_type_code;comment:门诊病历编辑器类型"`
	CreatedAt              time.Time `json:"created_at" gorm:"column:created_at;comment:创建日期时间"`
	ModifiedAt             time.Time `json:"modified_at" gorm:"column:modified_at;comment:最后修改日期时间"`
	EmrStatusCode          string    `json:"emr_status_code" gorm:"column:emr_status_code;comment:病历状态代码"`
	TenantID               int64     `json:"tenant_id" gorm:"column:tenant_id;comment:租户ID"`
	GroupID                int64     `json:"group_id" gorm:"column:group_id;comment:集团ID"`
	LocationID             int64     `json:"location_id" gorm:"column:location_id;comment:位置ID"`
	CreatedBy              int64     `json:"created_by" gorm:"column:created_by;comment:创建人"`
	UpdatedBy              int64     `json:"updated_by" gorm:"column:updated_by;comment:更新人"`
	UpdatedAt              time.Time `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`
	IsDel                  int       `json:"is_del" gorm:"column:is_del;comment:逻辑删除"`
	Revision               int       `json:"revision" gorm:"column:revision;comment:版本"`
	OrgID                  int64     `json:"org_id" gorm:"column:org_id;comment:医院/组织ID"`
	RoleID                 int64     `json:"role_id" gorm:"column:role_id;comment:患者类型ID"`
	VisitID                string    `json:"visit_id" gorm:"column:visit_id;comment:接诊ID"`
}

// 获取病历的ID
func (o *OutpEmr) GetDiagnosis() string {
	emrId := strconv.FormatInt(o.OutpatientEmrID, 10)
	return emrId
}

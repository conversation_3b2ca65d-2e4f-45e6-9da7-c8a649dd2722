package report

type LaboratoryExaminationReport struct {
	LaboratoryItem               string                            `json:"laboratory_item"`                //检验项目
	UnitOfMeasurement            string                            `json:"unit_of_measurement"`            //定量结果
	LaboratoryQuantitativeResult string                            `json:"laboratory_quantitative_result"` //生物样本类型
	BiologicalSampleType         string                            `json:"biological_sample_type"`         //指标单位
	Store                        *LaboratoryExaminationReportStore `json:"-"`
}

type LaboratoryExaminationReportStore struct {
	laboratoryExaminationReportMap  map[string]*LaboratoryExaminationReport
	laboratoryExaminationReportList []*LaboratoryExaminationReport
}

func NewLaboratoryExaminationReportStore() *LaboratoryExaminationReportStore {
	return &LaboratoryExaminationReportStore{
		laboratoryExaminationReportMap:  make(map[string]*LaboratoryExaminationReport),
		laboratoryExaminationReportList: make([]*LaboratoryExaminationReport, 0),
	}
}

func (l *LaboratoryExaminationReport) SetStore(store *LaboratoryExaminationReportStore) {
	l.Store = store
}

func (l *LaboratoryExaminationReportStore) AddLaboratoryReportMap(LaboratoryTestExamination *LaboratoryExaminationReport) {
	l.laboratoryExaminationReportMap[LaboratoryTestExamination.LaboratoryItem] = LaboratoryTestExamination
}

func (l *LaboratoryExaminationReportStore) AddLaboratoryReportList(LaboratoryTestExaminationList []*LaboratoryExaminationReport) {
	l.laboratoryExaminationReportList = LaboratoryTestExaminationList
}

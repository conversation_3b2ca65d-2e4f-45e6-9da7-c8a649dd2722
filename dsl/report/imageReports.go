package report

type ImageReport struct {
	MedtechRptTypeID string            `json:"medtech_rpt_type_id"`
	Modality         string            `json:"modality"`
	Store            *ImageReportStore `json:"-"`
}

type ImageReportStore struct {
	ImageReportMap  map[string]*ImageReport // 使用 map 存储药品，键为 medical_id
	ImageReportList []*ImageReport
}

func NewImageReportStore() *ImageReportStore {
	return &ImageReportStore{
		ImageReportMap:  make(map[string]*ImageReport),
		ImageReportList: make([]*ImageReport, 0),
	}
}

func (l *ImageReport) SetStore(store *ImageReportStore) {
	l.Store = store
}

func (l *ImageReportStore) AddLaboratoryReportMap(image *ImageReport) {
	l.ImageReportMap[image.MedtechRptTypeID] = image
}

func (l *ImageReportStore) AddLaboratoryReportList(image []*ImageReport) {
	l.ImageReportList = image
}

package dsl

import (
	"strconv"
	"strings"
)

type Ingredient struct {
	ID          int    `json:"id"`          // 成分ID
	Name        string `json:"name"`        // 成分名称
	Description string `json:"description"` // 成分描述
}

type MechanismOfAction struct {
	ID          int    `json:"id"`          // 作用机制ID
	Name        string `json:"name"`        // 作用机制名称
	Description string `json:"description"` // 作用机制描述
}

type Indication struct {
	ID          int    `json:"id"`          // 适应症ID
	Name        string `json:"name"`        // 适应症名称
	Description string `json:"description"` // 适应症描述
}

type Contraindication struct {
	ID          int    `json:"id"`          // 禁忌症ID
	Name        string `json:"name"`        // 禁忌症名称
	Description string `json:"description"` // 禁忌症描述
}

type SideEffect struct {
	ID          int    `json:"id"`          // 副作用ID
	Name        string `json:"name"`        // 副作用名称
	Description string `json:"description"` // 副作用描述
}

type DosageForm struct {
	ID          int    `json:"id"`          // 剂型ID
	Name        string `json:"name"`        // 剂型名称
	Description string `json:"description"` // 剂型描述
}

type Medicine struct {
	EncountId                                string              `json:"encountId"`                                //就诊ID
	PatId                                    string              `json:"patId"`                                    //患者ID
	DrugID                                   string              `json:"drug_id"`                                  // 药品ID
	DrugName                                 string              `json:"drug_name"`                                // 药品名称
	Ingredients                              []Ingredient        `json:"ingredients"`                              // 成分列表
	MechanismsOfAction                       []MechanismOfAction `json:"mechanismsOfAction"`                       // 作用机制列表
	Indications                              []Indication        `json:"indications"`                              // 适应症列表
	Contraindications                        []Contraindication  `json:"contraindications"`                        // 禁忌症列表
	SideEffects                              []SideEffect        `json:"sideEffects"`                              // 副作用列表
	DosageForm                               DosageForm          `json:"dosageForm"`                               // 剂型
	RecommendedDosage                        string              `json:"recommendedDosage"`                        // 推荐剂量
	AdministrationRoute                      string              `json:"administrationRoute"`                      // 给药途径
	Code                                     string              `json:"code"`                                     //药品编码
	DosageformCode                           string              `json:"dosageformCode"`                           //剂型编码
	MinimumPharmacologicalClassificationCode string              `json:"minimumPharmacologicalClassificationCode"` //最小药理分类编码
	SingleDoseQuantity                       float64             `json:"singleDoseQuantity"`                       //剂量：数值
	SingleDoseUnit                           string              `json:"singleDoseUnit"`                           //剂量：单位
	MedicationDays                           string              `json:"medicationDays"`                           //用药天数
}

type MedicineStore struct {
	medicines     map[string]*Medicine // 使用 map 存储药品，键为 DrugID
	medicinesList []*Medicine
}

func NewMedicineStore() *MedicineStore {
	return &MedicineStore{
		medicines: make(map[string]*Medicine),
	}
}

func (store *MedicineStore) AddMedicine(medicine *Medicine) {
	store.medicines[medicine.DrugID] = medicine
}

func (store *MedicineStore) AddMedicineList(medicine []*Medicine) {
	store.medicinesList = append(store.medicinesList, medicine...)
}

// 获取药品信息
func (store *MedicineStore) GetMedicineByID(drugID string) *Medicine {
	medicine, exists := store.medicines[drugID]
	if !exists {
		return nil
	}
	return medicine
}

// 判断药品是否存在
func (store *MedicineStore) IsExistByID(drugID string) bool {
	_, exists := store.medicines[drugID]
	if !exists {
		return false
	}
	return true
}

// 一次判断多个药品是否存在
func (store *MedicineStore) IsAnyExistByIDs(drugIDs string) bool {
	ids := strings.Split(drugIDs, ";")
	for _, id := range ids {
		id = strings.TrimSpace(id) // 去除空格
		if store.IsExistByID(id) {
			return true
		}
	}
	return false
}

// 判断是否存在相同编码
func (store *MedicineStore) ExistsSameCode(drugIDs []string) bool {
	charMap := make(map[rune]bool)

	for _, str := range drugIDs {
		for _, char := range str {
			if charMap[char] {
				return true
			}
			charMap[char] = true
		}
	}

	return false
}

// 判断是否存在相同药理机制
func (store *MedicineStore) ExistsSamePharmacology(medicine *Medicine) bool {
	mechanismMap := make(map[int]bool)
	for _, mechanism := range medicine.MechanismsOfAction {
		if mechanismMap[mechanism.ID] {
			return true
		}
		mechanismMap[mechanism.ID] = true
	}
	return false
}

// 用药时长是否超过指定天数
func (store *MedicineStore) IsExceedDays(keys, value, max string) bool {
	totalMedicationDays := 0
	intmax, _ := strconv.Atoi(max)
	for _, medicine := range store.medicinesList {
		medicationDays, _ := strconv.Atoi(medicine.MedicationDays)
		switch keys {
		case "encountId":
			if medicine.EncountId == value {
				totalMedicationDays += medicationDays
			}
		case "patId":
			if medicine.PatId == value {
				totalMedicationDays += medicationDays
			}
		case "drug_id":
			if medicine.DrugID == value {
				totalMedicationDays += medicationDays
			}
		case "drug_name":
			if medicine.DrugName == value {
				totalMedicationDays += medicationDays
			}
		case "code":
			if medicine.Code == value {
				totalMedicationDays += medicationDays
			}
		case "dosageformCode":
			if medicine.DosageformCode == value {
				totalMedicationDays += medicationDays
			}
		case "minimumPharmacologicalClassificationCode":
			if medicine.MinimumPharmacologicalClassificationCode == value {
				totalMedicationDays += medicationDays
			}
		}
	}
	return totalMedicationDays > intmax
}

func (store *MedicineStore) IsExceedDaysNoKey(max string) bool {
	totalMedicationDays := 0
	intmax, _ := strconv.Atoi(max)
	for _, medicine := range store.medicinesList {
		medicationDays, _ := strconv.Atoi(medicine.MedicationDays)
		totalMedicationDays += medicationDays
	}
	return totalMedicationDays > intmax
}

// 重复药品校验（给定列表中的药品不可重复出现）
func (store *MedicineStore) HasDuplicateDrugID(any string) bool {
	drugIDMap := make(map[string]bool)
	for _, medicine := range store.medicinesList {
		if _, exists := drugIDMap[medicine.DrugID]; exists {
			return true // 发现重复的 DrugID
		}
		drugIDMap[medicine.DrugID] = true
	}
	return false
}

// 单次超剂量
func (store *MedicineStore) SingleOverdose(drugID, dose string) bool {
	// 将 doseStr 转换为 float64
	doseValue, _ := strconv.ParseFloat(dose, 64)
	// 遍历 medicinesList 查找匹配的药品
	for _, medicine := range store.medicinesList {
		if medicine.DrugID == drugID {
			if medicine.SingleDoseQuantity > doseValue {
				return true
			}
		}
	}
	return false
}

// 累计超剂量
func (store *MedicineStore) TotalOverdose(drugID, dose string) bool {
	// 将 doseStr 转换为 float64
	doseValue, _ := strconv.ParseFloat(dose, 64)
	totalDose := 0.0
	found := false
	// 遍历 medicinesList 查找匹配 drugID 的药品
	for _, medicine := range store.medicinesList {
		if medicine.DrugID == drugID {
			found = true
			totalDose += medicine.SingleDoseQuantity
		}
	}
	// 如果找到了至少一个匹配的药品，检查累计剂量是否大于给定值
	if found {
		return totalDose > doseValue
	}
	// 如果没有找到任何匹配的药品，则返回 false
	return false
}

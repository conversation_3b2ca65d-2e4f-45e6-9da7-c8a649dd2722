package dsl

import "strconv"

type TestReportDetail struct {
	DetailID       int64  `json:"detail_id"`
	ReportID       int64  `json:"report_id"`
	TestItemID     int64  `json:"test_item_id"`
	TestItemName   string `json:"test_item_name"`
	TestResult     string `json:"test_result"`
	ReferenceRange string `json:"reference_range"`
	Unit           string `json:"unit"`
	HighLowFlag    string `json:"high_low_flag"`
	UnitSoid       string `json:"unit_soid"`
	Msg            []Message
}

type ReqTestReportDetail struct {
	DetailID       string `json:"detail_id"`
	ReportID       string `json:"report_id"`
	TestItemID     string `json:"test_item_id"`
	TestItemName   string `json:"test_item_name"`
	TestResult     string `json:"test_result"`
	ReferenceRange string `json:"reference_range"`
	Unit           string `json:"unit"`
	HighLowFlag    string `json:"high_low_flag"`
	UnitSoid       string `json:"unit_soid"`
	Msg            []Message
}

// 报告用来做判断的依据
type TestReportCriteria struct {
	TestItemID int64
	MinResult  string
	UnitSoid   string
}

// NewTestReportDetailStore 初始化并返回一个新的 TestReportDetailStore 实例
type TestReportDetailStore struct {
	reports []*TestReportDetail
}

type TestReportDetailMapStore struct {
	mapreportsID         map[int64]*TestReportDetail
	mapreportsTestItemID map[int64]*TestReportDetail
}

func NewTestReportDetailStore() *TestReportDetailStore {
	return &TestReportDetailStore{
		reports: []*TestReportDetail{},
	}
}

func NewTestReportDetailMapStore() *TestReportDetailMapStore {
	return &TestReportDetailMapStore{
		mapreportsTestItemID: make(map[int64]*TestReportDetail),
		mapreportsID:         make(map[int64]*TestReportDetail),
	}
}

// AddReport 向存储中添加新的检验报告
func (store *TestReportDetailStore) AddReport(report *TestReportDetail) {
	store.reports = append(store.reports, report)
}

// 检验报告检查项目id
func (store *TestReportDetailMapStore) AddReportMapForTestItemID(report *TestReportDetail) {
	store.mapreportsTestItemID[report.TestItemID] = report
}

// 检验报告id
func (store *TestReportDetailMapStore) AddReportMapForID(report *TestReportDetail) {
	store.mapreportsID[report.ReportID] = report
}

// 判断检查报告是否存在指定项目
func (store *TestReportDetailMapStore) IsExistByTestItemID(test_item_id string) bool {
	inttest_item_id, _ := strconv.ParseInt(test_item_id, 10, 64)
	_, exists := store.mapreportsTestItemID[inttest_item_id]
	if !exists {
		return false
	}
	return true
}

// 判断检查报告是否存在
func (store *TestReportDetailMapStore) IsExistByID(report_id string) bool {
	intreport_id, _ := strconv.ParseInt(report_id, 10, 64)
	_, exists := store.mapreportsID[intreport_id]
	if !exists {
		return false
	}
	return true
}

// isReportValid 根据给定规则检查单个 TestReportDetail 是否符合条件
func isReportValid(report *TestReportDetail) bool {
	return ((report.TestItemID == 1796078696373392480 && report.TestResult > "40" && report.UnitSoid == "1803307063279611996") ||
		(report.TestItemID == 1796078868742509665 && report.TestResult > "50" && report.UnitSoid == "1803307063279611996") ||
		(report.TestItemID == 1699639165613012651 && report.TestResult > "21" && report.UnitSoid == "1803307407212539997") ||
		(report.TestItemID == 1699639165696900155 && report.TestResult > "6" && report.UnitSoid == "1803307407212539997") ||
		(report.TestItemID == 1699639165604623898 && report.TestResult > "17" && report.UnitSoid == "1803307407212539997"))
}

// IsAnyReportValid 检查存储中是否有任意一个 TestReportDetail 符合条件
func (store *TestReportDetailStore) IsAnyReportValid() bool {
	for _, report := range store.reports {
		if isReportValid(report) {
			return true
		}
	}
	return false
}

// 输出检查报告的结果
func (t *TestReportDetail) ReportMessage(id string, name string, messgetype string) {
	message := Message{
		MessageID:   id,
		MessageDesc: name,
		MessageType: messgetype,
	}
	t.Msg = append(t.Msg, message)
}

// 判断单条诊断是否合规
func (t *TestReportDetail) IsQualified(test_item_id, result, unit string) bool {

	return true
}

// 规则以入参方式传入
//func isReportValid(report *TestReportDetail, criteriaList []TestReportCriteria) bool {
//	for _, criteria := range criteriaList {
//		if report.TestItemID == criteria.TestItemID && report.TestResult > criteria.MinResult && report.UnitSoid == criteria.UnitSoid {
//			return true
//		}
//	}
//	return false
//}
//
//func (store *TestReportDetailStore) IsAnyReportValid(criteriaList []TestReportCriteria) bool {
//	for _, report := range store.reports {
//		if isReportValid(report, criteriaList) {
//			return true
//		}
//	}
//	return false
//}

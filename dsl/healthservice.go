package dsl

type HealthService struct {
	Id                  int8   `json:"id"`                   //服务编码
	Active              bool   `json:"active"`               //是否激活
	ProvidedBy          string `json:"provided_by"`          //服务提供者
	OfferedIn           int8   `json:"offered_in"`           // 提供服务的服务
	Category            string `json:"category"`             //服务分类
	Type                string `json:"type"`                 //服务类型
	Eligibility         string `json:"eligibility"`          //服务资格
	AppointmentRequired bool   `json:"appointment_required"` //服务是否需要预约
	Availability        string `json:"availability"`         //服务是否可以用
}

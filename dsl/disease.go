package dsl

type DiagnosisMethod struct {
	ID          int    `json:"id"`          // 诊断方法ID
	Name        string `json:"name"`        // 诊断方法名称
	Description string `json:"description"` // 诊断方法描述
}

type TreatmentMethod struct {
	ID          int    `json:"id"`          // 治疗方法ID
	Name        string `json:"name"`        // 治疗方法名称
	Description string `json:"description"` // 治疗方法描述
}

type PreventiveMeasure struct {
	ID          int    `json:"id"`          // 预防措施ID
	Name        string `json:"name"`        // 预防措施名称
	Description string `json:"description"` // 预防措施描述
}

type DiseaseProgression struct {
	ID          int    `json:"id"`          // 疾病进展ID
	Stage       string `json:"stage"`       // 疾病阶段
	Description string `json:"description"` // 阶段描述
}

type Complication struct {
	ID          int    `json:"id"`          // 并发症ID
	Name        string `json:"name"`        // 并发症名称
	Description string `json:"description"` // 并发症描述
}

type CommonCause struct {
	ID          int    `json:"id"`          // 常见原因ID
	Name        string `json:"name"`        // 常见原因名称
	Description string `json:"description"` // 常见原因描述
}

type Disease struct {
	EncountId          string               `json:"encountId"`          //就诊ID
	PatId              string               `json:"patId"`              //患者ID
	Symptoms           []Symptom            `json:"symptoms"`           // 症状列表
	DiagnosisMethods   []DiagnosisMethod    `json:"diagnosisMethods"`   // 诊断方法列表
	Diagnosis          []string             `json:"diagnosis_list"`     // 诊断编码列表
	TreatmentMethods   []TreatmentMethod    `json:"treatmentMethods"`   // 治疗方法列表
	PreventiveMeasures []PreventiveMeasure  `json:"preventiveMeasures"` // 预防措施列表
	DiseaseProgress    []DiseaseProgression `json:"diseaseProgress"`    // 疾病进展列表
	Complications      []Complication       `json:"complications"`      // 并发症列表
	CommonCauses       []CommonCause        `json:"commonCauses"`       // 常见原因列表
	DiseaseCode        string               `json:"diseaseCode"`        //病种编码
	DiagnosisCode      string               `json:"diagnosisCode"`      //诊断编码
	DiseaseID          string               `json:"disease_id"`         // 疾病ID
	DiseaseName        string               `json:"disease_name"`       // 疾病名称
}

type DiseaseStore struct {
	mapDisease        map[string]*Disease
	diagnosisCodeList []string
}

func NewDiseaseMapStore() *DiseaseStore {
	return &DiseaseStore{
		mapDisease:        make(map[string]*Disease),
		diagnosisCodeList: make([]string, 0),
	}
}

func (mapStore *DiseaseStore) AddReportMap(disease *Disease) {
	mapStore.mapDisease[disease.DiseaseID] = disease
}

func (mapStore *DiseaseStore) AddDiagnosisCodeList(codeList []string) {
	mapStore.diagnosisCodeList = codeList
}

// 判断疾病是否存在
func (mapStore *DiseaseStore) IsExistByDiseaseID(disease_id string) bool {
	_, exists := mapStore.mapDisease[disease_id]
	if !exists {
		return false
	}
	return true
}

// 通过诊断判断疾病是否存在
func (mapStore *DiseaseStore) IsExistByDiagnosis(disease_id string) bool {
	for _, item := range mapStore.diagnosisCodeList {
		if item == disease_id {
			return true
		}
	}
	return false
}

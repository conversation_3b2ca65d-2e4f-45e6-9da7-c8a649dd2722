package dsl

type Application struct {
	EncountId                string `json:"encountId"`                    //就诊ID
	PatId                    string `json:"patId"`                        //患者ID
	ApplicationType          string `json:"application_type"`             //申请类型编码
	ApplicationStatus        string `json:"application_status"`           //申请状态编码
	ApplicationNumber        int    `json:"application_number"`           //申请数量
	ApplicationDayTime       int    `json:"application_day_time"`         //申请日期时间
	DayTimeToApplication     int    `json:"day_time_to_application"`      //申请距当前时间：数值
	DayTimeToApplicationUnit string `json:"day_time_to_application_unit"` //申请距当前时间：单位
}

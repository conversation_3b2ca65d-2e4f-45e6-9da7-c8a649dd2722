package executionrecord

type ExaminationExecutionRecord struct {
	ExecutionItem string                           `json:"execution_item"` //执行项目
	ExecutionTime string                           `json:"execution_time"` //执行时间
	Store         *ExaminationExecutionRecordStore `json:"-"`
}

type ExaminationExecutionRecordStore struct {
	ExaminationExecutionRecordMap  map[string]*ExaminationExecutionRecord // 使用 map 存储药品，键为 medical_id
	ExaminationExecutionRecordList []*ExaminationExecutionRecord
}

func NewExaminationExecutionRecordStore() *ExaminationExecutionRecordStore {
	return &ExaminationExecutionRecordStore{
		ExaminationExecutionRecordMap:  make(map[string]*ExaminationExecutionRecord),
		ExaminationExecutionRecordList: make([]*ExaminationExecutionRecord, 0),
	}
}

func (e *ExaminationExecutionRecord) SetStore(store *ExaminationExecutionRecordStore) {
	e.Store = store
}

func (e *ExaminationExecutionRecordStore) AddExaminationMap(r *ExaminationExecutionRecord) {
	e.ExaminationExecutionRecordMap[r.ExecutionItem] = r
}

func (e *ExaminationExecutionRecordStore) AddExaminationList(r []*ExaminationExecutionRecord) {
	e.ExaminationExecutionRecordList = r
}

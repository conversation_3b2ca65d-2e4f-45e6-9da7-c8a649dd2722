package executionrecord

import (
	"strconv"
	"time"
)

type WesternMedicineExecutionRecord struct {
	ExecutionItem string                               `json:"execution_item"` //执行项目
	ExecutionTime string                               `json:"execution_time"` //执行时间
	Store         *WesternMedicineExecutionRecordStore `json:"-"`
}

type WesternMedicineExecutionRecordStore struct {
	westernMedicineExecutionRecordMap  map[string]*WesternMedicineExecutionRecord // 使用 map 存储药品，键为 medical_id
	westernMedicineExecutionRecordList []*WesternMedicineExecutionRecord
}

func NewMedicineDoctorOrdersStore() *WesternMedicineExecutionRecordStore {
	return &WesternMedicineExecutionRecordStore{
		westernMedicineExecutionRecordMap:  make(map[string]*WesternMedicineExecutionRecord),
		westernMedicineExecutionRecordList: make([]*WesternMedicineExecutionRecord, 0),
	}
}

func (w *WesternMedicineExecutionRecord) SetStore(store *WesternMedicineExecutionRecordStore) {
	w.Store = store
}

func (w *WesternMedicineExecutionRecordStore) AddMedicalMap(r *WesternMedicineExecutionRecord) {
	w.westernMedicineExecutionRecordMap[r.ExecutionItem] = r
}

func (w *WesternMedicineExecutionRecordStore) AddMedicalList(r []*WesternMedicineExecutionRecord) {
	w.westernMedicineExecutionRecordList = r
}

func (w *WesternMedicineExecutionRecordStore) CheckExecutionRecordExistInDaysStore(days int, itemID string) bool {
	now := time.Now()
	startTime := now.AddDate(0, 0, -days).Unix()
	for _, order := range w.westernMedicineExecutionRecordList {
		if order == nil {
			continue
		}
		if order.ExecutionItem != itemID {
			continue
		}
		reviewTimeTimestamp, err := strconv.ParseInt(order.ExecutionTime, 10, 64)
		if err != nil {
			continue
		}
		// 判断该记录的审核时间是否在指定时间范围内
		if reviewTimeTimestamp >= startTime {
			return true // 如果找到了符合条件的手术项目，立即返回 true
		}
	}
	return false
}

// TODO:规定时间内是否存在某项执行记录
func (w *WesternMedicineExecutionRecord) CheckExecutionRecordExistInDays(days int, erItem string) bool {
	if w.Store == nil {
		return false
	}
	return w.Store.CheckExecutionRecordExistInDaysStore(days, erItem)
}

package executionrecord

type SurgicalExecutionRecord struct {
	ExecutionItem string                        `json:"execution_item"` //执行项目
	ExecutionTime string                        `json:"execution_time"` //执行时间
	Store         *SurgicalExecutionRecordStore `json:"-"`
}

type SurgicalExecutionRecordStore struct {
	SurgicalExecutionRecordMap  map[string]*SurgicalExecutionRecord // 使用 map 存储药品，键为 medical_id
	SurgicalExecutionRecordList []*SurgicalExecutionRecord
}

func NewSurgicalExecutionRecordStore() *SurgicalExecutionRecordStore {
	return &SurgicalExecutionRecordStore{
		SurgicalExecutionRecordMap:  make(map[string]*SurgicalExecutionRecord),
		SurgicalExecutionRecordList: make([]*SurgicalExecutionRecord, 0),
	}
}

func (s *SurgicalExecutionRecord) SetStore(store *SurgicalExecutionRecordStore) {
	s.Store = store
}

func (s *SurgicalExecutionRecordStore) AddSurgicalExecutionRecordMap(r *SurgicalExecutionRecord) {
	s.SurgicalExecutionRecordMap[r.ExecutionItem] = r
}

func (s *SurgicalExecutionRecordStore) AddSurgicalExecutionRecordList(r []*SurgicalExecutionRecord) {
	s.SurgicalExecutionRecordList = r
}

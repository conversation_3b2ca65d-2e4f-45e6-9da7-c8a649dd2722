package executionrecord

type LabExaminationExecutionRecord struct {
	ExecutionItem string                              `json:"execution_item"` //执行项目
	ExecutionTime string                              `json:"execution_time"` //执行时间
	Store         *LabExaminationExecutionRecordStore `json:"-"`
}

type LabExaminationExecutionRecordStore struct {
	LabExaminationExecutionRecordMap  map[string]*LabExaminationExecutionRecord // 使用 map 存储药品，键为 medical_id
	LabExaminationExecutionRecordList []*LabExaminationExecutionRecord
}

func NewLabExaminationExecutionRecordStore() *LabExaminationExecutionRecordStore {
	return &LabExaminationExecutionRecordStore{
		LabExaminationExecutionRecordMap:  make(map[string]*LabExaminationExecutionRecord),
		LabExaminationExecutionRecordList: make([]*LabExaminationExecutionRecord, 0),
	}
}

func (l *LabExaminationExecutionRecord) SetStore(store *LabExaminationExecutionRecordStore) {
	l.Store = store
}

func (l *LabExaminationExecutionRecordStore) AddLabExaminationExecutionRecordMap(r *LabExaminationExecutionRecord) {
	l.LabExaminationExecutionRecordMap[r.ExecutionItem] = r
}

func (l *LabExaminationExecutionRecordStore) AddLabExaminationExecutionRecordList(r []*LabExaminationExecutionRecord) {
	l.LabExaminationExecutionRecordList = r
}

package dsl

type Prescription struct {
	EncountId            string     `json:"encountId"` //就诊ID
	PatId                string     `json:"patId"`     //患者ID
	Drugs                []Medicine //药品列表
	PrescriptionType     string     `json:"prescription_type"`      //处方类型
	NumberOfMedicine     int        `json:"number_of_medicine"`     //药品数量
	DateOfPrescription   string     `json:"date_of_prescription"`   //处方日期
	HospitalDistrictCode string     `json:"hospital_district_code"` //医院代码
	DepartmentCode       string     `json:"department_code"`        //部门代码
	ProgramCode          string     `json:"program_code"`           //项目编码
	ClinicalService      string     `json:"clinical_service"`       //临床服务代码
	Frequency            float64    `json:"frequency"`              //
	SingleDoseQuantity   string     `json:"single_dose_quantity"`
	SingleDoseUnit       string     `json:"single_dose_unit"`
	Numberofdays         int        `json:"number_ofdays"`
	TotalnumberQuantity  int        `json:"totalnumber_quantity"`
	TotalQuantityUnit    string     `json:"totalnumber_unit"`
	AmountQuantity       string     `json:"amount_quantity"`
	AmountUnit           string     `json:"amount_unit"`
}

type PrescriptionStore struct {
	prescriptions []*Prescription
}

// t获取处方
func (p *Prescription) GetDiagnosis() string {

	return ""
}

//func (p *PrescriptionStore) GetMedicineByID(recipe_id string) *Prescription {
//
//	prescription, exists := p.prescriptions[recipe_id]
//	if !exists {
//		return nil
//	}
//	return prescription
//}

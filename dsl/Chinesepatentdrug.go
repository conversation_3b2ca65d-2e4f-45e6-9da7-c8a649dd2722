package dsl

type ChinesePatentDrug struct {
	EncountId                                string  `json:"encountId"`                                //就诊ID
	PatId                                    string  `json:"patId"`                                    //患者ID
	DrugID                                   string  `json:"drug_id"`                                  // 药品ID
	DrugName                                 string  `json:"drug_name"`                                // 药品名称
	RecommendedDosage                        string  `json:"recommendedDosage"`                        // 推荐剂量
	AdministrationRoute                      string  `json:"administrationRoute"`                      // 给药途径
	Code                                     string  `json:"code"`                                     //药品编码
	DosageformCode                           string  `json:"dosageformCode"`                           //剂型编码
	MinimumPharmacologicalClassificationCode string  `json:"minimumPharmacologicalClassificationCode"` //最小药理分类编码
	SingleDoseQuantity                       float64 `json:"singleDoseQuantity"`                       //剂量：数值
	SingleDoseUnit                           string  `json:"singleDoseUnit"`                           //剂量：单位
	MedicationDays                           int     `json:"medicationDays"`                           //用药天数
}

type ChinesePatentDrugStore struct {
	Chinesepatentdrug map[string]*ChinesePatentDrug
}

func NewChinesePatentDrugStore() *ChinesePatentDrugStore {
	return &ChinesePatentDrugStore{
		Chinesepatentdrug: make(map[string]*ChinesePatentDrug),
	}
}

func (store *ChinesePatentDrugStore) AddMedicine(medicine *ChinesePatentDrug) {
	store.Chinesepatentdrug[medicine.DrugID] = medicine
}

func (store *ChinesePatentDrugStore) IsExistByID(drugID string) bool {
	_, exists := store.Chinesepatentdrug[drugID]
	if !exists {
		return false
	}
	return true
}

package dsl

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"
)

type LabReport struct {
	ID             int          `json:"id"`             // 报告ID
	EncountId      string       `json:"encountId"`      //就诊ID
	PatId          string       `json:"patId"`          //患者ID
	ReportDate     time.Time    `json:"reportDate"`     // 报告日期
	OrderedBy      string       `json:"orderedBy"`      // 开单医生
	TestResults    []TestResult `json:"testResults"`    // 检验结果列表
	Interpretation string       `json:"interpretation"` // 结果解释
}

type TestResult struct {
	TestID        int    `json:"testId"`        // 检验结果ID
	TestName      string `json:"testName"`      // 检验项目名称
	TestIndicator string `json:"testIndicator"` //指标ID
	Result        string `json:"result"`        // 检验结果
	Reference     string `json:"reference"`     // 参考范围
	Unit          string `json:"unit"`          // 单位
}

// LabReportService 接口定义
type LabReportService interface {
	CreateLabReport(report LabReport) (LabReport, error)
	GetLabReportByID(id int) (LabReport, error)
	GetTestResultByID(reportID int, testID int) (TestResult, error)
}

// 实现LabReportService接口
type labReportServiceImpl struct {
	reports []LabReport
	nextID  int
}

func NewLabReportService() LabReportService {
	return &labReportServiceImpl{
		reports: []LabReport{},
		nextID:  1,
	}
}
func (s *labReportServiceImpl) CreateLabReport(report LabReport) (LabReport, error) {
	report.ID = s.nextID
	s.nextID++
	s.reports = append(s.reports, report)
	return report, nil
}

func (s *labReportServiceImpl) GetLabReportByID(id int) (LabReport, error) {
	for _, report := range s.reports {
		if report.ID == id {
			return report, nil
		}
	}
	return LabReport{}, fmt.Errorf("report not found")
}

func (s *labReportServiceImpl) GetTestResultByID(reportID int, testID int) (TestResult, error) {
	report, err := s.GetLabReportByID(reportID)
	if err != nil {
		return TestResult{}, err
	}
	for _, result := range report.TestResults {
		if result.TestID == testID {
			return result, nil
		}
	}
	return TestResult{}, fmt.Errorf("test result not found")
}

func main() {
	service := NewLabReportService()

	http.HandleFunc("/createLabReport", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			http.Error(w, "Invalid request method", http.StatusMethodNotAllowed)
			return
		}

		var report LabReport
		if err := json.NewDecoder(r.Body).Decode(&report); err != nil {
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}

		createdReport, err := service.CreateLabReport(report)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		w.WriteHeader(http.StatusCreated)
		json.NewEncoder(w).Encode(createdReport)
	})

	http.HandleFunc("/getLabReport", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "GET" {
			http.Error(w, "Invalid request method", http.StatusMethodNotAllowed)
			return
		}

		idParam := r.URL.Query().Get("id")
		if idParam == "" {
			http.Error(w, "ID parameter is required", http.StatusBadRequest)
			return
		}

		id, err := strconv.Atoi(idParam)
		if err != nil {
			http.Error(w, "Invalid ID parameter", http.StatusBadRequest)
			return
		}

		report, err := service.GetLabReportByID(id)
		if err != nil {
			http.Error(w, err.Error(), http.StatusNotFound)
			return
		}

		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(report)
	})

	http.ListenAndServe(":8080", nil)
}

package dsl

import "errors"

var reminderTypes = map[string]string{
	"01": "Information（信息）",
	"02": "Warning（警告）",
	"03": "Recommendation（建议）",
	"04": "Guideline（指南）",
	"05": "Reminder（提醒）",
	"06": "<PERSON><PERSON>（警报）",
	"07": "Compliance（合规性）",
}

func getReminderType(code string) (string, error) {
	if reminderType, exists := reminderTypes[code]; exists {
		return reminderType, nil
	}
	return "", errors.New("invalid code")
}

type Message struct {
	MessageID   string `json:"message_id"` //消息ID
	MessageDesc string `json:"message"`    //消息内容
	MessageType string `json:"type"`       //消息类型
}

package dsl

import (
	"strconv"
	"time"
)

type ExecutionRecord struct {
	ExecutionItem     string                `json:"execution_item"`     //执行项目
	ExecutionDateTime string                `json:"execution_datetime"` //执行时间
	Store             *ExecutionRecordStore `json:"-"`
}

type ExecutionRecordStore struct {
	executionRecordMap  map[string]*ExecutionRecord // 使用 map 存储药品，键为 medical_id
	executionRecordList []*ExecutionRecord
}

func NewExecutionRecordStore() *ExecutionRecordStore {
	return &ExecutionRecordStore{
		executionRecordMap:  make(map[string]*ExecutionRecord),
		executionRecordList: make([]*ExecutionRecord, 0),
	}
}

func (e *ExecutionRecord) SetStore(store *ExecutionRecordStore) {
	e.Store = store
}

func (e *ExecutionRecordStore) AddMedicalMap(medical *ExecutionRecord) {
	e.executionRecordMap[medical.ExecutionItem] = medical
}

func (e *ExecutionRecordStore) AddMedicalList(medicalList []*ExecutionRecord) {
	e.executionRecordList = medicalList
}

func (e *ExecutionRecordStore) ExistsWithinDaysStore(days int, executionItem string) bool {
	cutoffTime := time.Now().AddDate(0, 0, -days)
	for _, record := range e.executionRecordList {
		if record.ExecutionItem == executionItem {
			intTime, _ := strconv.ParseInt(record.ExecutionDateTime, 10, 64)
			executionTime := time.Unix(intTime, 0).UTC()
			if executionTime.After(cutoffTime) {
				return true
			}
		}
	}
	return false
}

// TODO:判断周期内是否存在重复的执行项目
func (e *ExecutionRecord) ExistsWithinDays(days int, executionItem string) bool {
	if e.Store == nil {
		return false
	}
	return e.Store.ExistsWithinDaysStore(days, executionItem)
}

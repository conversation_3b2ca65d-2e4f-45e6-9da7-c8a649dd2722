package dsl

/*
	symptom := Symptom{
	    Name:       "头痛",
	    Nature:     "阵发性",
	    Severity:   "中等",
	    Duration:   "几小时",
	    Frequency:  "偶尔",
	    Associated: []string{"恶心", "眩晕"},
	    Impact:     "影响工作",
	    Triggers:   []string{"压力", "疲劳"},
	}
*/
type Symptom struct {
	Name       string   `json:"name"`       // 名称
	Nature     string   `json:"nature"`     // 性质
	Severity   string   `json:"severity"`   // 程度
	Duration   string   `json:"duration"`   // 持续时间
	Frequency  string   `json:"frequency"`  // 频率
	Associated []string `json:"associated"` // 伴随症状
	Impact     string   `json:"impact"`     // 影响活动
	Triggers   []string `json:"triggers"`   // 发作触发因素
}

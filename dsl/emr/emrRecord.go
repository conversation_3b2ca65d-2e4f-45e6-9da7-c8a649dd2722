package emr

type EmrRecords struct {
	DataCreatedTime      string           `json:"data_created_time"`      //数据创建时间
	MedicalInsuranceType string           `json:"medical_insurance_type"` //病例类型
	DataUpdatedTime      string           `json:"data_updated_time"`      //数据修改时间
	Store                *EmrRecordsStore `json:"-"`
}

type EmrRecordsStore struct {
	emrRecordsMap  map[string]*EmrRecords // 使用 map 存储药品，键为 medical_id
	emrRecordsList []*EmrRecords
}

func NewMedicineDoctorOrdersStore() *EmrRecordsStore {
	return &EmrRecordsStore{
		emrRecordsMap:  make(map[string]*EmrRecords),
		emrRecordsList: make([]*EmrRecords, 0),
	}
}

func (e *EmrRecords) SetStore(store *EmrRecordsStore) {
	e.Store = store
}

func (e *EmrRecordsStore) AddMedicalMap(r *EmrRecords) {
	e.emrRecordsMap[r.MedicalInsuranceType] = r
}

func (e *EmrRecordsStore) AddMedicalList(r []*EmrRecords) {
	e.emrRecordsList = r
}

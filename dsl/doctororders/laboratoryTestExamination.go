package doctororders

type LabTestExamDoctorOrder struct {
	LaboratoryTestOrder string                       `json:"laboratory_test_order"` //检验项目
	ReviewTime          string                       `json:"review_time"`
	OrderDuration       string                       `json:"order_duration"`
	UrgentMark          string                       `json:"urgent_mark"`
	OrderCreateTime     string                       `json:"order_create_time"`
	Store               *LabTestExamDoctorOrderStore `json:"-"`
}

type LabTestExamDoctorOrderStore struct {
	labTestExamDoctorOrderMap  map[string]*LabTestExamDoctorOrder // 使用 map 存储药品，键为 medical_id
	labTestExamDoctorOrderList []*LabTestExamDoctorOrder
}

func NewLabTestExamDoctorOrderStore() *LabTestExamDoctorOrderStore {
	return &LabTestExamDoctorOrderStore{
		labTestExamDoctorOrderMap:  make(map[string]*LabTestExamDoctorOrder),
		labTestExamDoctorOrderList: make([]*LabTestExamDoctorOrder, 0),
	}
}

func (l *LabTestExamDoctorOrder) SetStore(store *LabTestExamDoctorOrderStore) {
	l.Store = store
}

func (l *LabTestExamDoctorOrderStore) AddLabTestExamOrderMap(lab *LabTestExamDoctorOrder) {
	l.labTestExamDoctorOrderMap[lab.LaboratoryTestOrder] = lab
}

func (l *LabTestExamDoctorOrderStore) AddLabTestExamOrderList(labList []*LabTestExamDoctorOrder) {
	l.labTestExamDoctorOrderList = labList
}

func (l *LabTestExamDoctorOrderStore) IsExistByLabExamIDStore(item string) bool {
	_, exists := l.labTestExamDoctorOrderMap[item]
	if !exists {
		return false
	}
	return true
}

// TODO:判断检验项目是否存在
func (l *LabTestExamDoctorOrder) IsExistByLabExamID(item string) bool {
	if l.Store == nil {
		return false
	}
	return l.Store.IsExistByLabExamIDStore(item)
}

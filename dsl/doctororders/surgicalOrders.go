package doctororders

import (
	"strconv"
	"time"
)

type SurgicalDoctorOrder struct {
	SurgicalOrderitem string                    `json:"surgical_order_item"` //手术医嘱项目
	SurgeryDateTime   string                    `json:"surgery_date_time"`   //手术日期时间
	OrderDuration     string                    `json:"order_duration"`      //医嘱时效性
	ReviewTime        string                    `json:"review_time"`         //审核时间
	UrgentMark        string                    `json:"urgent_mark"`         //加急标识
	Store             *SurgicalDoctorOrderStore `json:"-"`
}

type SurgicalDoctorOrderStore struct {
	surgicalDoctorOrdersMap  map[string]*SurgicalDoctorOrder // 使用 map 存储药品，键为 medical_id
	surgicalDoctorOrdersList []*SurgicalDoctorOrder
}

func NewSurgicalDoctorOrdersStore() *SurgicalDoctorOrderStore {
	return &SurgicalDoctorOrderStore{
		surgicalDoctorOrdersMap:  make(map[string]*SurgicalDoctorOrder),
		surgicalDoctorOrdersList: make([]*SurgicalDoctorOrder, 0),
	}
}

func (s *SurgicalDoctorOrder) SetStore(store *SurgicalDoctorOrderStore) {
	s.Store = store
}

func (s *SurgicalDoctorOrderStore) AddSurgicalDoctorOrdersMap(surgical *SurgicalDoctorOrder) {
	s.surgicalDoctorOrdersMap[surgical.SurgicalOrderitem] = surgical
}

func (s *SurgicalDoctorOrderStore) AddSurgicalDoctorOrdersList(surgicalList []*SurgicalDoctorOrder) {
	s.surgicalDoctorOrdersList = surgicalList
}

func (s *SurgicalDoctorOrderStore) IsExistBySurgicalOrderItemStore(item string) bool {
	_, exists := s.surgicalDoctorOrdersMap[item]
	if !exists {
		return false
	}
	return true
}

// TODO:判断手术项目是否存在
func (s *SurgicalDoctorOrder) IsExistBySurgicalOrderItem(item string) bool {
	if s.Store == nil {
		return false
	}
	return s.Store.IsExistBySurgicalOrderItemStore(item)
}

func (s *SurgicalDoctorOrderStore) CheckSurgicalOrderExistInDaysStore(days int, surgicalOrderItem string) bool {
	now := time.Now()
	startTime := now.AddDate(0, 0, -days).Unix()
	for _, order := range s.surgicalDoctorOrdersList {
		if order == nil {
			continue
		}
		if order.SurgicalOrderitem != surgicalOrderItem {
			continue
		}
		reviewTimeTimestamp, err := strconv.ParseInt(order.ReviewTime, 10, 64)
		if err != nil {
			continue
		}
		// 判断该记录的审核时间是否在指定时间范围内
		if reviewTimeTimestamp >= startTime {
			return true // 如果找到了符合条件的手术项目，立即返回 true
		}
	}
	return false
}

// TODO:术前时间段内是否有对应的手术项目
func (s *SurgicalDoctorOrder) CheckSurgicalOrderExistInDays(days int, surgicalOrderItem string) bool {
	if s.Store == nil {
		return false
	}
	return s.Store.CheckSurgicalOrderExistInDaysStore(days, surgicalOrderItem)
}

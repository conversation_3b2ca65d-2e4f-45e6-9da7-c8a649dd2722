package doctororders

import (
	"strings"
)

type WesternMedicineDoctorOrders struct {
	DrugDosageUnit             string                            `json:"drug_dosage_unit"`             //剂量：单位
	OrderDuration              string                            `json:"order_duration"`               //医嘱时效性
	MedicationPrescriptionItem string                            `json:"medication_prescription_item"` //药品ID
	RouteOfAdministration      string                            `json:"route_of_administration"`      //给药途径
	DrugDosage                 float64                           `json:"drug_dosage"`                  //剂量：数值（可能为小数）
	CourseOfTreatment          string                            `json:"course_of_treatment"`          //疗程
	ReviewTime                 string                            `json:"review_time"`                  //审核时间
	DailyDosage                string                            `json:"daily_dosage"`                 //药品单日用量
	UrgentMark                 string                            `json:"urgent_marks"`                 //加急标识
	UnitOfTreatmentCourse      string                            `json:"unit_of_treatment_course"`     //疗程单位
	OrderCreateTime            string                            `json:"order_create_time"`            //医嘱开立时间
	OrderExecutionTime         string                            `json:"order_execution_time"`         //执行时间
	Store                      *WesternMedicineDoctorOrdersStore `json:"-"`                            // 对 WesternMedicineDoctorOrdersStore 的引用
}

const (
	OrderDuration         = "OrderDuration"
	UrgentMark            = "UrgentMark"
	DrugDosageUnit        = "DrugDosageUnit"
	RouteOfAdministration = "RouteOfAdministration"
)

type WesternMedicineDoctorOrdersStore struct {
	medicineDoctorOrdersMap  map[string]*WesternMedicineDoctorOrders // 使用 map 存储药品，键为 medical_id
	medicineDoctorOrdersList []*WesternMedicineDoctorOrders
}

func NewMedicineDoctorOrdersStore() *WesternMedicineDoctorOrdersStore {
	return &WesternMedicineDoctorOrdersStore{
		medicineDoctorOrdersMap:  make(map[string]*WesternMedicineDoctorOrders),
		medicineDoctorOrdersList: make([]*WesternMedicineDoctorOrders, 0),
	}
}

func (w *WesternMedicineDoctorOrders) SetStore(store *WesternMedicineDoctorOrdersStore) {
	w.Store = store
}

func (w *WesternMedicineDoctorOrdersStore) AddMedicalMap(medical *WesternMedicineDoctorOrders) {
	w.medicineDoctorOrdersMap[medical.MedicationPrescriptionItem] = medical
}

func (w *WesternMedicineDoctorOrdersStore) AddMedicalList(medicalList []*WesternMedicineDoctorOrders) {
	w.medicineDoctorOrdersList = medicalList
}

func (w *WesternMedicineDoctorOrdersStore) IsExistByIDStore(drugID string) bool {
	_, exists := w.medicineDoctorOrdersMap[drugID]
	if !exists {
		return false
	}
	return true
}

// TODO:判断药品是否存在
func (w *WesternMedicineDoctorOrders) IsExistByID(medical_id string) bool {
	if w.Store == nil {
		return false
	}
	return w.Store.IsExistByIDStore(medical_id)
}

func (store *WesternMedicineDoctorOrdersStore) AreMedicationsInSameGroupStore(a, b string) bool {
	//groupMap := make(map[string]map[string]bool)
	//for _, order := range store.medicineDoctorOrdersList {
	//	if order == nil {
	//		continue
	//	}
	//	if _, exists := groupMap[order.GroupID]; !exists {
	//		groupMap[order.GroupID] = make(map[string]bool)
	//	}
	//	// 将药品 medical_id 添加到对应的 group_id 中
	//	groupMap[order.GroupID][order.MedicationPrescriptionItem] = true
	//}
	//// 遍历所有的 group_id，检查是否存在同时包含 a 和 b 的组
	//for _, medicalIDMap := range groupMap {
	//	if medicalIDMap[a] && medicalIDMap[b] {
	//		return true
	//	}
	//}
	//// 如果没有找到指定的两个药
	return false
}

// TODO:判断同组内是否存在指定的两个药品id
func (w *WesternMedicineDoctorOrders) AreMedicationsInSameGroup(a, b string) bool {
	if w.Store == nil {
		return false
	}
	return w.Store.AreMedicationsInSameGroupStore(a, b)
}

// 判断多个药品是否同时存在
func (w *WesternMedicineDoctorOrdersStore) IsAnyExistByIDs(drugIDs string) bool {
	ids := strings.Split(drugIDs, ";")
	for _, id := range ids {
		id = strings.TrimSpace(id) // 去除空格
		if w.IsExistByIDStore(id) {
			return true
		}
	}
	return false
}

//func (w *WesternMedicineDoctorOrdersStore) QuantityOfDrugUsedInSpecifiedDaysStore(days int, doseUnit string, maxDose float64, medicineNames []string) bool {
//	now := time.Now()
//	// 计算向前推的时间范围，得到天数范围内的起始时间戳
//	startTime := now.AddDate(0, 0, -days).Unix()
//	totalDose := 0.0 // 累计剂量
//
//	// Create a map for quick lookup of medicine names
//	medicineNameSet := make(map[string]struct{})
//	for _, name := range medicineNames {
//		medicineNameSet[name] = struct{}{}
//	}
//
//	for _, order := range w.medicineDoctorOrdersList {
//		if order == nil {
//			continue
//		}
//
//		// Check if the order's MedicalName exists in the medicineNameSet
//		if _, exists := medicineNameSet[order.MedicalName]; !exists {
//			continue
//		}
//
//		if order.DrugDosageUnit != doseUnit {
//			continue
//		}
//
//		orderBeginTimestamp, err := strconv.ParseInt(order.OrderBegin, 10, 64)
//		if err != nil {
//			continue
//		}
//		if orderBeginTimestamp < startTime {
//			continue
//		}
//
//		totalDose += order.DrugDosage
//	}
//
//	return totalDose <= maxDose
//}
//
//// TODO:规定天数内的药品总用量统计方法
//func (w *WesternMedicineDoctorOrders) QuantityOfDrugUsedInSpecifiedDays(days int, doseUnit string, maxDose float64, medicineName ...string) bool {
//	if w.Store == nil {
//		return false
//	}
//	return w.Store.QuantityOfDrugUsedInSpecifiedDaysStore(days, doseUnit, maxDose, medicineName)
//}

//func (s *WesternMedicineDoctorOrdersStore) CheckWesternMedicineDoctorOrdersExistInDaysStore(days int, MedicalID string) bool {
//	now := time.Now()
//	startTime := now.AddDate(0, 0, -days).Unix()
//	for _, order := range s.medicineDoctorOrdersList {
//		if order == nil {
//			continue
//		}
//		if order.MedicationPrescriptionItem != MedicalID {
//			continue
//		}
//		reviewTimeTimestamp, err := strconv.ParseInt(order.OrderBegin, 10, 64)
//		if err != nil {
//			continue
//		}
//		// 判断该记录的审核时间是否在指定时间范围内
//		if reviewTimeTimestamp >= startTime {
//			return true // 如果找到了符合条件的手术项目，立即返回 true
//		}
//	}
//	return false
//}
//
//// TODO:规定时间段内是否存在该药品
//func (s *WesternMedicineDoctorOrders) CheckWesternMedicineDoctorOrdersExistInDays(days int, MedicalID string) bool {
//	if s.Store == nil {
//		return false
//	}
//	return s.Store.CheckWesternMedicineDoctorOrdersExistInDaysStore(days, MedicalID)
//}

//func (w *WesternMedicineDoctorOrdersStore) CalculateDaysInRangeStore(medicalIDs []string, maxDays int) bool {
//	// 获取当前时间的时间戳，计算时间范围
//	now := time.Now()
//	minTimestamp := now.AddDate(0, 0, -maxDays).Unix()
//
//	for _, id := range medicalIDs {
//		order, exists := w.medicineDoctorOrdersMap[id]
//		if !exists {
//			continue // 如果 medical_id 不存在于 store 中，跳过
//		}
//
//		// 解析 order_begin 为时间戳
//		orderBeginTimestamp, err := strconv.ParseInt(order.OrderBegin, 10, 64)
//		if err != nil || orderBeginTimestamp < minTimestamp {
//			continue // 跳过错误或在时间范围之外的记录
//		}
//
//		// 将 DailyUsage 转换为数值类型
//		dailyUsage, err := strconv.ParseFloat(order.DailyUsage, 64)
//		if err != nil || dailyUsage == 0 {
//			continue // 跳过 DailyUsage 转换失败或为零的记录
//		}
//
//		// 计算药品剂量除以单日用量的天数
//		daysOfUse := order.DrugDosage / dailyUsage
//
//		// 如果结果大于 maxDays，返回 false
//		if daysOfUse > float64(maxDays) {
//			return false
//		}
//	}
//	// 所有记录都在最大天数范围内
//	return true
//}
//
//// TODO:单次开药是否超出限制天数
//func (s *WesternMedicineDoctorOrders) CalculateDaysInRange(MedicalID []string, days int) bool {
//	if s.Store == nil {
//		return false
//	}
//	return s.Store.CalculateDaysInRangeStore(MedicalID, days)
//}

//func (w *WesternMedicineDoctorOrdersStore) SetTheTimeDrugTypeStatisticsStore(days int, doseUnit string, maxDose float64, medicineNames []string) bool {
//	now := time.Now()
//	// 计算向前推的时间范围，得到天数范围内的起始时间戳
//	startTime := now.AddDate(0, 0, -days).Unix()
//	totalDose := 0.0 // 累计剂量
//
//	// Create a map for quick lookup of medicine names
//	medicineNameSet := make(map[string]struct{})
//	for _, name := range medicineNames {
//		medicineNameSet[name] = struct{}{}
//	}
//
//	for _, order := range w.medicineDoctorOrdersList {
//		if order == nil {
//			continue
//		}
//
//		// Check if the order's MedicalName exists in the medicineNameSet
//		if _, exists := medicineNameSet[order.MedicalName]; !exists {
//			continue
//		}
//
//		if order.DrugDosageUnit != doseUnit {
//			continue
//		}
//
//		orderBeginTimestamp, err := strconv.ParseInt(order.OrderBegin, 10, 64)
//		if err != nil {
//			continue
//		}
//		if orderBeginTimestamp < startTime {
//			continue
//		}
//
//		totalDose += order.DrugDosage
//	}
//
//	return totalDose <= maxDose
//}
//
//// TODO:规则周期内药品种类统计
//func (s *WesternMedicineDoctorOrders) SetTheTimeDrugTypeStatistics(MedicalID []string, days int) bool {
//	if s.Store == nil {
//		return false
//	}
//	return s.Store.SetTheTimeDrugTypeStatisticsStore(MedicalID, days)
//}

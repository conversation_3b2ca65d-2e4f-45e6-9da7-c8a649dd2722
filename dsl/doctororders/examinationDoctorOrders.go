package doctororders

import (
	"strconv"
	"time"
)

type ExaminationOrder struct {
	ExaminationOrderItem string                 `json:"examination_order_item"` //检查项目ID
	UrgentMark           string                 `json:"urgent_mark"`            //加急标识
	OrderDuration        string                 `json:"order_duration"`         //医嘱时效性
	ReviewTime           string                 `json:"review_time"`            //审核时间
	OrderCreateTime      string                 `json:"order_create_time"`      //医嘱开立时间
	Store                *ExaminationOrderStore `json:"-"`
}

type ExaminationOrderStore struct {
	examinationMap  map[string]*ExaminationOrder // 使用 map 存储药品，键为 medical_id
	examinationList []*ExaminationOrder
}

func NewExaminationOrderStore() *ExaminationOrderStore {
	return &ExaminationOrderStore{
		examinationMap:  make(map[string]*ExaminationOrder),
		examinationList: make([]*ExaminationOrder, 0),
	}
}

func (e *ExaminationOrder) SetStore(store *ExaminationOrderStore) {
	e.Store = store
}

func (e *ExaminationOrderStore) AddExaminationMap(image *ExaminationOrder) {
	e.examinationMap[image.ExaminationOrderItem] = image
}

func (e *ExaminationOrderStore) AddExaminationList(imageList []*ExaminationOrder) {
	e.examinationList = imageList
}

func (e *ExaminationOrderStore) IsExistByExaminationOrderItemStore(item string) bool {
	_, exists := e.examinationMap[item]
	if !exists {
		return false
	}
	return true
}

// TODO:判断检查项目是否存在
func (e *ExaminationOrder) IsExistByExaminationOrderItem(item string) bool {
	if e.Store == nil {
		return false
	}
	return e.Store.IsExistByExaminationOrderItemStore(item)
}

func (i *ExaminationOrderStore) CheckDuplicateImageOrdersInDaysStore(days int, imageItem, inspectionSite string) bool {
	now := time.Now()
	startTime := now.AddDate(0, 0, -days).Unix()
	for _, order := range i.examinationList {
		if order == nil {
			continue
		}
		if order.ExaminationOrderItem != imageItem {
			continue
		}
		orderBeginTimestamp, err := strconv.ParseInt(order.OrderCreateTime, 10, 64)
		if err != nil {
			continue
		}
		// 判断该记录的开立时间是否在指定时间范围内
		if orderBeginTimestamp >= startTime {
			return true // 如果找到了符合条件的检查项目，立即返回 true
		}
	}
	return false
}

// TODO:判断时间范围内是否存在重复申请项目（检查项目且检查部位相同）
func (i *ExaminationOrder) CheckDuplicateImageOrdersInDays(days int, imageItem, inspectionSite string) bool {
	if i.Store == nil {
		return false
	}
	return i.Store.CheckDuplicateImageOrdersInDaysStore(days, imageItem, inspectionSite)
}

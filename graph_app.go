package main

import (
	"github.com/gin-gonic/gin"
	"kmbservice/controller"
	"kmbservice/pkg/logging"
	"kmbservice/pkg/setting"
)

func main() {
	setting.Setup("conf/app.ini")
	logging.Setup()
	router := gin.Default()
	router.POST("/diagnosis", controller.HandleDiagnosis)
	router.POST("/GetTestTable", controller.GetTestTable)
	router.POST("/GetTablesByDis", controller.GetTablesByDis)
	router.POST("/HandleTcmSymn", controller.HandleTcmSymn)
	router.POST("/HandleDiagnosis_acc", controller.HandleDiagnosis_acc)
	router.POST("/Handletcms_tcmdis", controller.Handletcms_tcmdis)
	router.POST("/HandleRecommendation", controller.HandleRecommendation)
	router.Run(":9892")
}

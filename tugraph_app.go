package main

import (
	"fmt"
	"github.com/fvbock/endless"
	"kmbservice/pkg/logging"
	"kmbservice/pkg/setting"
	"kmbservice/routers"
	"log"
	"syscall"
)

func main() {
	setting.Setup("conf/app.ini") //初始化配置信息
	logging.Setup()               //初始化日志信息
	port := fmt.Sprintf(":%d", setting.ServerSetting.HttpPort)
	endless.DefaultReadTimeOut = setting.ReadTimeout
	endless.DefaultWriteTimeOut = setting.WriteTimeout
	endless.DefaultMaxHeaderBytes = 1 << 20
	server := endless.NewServer(port, routers.InitRuleRouter())
	server.BeforeBegin = func(add string) {
		log.Printf("实际进程PID 是：%d,%d", syscall.Getpid(), port)
	}
	err := server.ListenAndServe()
	if err != nil {
		log.Printf("服务器错误：%v", err)
	}
}

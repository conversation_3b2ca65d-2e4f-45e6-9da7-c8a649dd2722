[INFO][job.go:13]2024/07/25 10:34:51 [添加定时任务2024-07-25 10:34:51]
[INFO][job.go:13]2024/07/25 14:34:06 [添加定时任务2024-07-25 14:34:06]
[ERROR][InRequestApi.go:66]2024/07/25 14:34:12 [unexpected end of JSON input]
[INFO][ruleinterface.go:143]2024/07/25 14:34:12 [rules num:1, load rules cost time:46594300 ns]
[ERROR][InRequestApi.go:66]2024/07/25 14:34:51 [unexpected end of JSON input]
[INFO][ruleinterface.go:143]2024/07/25 14:35:17 [rules num:1, load rules cost time:25565228600 ns]
[INFO][job.go:13]2024/07/25 14:36:25 [添加定时任务2024-07-25 14:36:25]
[ERROR][InRequestApi.go:66]2024/07/25 14:36:28 [unexpected end of JSON input]
[INFO][ruleinterface.go:142]2024/07/25 14:36:28 [rules num:1, load rules cost time:46582200 ns]
[INFO][ruleinterface.go:151]2024/07/25 14:36:28 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/25 14:36:44 [unexpected end of JSON input]
[INFO][ruleinterface.go:142]2024/07/25 14:36:44 [rules num:1, load rules cost time:29648400 ns]
[INFO][ruleinterface.go:151]2024/07/25 14:36:44 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/25 14:38:12 [unexpected end of JSON input]
[INFO][ruleinterface.go:142]2024/07/25 14:38:12 [rules num:1, load rules cost time:44790900 ns]
[INFO][ruleinterface.go:151]2024/07/25 14:38:12 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/25 14:38:34 [unexpected end of JSON input]
[INFO][ruleinterface.go:142]2024/07/25 14:38:34 [rules num:1, load rules cost time:31782600 ns]
[INFO][ruleinterface.go:151]2024/07/25 14:38:34 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/25 14:44:13 [unexpected end of JSON input]
[INFO][ruleinterface.go:142]2024/07/25 14:44:13 [rules num:1, load rules cost time:33957100 ns]
[INFO][ruleinterface.go:151]2024/07/25 14:44:13 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/25 14:45:10 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/07/25 14:46:00 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/07/25 14:46:32 [unexpected end of JSON input]
[INFO][ruleinterface.go:142]2024/07/25 14:46:32 [rules num:1, load rules cost time:34592400 ns]
[INFO][ruleinterface.go:151]2024/07/25 14:46:32 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/25 14:46:54 [unexpected end of JSON input]
[INFO][ruleinterface.go:142]2024/07/25 14:46:54 [rules num:1, load rules cost time:28931800 ns]
[INFO][ruleinterface.go:151]2024/07/25 14:46:54 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/25 14:48:10 [unexpected end of JSON input]
[INFO][ruleinterface.go:142]2024/07/25 14:48:47 [rules num:1, load rules cost time:37068230700 ns]
[INFO][ruleinterface.go:151]2024/07/25 14:48:47 [execute rule cost 536800 ns]
[INFO][job.go:13]2024/07/25 14:51:35 [添加定时任务2024-07-25 14:51:35]
[ERROR][InRequestApi.go:66]2024/07/25 14:51:37 [unexpected end of JSON input]
[INFO][ruleinterface.go:142]2024/07/25 14:51:59 [rules num:1, load rules cost time:22433202300 ns]
[INFO][ruleinterface.go:151]2024/07/25 14:51:59 [execute rule cost 513200 ns]
[INFO][job.go:13]2024/07/25 14:53:13 [添加定时任务2024-07-25 14:53:13]
[ERROR][InRequestApi.go:66]2024/07/25 14:53:16 [unexpected end of JSON input]
[INFO][ruleinterface.go:144]2024/07/25 14:53:31 [rules num:1, load rules cost time:15080641400 ns]
[INFO][ruleinterface.go:153]2024/07/25 14:53:31 [execute rule cost 0 ns]
[INFO][job.go:13]2024/07/25 14:53:35 [添加定时任务2024-07-25 14:53:35]
[ERROR][InRequestApi.go:66]2024/07/25 14:53:38 [unexpected end of JSON input]
[INFO][ruleinterface.go:144]2024/07/25 14:53:44 [rules num:1, load rules cost time:6625084200 ns]
[INFO][ruleinterface.go:153]2024/07/25 14:53:44 [execute rule cost 142700 ns]
[INFO][job.go:13]2024/07/25 14:54:07 [添加定时任务2024-07-25 14:54:07]
[ERROR][InRequestApi.go:66]2024/07/25 14:54:16 [unexpected end of JSON input]
[INFO][ruleinterface.go:144]2024/07/25 14:54:34 [rules num:1, load rules cost time:18476838900 ns]
[INFO][ruleinterface.go:153]2024/07/25 14:54:34 [execute rule cost 0 ns]
[INFO][job.go:13]2024/07/25 14:54:47 [添加定时任务2024-07-25 14:54:47]
[ERROR][InRequestApi.go:66]2024/07/25 14:54:53 [unexpected end of JSON input]
[INFO][job.go:13]2024/07/25 15:00:29 [添加定时任务2024-07-25 15:00:29]
[ERROR][InRequestApi.go:66]2024/07/25 15:00:46 [unexpected end of JSON input]
[INFO][job.go:13]2024/07/25 15:02:01 [添加定时任务2024-07-25 15:02:01]
[INFO][job.go:13]2024/07/25 15:03:13 [添加定时任务2024-07-25 15:03:13]
[ERROR][InRequestApi.go:66]2024/07/25 15:03:16 [unexpected end of JSON input]
[INFO][ruleinterface.go:142]2024/07/25 15:03:16 [rules num:1, load rules cost time:43046300 ns]
[INFO][ruleinterface.go:151]2024/07/25 15:03:16 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/25 15:03:23 [unexpected end of JSON input]
[INFO][ruleinterface.go:142]2024/07/25 15:03:23 [rules num:1, load rules cost time:30066700 ns]
[INFO][ruleinterface.go:151]2024/07/25 15:03:23 [execute rule cost 507100 ns]
[INFO][job.go:13]2024/07/25 15:36:01 [添加定时任务2024-07-25 15:36:01]
[ERROR][InRequestApi.go:66]2024/07/25 15:36:10 [unexpected end of JSON input]
[INFO][ruleinterface.go:188]2024/07/25 15:36:10 [rules num:1, load rules cost time:43789600 ns]
[INFO][ruleinterface.go:197]2024/07/25 15:36:10 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/25 15:36:20 [unexpected end of JSON input]
[INFO][ruleinterface.go:188]2024/07/25 15:36:20 [rules num:1, load rules cost time:30969100 ns]
[INFO][ruleinterface.go:197]2024/07/25 15:36:20 [execute rule cost 0 ns]
[INFO][job.go:13]2024/07/25 15:40:20 [添加定时任务2024-07-25 15:40:20]
[ERROR][InRequestApi.go:66]2024/07/25 15:40:25 [unexpected end of JSON input]
[INFO][ruleinterface.go:190]2024/07/25 15:40:44 [rules num:1, load rules cost time:19254007100 ns]
[INFO][ruleinterface.go:199]2024/07/25 15:40:44 [execute rule cost 0 ns]
[INFO][job.go:13]2024/07/25 15:41:14 [添加定时任务2024-07-25 15:41:14]
[ERROR][InRequestApi.go:66]2024/07/25 15:41:17 [unexpected end of JSON input]
[INFO][ruleinterface.go:190]2024/07/25 15:41:26 [rules num:1, load rules cost time:9375453600 ns]
[INFO][ruleinterface.go:199]2024/07/25 15:41:26 [execute rule cost 0 ns]
[INFO][job.go:13]2024/07/25 15:42:12 [添加定时任务2024-07-25 15:42:12]
[ERROR][InRequestApi.go:66]2024/07/25 15:42:15 [unexpected end of JSON input]
[INFO][ruleinterface.go:190]2024/07/25 15:42:15 [rules num:1, load rules cost time:39759400 ns]
[INFO][ruleinterface.go:199]2024/07/25 15:42:15 [execute rule cost 0 ns]
[INFO][job.go:13]2024/07/25 16:10:38 [添加定时任务2024-07-25 16:10:38]
[INFO][job.go:13]2024/07/25 16:21:22 [添加定时任务2024-07-25 16:21:22]
[ERROR][InRequestApi.go:66]2024/07/25 16:21:31 [unexpected end of JSON input]
[INFO][ruleinterface.go:108]2024/07/25 16:21:31 [rules num:1, load rules cost time:48441200 ns]
[ERROR][ruleengine.go:65]2024/07/25 16:21:31 [Recovered from panic: [rule: "检验报告指定检验项目与报告是否存在01" executed, error:
 line 3, column 10, code: testReports.IsExistByTestItemID("12345678"), NOT FOUND Function: IsExistByTestItemID ]]
[ERROR][InRequestApi.go:66]2024/07/25 16:22:40 [unexpected end of JSON input]
[INFO][ruleinterface.go:108]2024/07/25 16:24:05 [rules num:1, load rules cost time:27784100 ns]
[ERROR][ruleengine.go:65]2024/07/25 16:24:09 [Recovered from panic: [rule: "检验报告指定检验项目与报告是否存在01" executed, error:
 line 3, column 10, code: testReports.IsExistByTestItemID("12345678"), NOT FOUND Function: IsExistByTestItemID ]]
[INFO][job.go:13]2024/07/25 16:24:36 [添加定时任务2024-07-25 16:24:36]
[ERROR][InRequestApi.go:66]2024/07/25 16:24:39 [unexpected end of JSON input]
[INFO][ruleinterface.go:108]2024/07/25 16:25:50 [rules num:1, load rules cost time:48315632800 ns]
[ERROR][ruleengine.go:65]2024/07/25 16:26:35 [Recovered from panic: [rule: "检验报告指定检验项目与报告是否存在01" executed, error:
 line 3, column 10, code: testReports.IsExistByTestItemID("12345678"), NOT FOUND Function: IsExistByTestItemID ]]
[INFO][job.go:13]2024/07/25 16:27:39 [添加定时任务2024-07-25 16:27:39]
[INFO][job.go:13]2024/07/25 16:30:08 [添加定时任务2024-07-25 16:30:08]
[ERROR][InRequestApi.go:66]2024/07/25 16:30:46 [unexpected end of JSON input]
[INFO][ruleinterface.go:109]2024/07/25 16:30:50 [rules num:1, load rules cost time:45660600 ns]
[ERROR][ruleengine.go:65]2024/07/25 16:30:50 [Recovered from panic: [rule: "检验报告指定检验项目与报告是否存在01" executed, error:
 line 3, column 10, code: testReports.IsExistByTestItemID("12345678"), NOT FOUND Function: IsExistByTestItemID ]]
[INFO][job.go:13]2024/07/25 16:30:55 [添加定时任务2024-07-25 16:30:55]
[ERROR][InRequestApi.go:66]2024/07/25 16:30:57 [unexpected end of JSON input]
[INFO][ruleinterface.go:109]2024/07/25 16:30:57 [rules num:1, load rules cost time:46716000 ns]
[ERROR][ruleengine.go:65]2024/07/25 16:30:57 [Recovered from panic: [rule: "检验报告指定检验项目与报告是否存在01" executed, error:
 line 3, column 10, code: testReports.IsExistByTestItemID("12345678"), NOT FOUND Function: IsExistByTestItemID ]]
[ERROR][InRequestApi.go:66]2024/07/25 16:31:22 [unexpected end of JSON input]
[INFO][ruleinterface.go:109]2024/07/25 16:31:22 [rules num:1, load rules cost time:30997700 ns]
[ERROR][ruleengine.go:65]2024/07/25 16:31:48 [Recovered from panic: [rule: "检验报告指定检验项目与报告是否存在01" executed, error:
 line 3, column 10, code: testReports.IsExistByTestItemID("12345678"), NOT FOUND Function: IsExistByTestItemID ]]
[INFO][job.go:13]2024/07/25 16:36:33 [添加定时任务2024-07-25 16:36:33]
[INFO][job.go:13]2024/07/25 16:38:03 [添加定时任务2024-07-25 16:38:03]
[ERROR][InRequestApi.go:66]2024/07/25 16:38:05 [unexpected end of JSON input]
[INFO][ruleinterface.go:110]2024/07/25 16:38:05 [rules num:1, load rules cost time:41602600 ns]
[ERROR][ruleengine.go:65]2024/07/25 16:38:05 [Recovered from panic: [rule: "检验报告指定检验项目与报告是否存在01" executed, error:
 line 3, column 10, code: testReports.IsExistByTestItemID("12345678"), it is not number type, type is string ! 
goroutine 52 [running]:
github.com/bilibili/gengine/internal/base.(*MethodCall).Evaluate.func1()
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/method_call.go:32 +0x9f
error({0xeaf440?, 0xc0004557f0?})
	C:/Program Files/Go/src/runtime/error.go:920 +0x290
github.com/bilibili/gengine/internal/core.getNumType({0xeaf440, 0xc0004556f0, 0x98})
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/core/execute.go:381 +0x1b1
github.com/bilibili/gengine/internal/core.ParamsTypeChange({0xf3c3e0, 0xc0002440d0, 0xe13}, {0xc0004e9830, 0x1, 0x1})
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/core/execute.go:275 +0x1075
github.com/bilibili/gengine/internal/core.InvokeFunction({0xf3c3e0, 0xc0002440d0, 0x16}, {0xc0003f403c, 0x13}, {0xc0004e9830, 0x1, 0x1})
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/core/execute.go:18 +0x11e
github.com/bilibili/gengine/context.(*DataContext).ExecMethod(0xc0000088b8, 0xc0005e4450, {0xc0003f4030, 0x1f}, {0xc0004e9830, 0x1, 0x1})
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/context/data_context.go:155 +0x45d
github.com/bilibili/gengine/internal/base.(*MethodCall).Evaluate(0xc00035a4c0, 0xc0000088b8, 0xc0005e4450)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/method_call.go:54 +0x3d4
github.com/bilibili/gengine/internal/base.(*ExpressionAtom).Evaluate(0xc00053f4a0, 0xc0000088b8, 0xc0005e4450)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/expression_atom.go:75 +0x46d
github.com/bilibili/gengine/internal/base.(*MathExpression).Evaluate(0xc00053f3e0, 0xc0000088b8, 0xc0005e4450)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/math_expression.go:44 +0xb2
github.com/bilibili/gengine/internal/base.(*Expression).Evaluate(0xc000224c80, 0xc0000088b8, 0xc0005e4450)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/expression.go:70 +0xc7
github.com/bilibili/gengine/internal/base.(*Expression).Evaluate(0xc000224c00, 0xc0000088b8, 0xc0005e4450)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/expression.go:100 +0x6a5
github.com/bilibili/gengine/internal/base.(*Expression).Evaluate(0xc000224b80, 0xc0000088b8, 0xc0005e4450)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/expression.go:89 +0x4c5
github.com/bilibili/gengine/internal/base.(*Expression).Evaluate(0xc000224b00, 0xc0000088b8, 0xc0005e4450)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/expression.go:89 +0x4c5
github.com/bilibili/gengine/internal/base.(*Expression).Evaluate(0xc000224a80, 0xc0000088b8, 0xc0005e4450)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/expression.go:89 +0x4c5
github.com/bilibili/gengine/internal/base.(*IfStmt).Evaluate(0xc0005e43f0, 0xc0000088b8, 0xc0005e4450)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/if_stmt.go:18 +0x9a
github.com/bilibili/gengine/internal/base.(*Statement).Evaluate(0xc000609130, 0xc0000088b8, 0xc0005e4450)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/statement.go:26 +0xaf
github.com/bilibili/gengine/internal/base.(*Statements).Evaluate(0xc0005caf00, 0xc0000088b8, 0xc0005e4450)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/statements.go:15 +0xf3
github.com/bilibili/gengine/internal/base.(*RuleContent).Execute(0xc0004569b0, 0xc0000088b8, 0xc0005e4450)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/rule_content.go:14 +0x88
github.com/bilibili/gengine/internal/base.(*RuleEntity).Execute(0xc0005e43c0, 0xc0000088b8)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/rule_entity.go:37 +0x8d
github.com/bilibili/gengine/engine.(*Gengine).Execute(0xc0004557a0, 0xc000008930, 0x1)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/engine/gengine.go:57 +0x1e5
kmbservice/internals/rulengines/services/ruleservice.ExectestReportsWithMedicine(0x1935181cad73f40b, {0xc0002440c0, 0x2, 0x2}, {0x0, 0x0, 0x0}, 0xc000000000)
	D:/TaiYi/Gitee/kmbservice/internals/rulengines/services/ruleservice/ruleinterface.go:113 +0x865
kmbservice/controller.RuleExec(0xc0004d2000)
	D:/TaiYi/Gitee/kmbservice/controller/ruleengine.go:200 +0xc3b
github.com/gin-gonic/gin.(*Context).Next(0xc0004d2000)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/context.go:174 +0x7a
github.com/gin-gonic/gin.LoggerWithConfig.func1(0xc0004d2000)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/logger.go:240 +0xe6
github.com/gin-gonic/gin.(*Context).Next(0xc0004d2000)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/context.go:174 +0x7a
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1(0xc0004d2000)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/recovery.go:102 +0xff
github.com/gin-gonic/gin.(*Context).Next(0xc0004d2000)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/context.go:174 +0x7a
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1(0xc0004d2000)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/recovery.go:102 +0xff
github.com/gin-gonic/gin.(*Context).Next(0xc0004d2000)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/context.go:174 +0x7a
github.com/gin-gonic/gin.LoggerWithConfig.func1(0xc0004d2000)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/logger.go:240 +0xe6
github.com/gin-gonic/gin.(*Context).Next(0xc0004d2000)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/context.go:174 +0x7a
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest(0xc000482b60, 0xc0004d2000)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/gin.go:620 +0x427
github.com/gin-gonic/gin.(*Engine).ServeHTTP(0xc000482b60, {0x1100260, 0xc0004ce000}, 0xc000574500)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/gin.go:576 +0xbc
net/http.serverHandler.ServeHTTP({0xc00052af00}, {0x1100260, 0xc0004ce000}, 0xc000574500)
	C:/Program Files/Go/src/net/http/server.go:2938 +0x257
net/http.(*conn).serve(0xc000130240, {0x11017b0, 0xc0002426e0})
	C:/Program Files/Go/src/net/http/server.go:2009 +0x1a59
created by net/http.(*Server).Serve in goroutine 1
	C:/Program Files/Go/src/net/http/server.go:3086 +0xa65
 ]]
[ERROR][InRequestApi.go:66]2024/07/25 16:38:35 [unexpected end of JSON input]
[INFO][ruleinterface.go:110]2024/07/25 16:38:35 [rules num:1, load rules cost time:29038700 ns]
[ERROR][ruleengine.go:65]2024/07/25 16:39:16 [Recovered from panic: [rule: "检验报告指定检验项目与报告是否存在01" executed, error:
 line 3, column 10, code: testReports.IsExistByTestItemID("12345678"), it is not number type, type is string ! 
goroutine 52 [running]:
github.com/bilibili/gengine/internal/base.(*MethodCall).Evaluate.func1()
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/method_call.go:32 +0x9f
error({0xeaf440?, 0xc0007307a0?})
	C:/Program Files/Go/src/runtime/error.go:920 +0x290
github.com/bilibili/gengine/internal/core.getNumType({0xeaf440, 0xc0007306c0, 0x98})
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/core/execute.go:381 +0x1b1
github.com/bilibili/gengine/internal/core.ParamsTypeChange({0xf3c3e0, 0xc0003680d0, 0xe13}, {0xc0004f31e8, 0x1, 0x1})
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/core/execute.go:275 +0x1075
github.com/bilibili/gengine/internal/core.InvokeFunction({0xf3c3e0, 0xc0003680d0, 0x16}, {0xc0002de00c, 0x13}, {0xc0004f31e8, 0x1, 0x1})
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/core/execute.go:18 +0x11e
github.com/bilibili/gengine/context.(*DataContext).ExecMethod(0xc0004f20a8, 0xc0006b4e40, {0xc0002de000, 0x1f}, {0xc0004f31e8, 0x1, 0x1})
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/context/data_context.go:155 +0x45d
github.com/bilibili/gengine/internal/base.(*MethodCall).Evaluate(0xc000145140, 0xc0004f20a8, 0xc0006b4e40)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/method_call.go:54 +0x3d4
github.com/bilibili/gengine/internal/base.(*ExpressionAtom).Evaluate(0xc0001a3620, 0xc0004f20a8, 0xc0006b4e40)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/expression_atom.go:75 +0x46d
github.com/bilibili/gengine/internal/base.(*MathExpression).Evaluate(0xc0001a35c0, 0xc0004f20a8, 0xc0006b4e40)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/math_expression.go:44 +0xb2
github.com/bilibili/gengine/internal/base.(*Expression).Evaluate(0xc0006ced80, 0xc0004f20a8, 0xc0006b4e40)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/expression.go:70 +0xc7
github.com/bilibili/gengine/internal/base.(*Expression).Evaluate(0xc0006cea80, 0xc0004f20a8, 0xc0006b4e40)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/expression.go:100 +0x6a5
github.com/bilibili/gengine/internal/base.(*Expression).Evaluate(0xc0006cea00, 0xc0004f20a8, 0xc0006b4e40)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/expression.go:89 +0x4c5
github.com/bilibili/gengine/internal/base.(*Expression).Evaluate(0xc0006ce980, 0xc0004f20a8, 0xc0006b4e40)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/expression.go:89 +0x4c5
github.com/bilibili/gengine/internal/base.(*Expression).Evaluate(0xc0006ce880, 0xc0004f20a8, 0xc0006b4e40)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/expression.go:89 +0x4c5
github.com/bilibili/gengine/internal/base.(*IfStmt).Evaluate(0xc0006b4de0, 0xc0004f20a8, 0xc0006b4e40)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/if_stmt.go:18 +0x9a
github.com/bilibili/gengine/internal/base.(*Statement).Evaluate(0xc00034c6e0, 0xc0004f20a8, 0xc0006b4e40)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/statement.go:26 +0xaf
github.com/bilibili/gengine/internal/base.(*Statements).Evaluate(0xc000800520, 0xc0004f20a8, 0xc0006b4e40)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/statements.go:15 +0xf3
github.com/bilibili/gengine/internal/base.(*RuleContent).Execute(0xc00051ca38, 0xc0004f20a8, 0xc0006b4e40)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/rule_content.go:14 +0x88
github.com/bilibili/gengine/internal/base.(*RuleEntity).Execute(0xc0006b4db0, 0xc0004f20a8)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/internal/base/rule_entity.go:37 +0x8d
github.com/bilibili/gengine/engine.(*Gengine).Execute(0xc000730770, 0xc0004f20f0, 0x1)
	C:/Users/<USER>/go/pkg/mod/github.com/bilibili/gengine@v1.5.7/engine/gengine.go:57 +0x1e5
kmbservice/internals/rulengines/services/ruleservice.ExectestReportsWithMedicine(0x1935181cad73f40b, {0xc0003680c0, 0x2, 0x2}, {0x0, 0x0, 0x0}, 0xc00017e0c0)
	D:/TaiYi/Gitee/kmbservice/internals/rulengines/services/ruleservice/ruleinterface.go:113 +0x865
kmbservice/controller.RuleExec(0xc0004d2300)
	D:/TaiYi/Gitee/kmbservice/controller/ruleengine.go:200 +0xc3b
github.com/gin-gonic/gin.(*Context).Next(0xc0004d2300)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/context.go:174 +0x7a
github.com/gin-gonic/gin.LoggerWithConfig.func1(0xc0004d2300)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/logger.go:240 +0xe6
github.com/gin-gonic/gin.(*Context).Next(0xc0004d2300)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/context.go:174 +0x7a
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1(0xc0004d2300)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/recovery.go:102 +0xff
github.com/gin-gonic/gin.(*Context).Next(0xc0004d2300)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/context.go:174 +0x7a
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1(0xc0004d2300)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/recovery.go:102 +0xff
github.com/gin-gonic/gin.(*Context).Next(0xc0004d2300)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/context.go:174 +0x7a
github.com/gin-gonic/gin.LoggerWithConfig.func1(0xc0004d2300)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/logger.go:240 +0xe6
github.com/gin-gonic/gin.(*Context).Next(0xc0004d2300)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/context.go:174 +0x7a
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest(0xc000482b60, 0xc0004d2300)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/gin.go:620 +0x427
github.com/gin-gonic/gin.(*Engine).ServeHTTP(0xc000482b60, {0x1100260, 0xc0004ce0e0}, 0xc0004d2200)
	C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.1/gin.go:576 +0xbc
net/http.serverHandler.ServeHTTP({0xc00052af00}, {0x1100260, 0xc0004ce0e0}, 0xc0004d2200)
	C:/Program Files/Go/src/net/http/server.go:2938 +0x257
net/http.(*conn).serve(0xc000130240, {0x11017b0, 0xc0002426e0})
	C:/Program Files/Go/src/net/http/server.go:2009 +0x1a59
created by net/http.(*Server).Serve in goroutine 1
	C:/Program Files/Go/src/net/http/server.go:3086 +0xa65
 ]]
[INFO][job.go:13]2024/07/25 16:41:10 [添加定时任务2024-07-25 16:41:10]
[ERROR][InRequestApi.go:66]2024/07/25 16:41:19 [unexpected end of JSON input]
[INFO][ruleinterface.go:110]2024/07/25 16:41:19 [rules num:1, load rules cost time:42155200 ns]
[INFO][ruleinterface.go:120]2024/07/25 16:41:19 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/25 16:41:28 [unexpected end of JSON input]
[INFO][ruleinterface.go:110]2024/07/25 16:41:28 [rules num:1, load rules cost time:45021100 ns]
[INFO][ruleinterface.go:120]2024/07/25 16:41:28 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/25 16:41:35 [unexpected end of JSON input]
[INFO][ruleinterface.go:110]2024/07/25 16:41:35 [rules num:1, load rules cost time:33469000 ns]
[INFO][ruleinterface.go:120]2024/07/25 16:41:35 [execute rule cost 0 ns]

[INFO][job.go:13]2024/07/24 09:48:36 [添加定时任务2024-07-24 09:48:36]
[ERROR][InRequestApi.go:66]2024/07/24 09:51:43 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/07/24 09:52:12 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/07/24 09:53:17 [unexpected end of JSON input]
[INFO][job.go:13]2024/07/24 09:56:55 [添加定时任务2024-07-24 09:56:55]
[ERROR][InRequestApi.go:66]2024/07/24 09:56:58 [unexpected end of JSON input]
[INFO][job.go:13]2024/07/24 09:57:08 [添加定时任务2024-07-24 09:57:08]
[ERROR][InRequestApi.go:66]2024/07/24 09:57:11 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/07/24 09:57:29 [unexpected end of JSON input]
[INFO][job.go:13]2024/07/24 09:58:50 [添加定时任务2024-07-24 09:58:50]
[ERROR][InRequestApi.go:66]2024/07/24 09:58:53 [unexpected end of JSON input]
[INFO][job.go:13]2024/07/24 09:59:07 [添加定时任务2024-07-24 09:59:07]
[ERROR][InRequestApi.go:66]2024/07/24 09:59:09 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/07/24 09:59:49 [unexpected end of JSON input]
[INFO][job.go:13]2024/07/24 10:05:44 [添加定时任务2024-07-24 10:05:44]
[ERROR][InRequestApi.go:66]2024/07/24 10:05:47 [unexpected end of JSON input]
[INFO][job.go:13]2024/07/24 10:07:05 [添加定时任务2024-07-24 10:07:05]
[ERROR][InRequestApi.go:66]2024/07/24 10:07:14 [unexpected end of JSON input]
[INFO][job.go:13]2024/07/24 10:27:07 [添加定时任务2024-07-24 10:27:07]
[ERROR][InRequestApi.go:66]2024/07/24 10:27:10 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/07/24 10:28:03 [unexpected end of JSON input]
[INFO][job.go:13]2024/07/24 10:37:28 [添加定时任务2024-07-24 10:37:28]
[ERROR][InRequestApi.go:66]2024/07/24 10:37:31 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/07/24 10:37:54 [unexpected end of JSON input]
[INFO][job.go:13]2024/07/24 10:58:35 [添加定时任务2024-07-24 10:58:35]
[ERROR][InRequestApi.go:66]2024/07/24 10:58:38 [unexpected end of JSON input]
[INFO][job.go:13]2024/07/24 11:01:16 [添加定时任务2024-07-24 11:01:16]
[INFO][job.go:13]2024/07/24 11:04:34 [添加定时任务2024-07-24 11:04:34]
[INFO][ruleinterface.go:147]2024/07/24 11:06:32 [rules num:2, load rules cost time:114965874300 ns]
[INFO][job.go:13]2024/07/24 11:17:21 [添加定时任务2024-07-24 11:17:21]
[INFO][ruleinterface.go:147]2024/07/24 11:17:24 [rules num:1, load rules cost time:40013000 ns]
[INFO][ruleinterface.go:157]2024/07/24 11:17:24 [execute rule cost 0 ns]
[INFO][job.go:13]2024/07/24 13:11:19 [添加定时任务2024-07-24 13:11:19]
[INFO][ruleinterface.go:165]2024/07/24 13:11:26 [rules num:1, load rules cost time:48229500 ns]
[INFO][ruleinterface.go:175]2024/07/24 13:11:26 [execute rule cost 0 ns]
[INFO][job.go:13]2024/07/24 13:21:59 [添加定时任务2024-07-24 13:21:59]
[ERROR][ruleinterface.go:130]2024/07/24 13:22:54 [Recovered from panic: [line 10:5 token recognition error at: '前' line 10:6 token recognition error at: '列' line 10:7 token recognition error at: '腺' line 10:8 token recognition error at: '炎' line 10:9 token recognition error at: '诊' line 10:10 token recognition error at: '断' line 10:11 token recognition error at: '性' line 10:12 token recognition error at: '别' line 10:13 token recognition error at: '合' line 10:14 token recognition error at: '理' line 10:15 token recognition error at: '性' line 12:17 token recognition error at: '生' line 12:18 token recognition error at: '理' line 12:19 token recognition error at: '性' line 12:20 token recognition error at: '别' line 12:65 token recognition error at: '病' line 12:66 token recognition error at: '种' line 12:67 token recognition error at: '名' line 12:68 token recognition error at: '称' line 12:70 token recognition error at: '前' line 12:71 token recognition error at: '列' line 12:72 token recognition error at: '腺' line 12:73 token recognition error at: '炎']]
[INFO][job.go:13]2024/07/24 13:23:07 [添加定时任务2024-07-24 13:23:07]
[ERROR][ruleinterface.go:130]2024/07/24 13:23:15 [Recovered from panic: [line 10:5 token recognition error at: '前' line 10:6 token recognition error at: '列' line 10:7 token recognition error at: '腺' line 10:8 token recognition error at: '炎' line 10:9 token recognition error at: '诊' line 10:10 token recognition error at: '断' line 10:11 token recognition error at: '性' line 10:12 token recognition error at: '别' line 10:13 token recognition error at: '合' line 10:14 token recognition error at: '理' line 10:15 token recognition error at: '性' line 12:17 token recognition error at: '生' line 12:18 token recognition error at: '理' line 12:19 token recognition error at: '性' line 12:20 token recognition error at: '别' line 12:65 token recognition error at: '病' line 12:66 token recognition error at: '种' line 12:67 token recognition error at: '名' line 12:68 token recognition error at: '称' line 12:70 token recognition error at: '前' line 12:71 token recognition error at: '列' line 12:72 token recognition error at: '腺' line 12:73 token recognition error at: '炎']]
[INFO][job.go:13]2024/07/24 13:24:40 [添加定时任务2024-07-24 13:24:40]
[INFO][job.go:13]2024/07/24 14:56:09 [添加定时任务2024-07-24 14:56:09]
[ERROR][InRequestApi.go:66]2024/07/24 14:56:38 [unexpected end of JSON input]
[INFO][job.go:13]2024/07/24 14:56:57 [添加定时任务2024-07-24 14:56:57]
[ERROR][InRequestApi.go:66]2024/07/24 14:57:00 [unexpected end of JSON input]
[INFO][ruleinterface.go:152]2024/07/24 14:57:14 [rules num:1, load rules cost time:42152800 ns]
[INFO][ruleinterface.go:161]2024/07/24 14:57:14 [execute rule cost 0 ns]
[ERROR][ruleengine.go:63]2024/07/24 14:57:26 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[INFO][job.go:13]2024/07/24 14:58:12 [添加定时任务2024-07-24 14:58:12]
[ERROR][InRequestApi.go:66]2024/07/24 14:58:15 [unexpected end of JSON input]
[INFO][ruleinterface.go:152]2024/07/24 14:59:03 [rules num:1, load rules cost time:31410162900 ns]
[INFO][ruleinterface.go:161]2024/07/24 14:59:14 [execute rule cost 8562732000 ns]
[ERROR][ruleengine.go:63]2024/07/24 14:59:18 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[INFO][job.go:13]2024/07/24 14:59:43 [添加定时任务2024-07-24 14:59:43]
[ERROR][InRequestApi.go:66]2024/07/24 14:59:46 [unexpected end of JSON input]
[INFO][ruleinterface.go:152]2024/07/24 14:59:46 [rules num:1, load rules cost time:43439200 ns]
[INFO][ruleinterface.go:161]2024/07/24 14:59:46 [execute rule cost 0 ns]

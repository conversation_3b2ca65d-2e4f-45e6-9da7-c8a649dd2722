[INFO][job.go:11]2024/08/20 13:35:40 [添加定时任务2024-08-20 13:35:40]
[ERROR][InRequestApi.go:66]2024/08/20 13:35:50 [unexpected end of JSON input]
[INFO][ruleinterface.go:144]2024/08/20 13:35:50 [rules num:1, load rules cost time:28069300 ns]
[INFO][ruleinterface.go:153]2024/08/20 13:35:50 [execute rule cost 0 ns]
[INFO][job.go:11]2024/08/20 13:39:34 [添加定时任务2024-08-20 13:39:34]
[ERROR][InRequestApi.go:66]2024/08/20 13:39:36 [unexpected end of JSON input]
[ERROR][ruleengine.go:23]2024/08/20 13:39:36 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[ERROR][InRequestApi.go:66]2024/08/20 13:40:06 [unexpected end of JSON input]
[ERROR][ruleengine.go:23]2024/08/20 13:40:24 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[INFO][job.go:11]2024/08/20 13:40:42 [添加定时任务2024-08-20 13:40:42]
[ERROR][InRequestApi.go:66]2024/08/20 13:40:44 [unexpected end of JSON input]
[ERROR][ruleengine.go:23]2024/08/20 13:41:30 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[INFO][job.go:11]2024/08/20 13:41:41 [添加定时任务2024-08-20 13:41:41]
[ERROR][InRequestApi.go:66]2024/08/20 13:41:43 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 13:42:07 [添加定时任务2024-08-20 13:42:07]
[ERROR][InRequestApi.go:66]2024/08/20 13:42:11 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/08/20 13:42:50 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 13:44:29 [添加定时任务2024-08-20 13:44:29]
[ERROR][InRequestApi.go:66]2024/08/20 13:45:21 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 13:47:13 [添加定时任务2024-08-20 13:47:13]
[ERROR][InRequestApi.go:66]2024/08/20 13:47:38 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 13:51:38 [添加定时任务2024-08-20 13:51:38]
[ERROR][InRequestApi.go:66]2024/08/20 13:51:43 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 13:52:22 [添加定时任务2024-08-20 13:52:22]
[ERROR][InRequestApi.go:66]2024/08/20 13:52:25 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 13:58:49 [添加定时任务2024-08-20 13:58:49]
[ERROR][InRequestApi.go:66]2024/08/20 13:58:53 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 13:59:51 [添加定时任务2024-08-20 13:59:51]
[ERROR][InRequestApi.go:66]2024/08/20 13:59:54 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/08/20 14:00:17 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 14:00:51 [添加定时任务2024-08-20 14:00:51]
[ERROR][InRequestApi.go:66]2024/08/20 14:00:54 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/08/20 14:01:58 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 14:04:27 [添加定时任务2024-08-20 14:04:27]
[ERROR][InRequestApi.go:66]2024/08/20 14:04:34 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/08/20 14:05:01 [unexpected end of JSON input]
[INFO][ruleinterface.go:144]2024/08/20 14:05:01 [rules num:1, load rules cost time:20104600 ns]
[INFO][ruleinterface.go:153]2024/08/20 14:05:01 [execute rule cost 522800 ns]
[INFO][job.go:11]2024/08/20 14:23:46 [添加定时任务2024-08-20 14:23:46]
[ERROR][InRequestApi.go:66]2024/08/20 14:23:55 [unexpected end of JSON input]
[INFO][ruleinterface.go:144]2024/08/20 14:23:55 [rules num:1, load rules cost time:23443300 ns]
[INFO][ruleinterface.go:153]2024/08/20 14:23:55 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/08/20 14:24:20 [unexpected end of JSON input]
[INFO][ruleinterface.go:144]2024/08/20 14:24:46 [rules num:1, load rules cost time:22476800 ns]
[INFO][ruleinterface.go:153]2024/08/20 14:24:46 [execute rule cost 0 ns]
[INFO][job.go:11]2024/08/20 14:25:11 [添加定时任务2024-08-20 14:25:11]
[ERROR][InRequestApi.go:66]2024/08/20 14:25:18 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 14:25:39 [添加定时任务2024-08-20 14:25:39]
[ERROR][InRequestApi.go:66]2024/08/20 14:25:43 [unexpected end of JSON input]
[INFO][ruleinterface.go:144]2024/08/20 14:25:43 [rules num:1, load rules cost time:14561000 ns]
[INFO][ruleinterface.go:153]2024/08/20 14:25:43 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/08/20 14:25:58 [unexpected end of JSON input]
[INFO][ruleinterface.go:144]2024/08/20 14:26:26 [rules num:1, load rules cost time:11989100 ns]
[INFO][ruleinterface.go:153]2024/08/20 14:26:26 [execute rule cost 0 ns]
[INFO][job.go:11]2024/08/20 14:27:10 [添加定时任务2024-08-20 14:27:10]
[ERROR][InRequestApi.go:66]2024/08/20 14:27:14 [unexpected end of JSON input]
[INFO][ruleinterface.go:284]2024/08/20 14:27:15 [rules num:1, load rules cost time:30313500 ns]
[ERROR][InRequestApi.go:66]2024/08/20 14:27:28 [unexpected end of JSON input]
[INFO][ruleinterface.go:43]2024/08/20 14:27:28 [rules num:1, load rules cost time:9893400 ns]
[ERROR][ruleengine.go:65]2024/08/20 14:27:28 [Recovered from panic: [rule: "诊断判断病种是否存在01" executed, error:
 line 3, column 9, code: disease.IsExistByDiagnosis("238862"), Not found method: "disease.IsExistByDiagnosis(..)" ]]
[ERROR][InRequestApi.go:66]2024/08/20 14:28:09 [unexpected end of JSON input]
[INFO][ruleinterface.go:144]2024/08/20 14:28:09 [rules num:1, load rules cost time:11069500 ns]
[INFO][ruleinterface.go:153]2024/08/20 14:28:09 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/08/20 14:28:18 [unexpected end of JSON input]
[INFO][ruleinterface.go:144]2024/08/20 14:28:18 [rules num:1, load rules cost time:29655400 ns]
[INFO][ruleinterface.go:153]2024/08/20 14:28:18 [execute rule cost 0 ns]
[INFO][job.go:11]2024/08/20 14:29:05 [添加定时任务2024-08-20 14:29:05]
[ERROR][InRequestApi.go:66]2024/08/20 14:29:07 [unexpected end of JSON input]
[INFO][ruleinterface.go:144]2024/08/20 14:29:07 [rules num:1, load rules cost time:23002700 ns]
[INFO][ruleinterface.go:153]2024/08/20 14:29:07 [execute rule cost 0 ns]
[INFO][job.go:11]2024/08/20 14:29:36 [添加定时任务2024-08-20 14:29:36]
[ERROR][InRequestApi.go:66]2024/08/20 14:29:40 [unexpected end of JSON input]
[ERROR][ruleengine.go:65]2024/08/20 14:30:10 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[INFO][job.go:11]2024/08/20 14:31:42 [添加定时任务2024-08-20 14:31:42]
[ERROR][InRequestApi.go:66]2024/08/20 14:31:44 [unexpected end of JSON input]
[INFO][ruleinterface.go:144]2024/08/20 14:31:45 [rules num:1, load rules cost time:17225200 ns]
[INFO][ruleinterface.go:153]2024/08/20 14:31:45 [execute rule cost 0 ns]
[INFO][job.go:11]2024/08/20 15:45:42 [添加定时任务2024-08-20 15:45:42]
[INFO][job.go:11]2024/08/20 15:54:16 [添加定时任务2024-08-20 15:54:16]
[INFO][job.go:11]2024/08/20 15:57:43 [添加定时任务2024-08-20 15:57:43]
[ERROR][InRequestApi.go:66]2024/08/20 15:57:45 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 16:29:17 [添加定时任务2024-08-20 16:29:17]
[INFO][job.go:11]2024/08/20 16:53:54 [添加定时任务2024-08-20 16:53:54]
[INFO][job.go:11]2024/08/20 17:50:55 [添加定时任务2024-08-20 17:50:55]
[ERROR][InRequestApi.go:66]2024/08/20 17:51:03 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/08/20 17:51:22 [unexpected end of JSON input]
[ERROR][ruleengine.go:66]2024/08/20 17:51:22 [Recovered from panic: [line 3:17 token recognition error at: '年' line 3:18 token recognition error at: '龄' line 3:43 token recognition error at: '生' line 3:44 token recognition error at: '理' line 3:45 token recognition error at: '性' line 3:46 token recognition error at: '别']]
[INFO][job.go:11]2024/08/20 18:15:00 [添加定时任务2024-08-20 18:15:00]
[ERROR][InRequestApi.go:66]2024/08/20 18:15:04 [unexpected end of JSON input]
[INFO][ruleinterface.go:102]2024/08/20 18:15:04 [rules num:1, load rules cost time:31272300 ns]
[ERROR][ruleengine.go:66]2024/08/20 18:15:04 [Recovered from panic: [rule: "男-20" executed, error:
 line 3, column 9, code: patient.Gender, evaluate Expression err! ]]
[INFO][job.go:11]2024/08/20 18:15:39 [添加定时任务2024-08-20 18:15:39]
[ERROR][InRequestApi.go:66]2024/08/20 18:15:41 [unexpected end of JSON input]
[INFO][ruleinterface.go:102]2024/08/20 18:15:41 [rules num:1, load rules cost time:31246900 ns]
[ERROR][InRequestApi.go:66]2024/08/20 18:16:06 [unexpected end of JSON input]
[INFO][ruleinterface.go:102]2024/08/20 18:16:06 [rules num:1, load rules cost time:16921000 ns]
[INFO][job.go:11]2024/08/20 18:30:00 [添加定时任务2024-08-20 18:30:00]
[ERROR][InRequestApi.go:66]2024/08/20 18:30:05 [unexpected end of JSON input]
[INFO][ruleinterface.go:102]2024/08/20 18:30:05 [rules num:1, load rules cost time:22552200 ns]
[INFO][job.go:11]2024/08/20 18:31:37 [添加定时任务2024-08-20 18:31:37]
[ERROR][InRequestApi.go:66]2024/08/20 18:31:41 [unexpected end of JSON input]
[INFO][ruleinterface.go:102]2024/08/20 18:31:41 [rules num:1, load rules cost time:14131000 ns]
[INFO][job.go:11]2024/08/20 18:35:10 [添加定时任务2024-08-20 18:35:10]
[ERROR][InRequestApi.go:66]2024/08/20 18:35:17 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 18:35:31 [添加定时任务2024-08-20 18:35:31]
[INFO][job.go:11]2024/08/20 18:35:46 [添加定时任务2024-08-20 18:35:46]
[ERROR][InRequestApi.go:66]2024/08/20 18:35:48 [unexpected end of JSON input]
[INFO][ruleinterface.go:103]2024/08/20 18:35:49 [rules num:1, load rules cost time:26802000 ns]
[INFO][ruleinterface.go:113]2024/08/20 18:36:03 [execute rule cost 14102009100 ns]
[INFO][job.go:11]2024/08/20 18:36:07 [添加定时任务2024-08-20 18:36:07]
[ERROR][InRequestApi.go:66]2024/08/20 18:36:09 [unexpected end of JSON input]
[INFO][ruleinterface.go:103]2024/08/20 18:36:09 [rules num:1, load rules cost time:15578900 ns]
[INFO][ruleinterface.go:113]2024/08/20 18:36:09 [execute rule cost 0 ns]
[INFO][job.go:11]2024/08/20 19:14:10 [添加定时任务2024-08-20 19:14:10]
[ERROR][InRequestApi.go:66]2024/08/20 19:14:14 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/08/20 19:14:52 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 19:15:50 [添加定时任务2024-08-20 19:15:50]
[ERROR][InRequestApi.go:66]2024/08/20 19:15:52 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 19:16:18 [添加定时任务2024-08-20 19:16:18]
[ERROR][InRequestApi.go:66]2024/08/20 19:16:21 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 19:16:46 [添加定时任务2024-08-20 19:16:46]
[ERROR][InRequestApi.go:66]2024/08/20 19:16:49 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 19:18:07 [添加定时任务2024-08-20 19:18:07]
[ERROR][InRequestApi.go:66]2024/08/20 19:18:09 [unexpected end of JSON input]
[INFO][ruleinterface.go:285]2024/08/20 19:18:10 [rules num:1, load rules cost time:13667700 ns]
[INFO][ruleinterface.go:294]2024/08/20 19:18:10 [execute rule cost 0 ns]
[INFO][job.go:11]2024/08/20 19:18:53 [添加定时任务2024-08-20 19:18:53]
[ERROR][InRequestApi.go:66]2024/08/20 19:18:55 [unexpected end of JSON input]
[INFO][ruleinterface.go:285]2024/08/20 19:18:55 [rules num:1, load rules cost time:19134700 ns]
[INFO][ruleinterface.go:294]2024/08/20 19:18:55 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/08/20 19:19:16 [unexpected end of JSON input]
[INFO][ruleinterface.go:285]2024/08/20 19:19:16 [rules num:1, load rules cost time:10338900 ns]
[INFO][ruleinterface.go:294]2024/08/20 19:19:16 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/08/20 19:19:31 [unexpected end of JSON input]
[INFO][ruleinterface.go:285]2024/08/20 19:19:46 [rules num:1, load rules cost time:14828800 ns]
[INFO][ruleinterface.go:294]2024/08/20 19:19:46 [execute rule cost 0 ns]
[INFO][job.go:11]2024/08/20 19:20:27 [添加定时任务2024-08-20 19:20:27]
[ERROR][InRequestApi.go:66]2024/08/20 19:20:29 [unexpected end of JSON input]
[INFO][ruleinterface.go:285]2024/08/20 19:20:30 [rules num:1, load rules cost time:20578300 ns]
[INFO][ruleinterface.go:294]2024/08/20 19:20:30 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/08/20 19:20:38 [unexpected end of JSON input]
[INFO][ruleinterface.go:285]2024/08/20 19:20:45 [rules num:1, load rules cost time:16593100 ns]
[INFO][ruleinterface.go:294]2024/08/20 19:20:45 [execute rule cost 0 ns]
[INFO][job.go:11]2024/08/20 19:37:03 [添加定时任务2024-08-20 19:37:03]
[ERROR][InRequestApi.go:66]2024/08/20 19:37:41 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 19:37:45 [添加定时任务2024-08-20 19:37:45]
[ERROR][InRequestApi.go:66]2024/08/20 19:37:48 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 19:39:07 [添加定时任务2024-08-20 19:39:07]
[ERROR][InRequestApi.go:66]2024/08/20 19:39:09 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/08/20 19:39:25 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/08/20 19:39:48 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 19:40:04 [添加定时任务2024-08-20 19:40:04]
[ERROR][InRequestApi.go:66]2024/08/20 19:40:06 [unexpected end of JSON input]
[INFO][ruleinterface.go:285]2024/08/20 19:40:06 [rules num:2, load rules cost time:26565400 ns]
[ERROR][InRequestApi.go:66]2024/08/20 19:40:43 [unexpected end of JSON input]
[INFO][ruleinterface.go:285]2024/08/20 19:40:43 [rules num:2, load rules cost time:17542600 ns]
[INFO][job.go:11]2024/08/20 19:40:50 [添加定时任务2024-08-20 19:40:50]
[ERROR][InRequestApi.go:66]2024/08/20 19:40:55 [unexpected end of JSON input]
[INFO][ruleinterface.go:285]2024/08/20 19:40:55 [rules num:2, load rules cost time:31531300 ns]
[INFO][job.go:11]2024/08/20 19:41:09 [添加定时任务2024-08-20 19:41:09]
[ERROR][InRequestApi.go:66]2024/08/20 19:41:11 [unexpected end of JSON input]
[INFO][ruleinterface.go:285]2024/08/20 19:41:11 [rules num:2, load rules cost time:31071900 ns]
[INFO][job.go:11]2024/08/20 19:42:26 [添加定时任务2024-08-20 19:42:26]
[ERROR][InRequestApi.go:66]2024/08/20 19:42:29 [unexpected end of JSON input]
[INFO][ruleinterface.go:285]2024/08/20 19:42:54 [rules num:2, load rules cost time:23719684200 ns]
[INFO][job.go:11]2024/08/20 19:43:46 [添加定时任务2024-08-20 19:43:46]
[ERROR][InRequestApi.go:66]2024/08/20 19:43:48 [unexpected end of JSON input]
[INFO][ruleinterface.go:285]2024/08/20 19:43:48 [rules num:2, load rules cost time:33853300 ns]
[INFO][job.go:11]2024/08/20 19:44:47 [添加定时任务2024-08-20 19:44:47]
[ERROR][InRequestApi.go:66]2024/08/20 19:44:53 [unexpected end of JSON input]
[INFO][ruleinterface.go:285]2024/08/20 19:44:53 [rules num:1, load rules cost time:35940200 ns]
[INFO][job.go:11]2024/08/20 19:45:46 [添加定时任务2024-08-20 19:45:46]
[ERROR][InRequestApi.go:66]2024/08/20 19:45:49 [unexpected end of JSON input]
[ERROR][InRequestApi.go:66]2024/08/20 19:46:24 [unexpected end of JSON input]
[INFO][job.go:11]2024/08/20 19:48:19 [添加定时任务2024-08-20 19:48:19]
[ERROR][InRequestApi.go:66]2024/08/20 19:48:22 [unexpected end of JSON input]
[INFO][ruleinterface.go:285]2024/08/20 19:48:22 [rules num:1, load rules cost time:37940500 ns]
[ERROR][ruleinterface.go:251]2024/08/20 19:48:22 [Recovered from panic: reflect: call of reflect.Value.FieldByName on zero Value]
[ERROR][InRequestApi.go:66]2024/08/20 19:48:49 [unexpected end of JSON input]
[INFO][ruleinterface.go:285]2024/08/20 19:48:49 [rules num:1, load rules cost time:17238800 ns]
[ERROR][ruleinterface.go:251]2024/08/20 19:48:49 [Recovered from panic: reflect: call of reflect.Value.FieldByName on zero Value]
[INFO][job.go:11]2024/08/20 20:01:34 [添加定时任务2024-08-20 20:01:34]
[ERROR][InRequestApi.go:66]2024/08/20 20:01:41 [unexpected end of JSON input]
[INFO][ruleinterface.go:103]2024/08/20 20:01:41 [rules num:1, load rules cost time:16038900 ns]
[INFO][ruleinterface.go:113]2024/08/20 20:01:41 [execute rule cost 518000 ns]
[ERROR][InRequestApi.go:66]2024/08/20 20:02:34 [unexpected end of JSON input]
[INFO][ruleinterface.go:103]2024/08/20 20:02:43 [rules num:1, load rules cost time:12138400 ns]
[INFO][ruleinterface.go:113]2024/08/20 20:02:43 [execute rule cost 0 ns]
[INFO][job.go:11]2024/08/20 20:09:02 [添加定时任务2024-08-20 20:09:02]
[ERROR][InRequestApi.go:66]2024/08/20 20:09:19 [unexpected end of JSON input]
[INFO][ruleinterface.go:103]2024/08/20 20:09:51 [rules num:1, load rules cost time:18554700 ns]
[INFO][ruleinterface.go:113]2024/08/20 20:09:51 [execute rule cost 0 ns]
[INFO][job.go:11]2024/08/20 20:10:49 [添加定时任务2024-08-20 20:10:49]
[ERROR][InRequestApi.go:66]2024/08/20 20:10:52 [unexpected end of JSON input]
[INFO][ruleinterface.go:103]2024/08/20 20:11:18 [rules num:1, load rules cost time:15898839200 ns]
[INFO][ruleinterface.go:113]2024/08/20 20:11:32 [execute rule cost 6984644300 ns]
[INFO][job.go:11]2024/08/20 20:14:54 [添加定时任务2024-08-20 20:14:54]
[ERROR][InRequestApi.go:66]2024/08/20 20:14:57 [unexpected end of JSON input]
[INFO][ruleinterface.go:103]2024/08/20 20:15:02 [rules num:1, load rules cost time:18689000 ns]
[INFO][ruleinterface.go:113]2024/08/20 20:15:02 [execute rule cost 0 ns]
[INFO][job.go:11]2024/08/20 20:15:08 [添加定时任务2024-08-20 20:15:08]
[ERROR][InRequestApi.go:66]2024/08/20 20:15:10 [unexpected end of JSON input]
[INFO][ruleinterface.go:103]2024/08/20 20:15:10 [rules num:1, load rules cost time:14966300 ns]
[INFO][ruleinterface.go:113]2024/08/20 20:15:10 [execute rule cost 0 ns]

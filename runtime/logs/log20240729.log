[INFO][job.go:11]2024/07/29 14:02:51 [添加定时任务2024-07-29 14:02:51]
[INFO][job.go:11]2024/07/29 14:38:59 [添加定时任务2024-07-29 14:38:59]
[INFO][job.go:11]2024/07/29 14:51:48 [添加定时任务2024-07-29 14:51:48]
[INFO][job.go:11]2024/07/29 15:00:19 [添加定时任务2024-07-29 15:00:19]
[INFO][ruleinterface.go:43]2024/07/29 15:00:23 [rules num:1, load rules cost time:45223100 ns]
[INFO][ruleinterface.go:53]2024/07/29 15:00:23 [execute rule cost 0 ns]
[INFO][ruleinterface.go:43]2024/07/29 15:00:34 [rules num:1, load rules cost time:37089100 ns]
[INFO][ruleinterface.go:53]2024/07/29 15:00:34 [execute rule cost 0 ns]
[INFO][ruleinterface.go:43]2024/07/29 15:00:52 [rules num:1, load rules cost time:43101500 ns]
[INFO][ruleinterface.go:53]2024/07/29 15:00:52 [execute rule cost 0 ns]
[INFO][ruleinterface.go:43]2024/07/29 15:01:01 [rules num:1, load rules cost time:28489600 ns]
[INFO][ruleinterface.go:53]2024/07/29 15:01:01 [execute rule cost 0 ns]
[INFO][ruleinterface.go:43]2024/07/29 15:02:16 [rules num:1, load rules cost time:38159000 ns]
[INFO][ruleinterface.go:53]2024/07/29 15:02:16 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/29 15:06:16 [unexpected end of JSON input]
[INFO][ruleinterface.go:43]2024/07/29 15:06:16 [rules num:1, load rules cost time:31572700 ns]
[INFO][ruleinterface.go:53]2024/07/29 15:06:16 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/29 15:14:42 [unexpected end of JSON input]
[INFO][ruleinterface.go:43]2024/07/29 15:14:42 [rules num:1, load rules cost time:33967500 ns]
[ERROR][ruleengine.go:64]2024/07/29 15:14:42 [Recovered from panic: [rule: "Zopiclone Tablets use with caution if liver function is abnormal" executed, error:
 line 1, column 176, code: testreports.IsAnyReportValid(), Not found method: "testreports.IsAnyReportValid(..)" ]]
[ERROR][InRequestApi.go:66]2024/07/29 15:15:08 [unexpected end of JSON input]
[INFO][ruleinterface.go:43]2024/07/29 15:15:25 [rules num:1, load rules cost time:*********** ns]
[ERROR][ruleengine.go:64]2024/07/29 15:15:25 [Recovered from panic: [rule: "Zopiclone Tablets use with caution if liver function is abnormal" executed, error:
 line 1, column 176, code: testreports.IsAnyReportValid(), Not found method: "testreports.IsAnyReportValid(..)" ]]
[INFO][job.go:11]2024/07/29 15:16:15 [添加定时任务2024-07-29 15:16:15]
[ERROR][InRequestApi.go:66]2024/07/29 15:16:19 [unexpected end of JSON input]
[INFO][ruleinterface.go:43]2024/07/29 15:16:19 [rules num:1, load rules cost time:36568000 ns]
[INFO][ruleinterface.go:53]2024/07/29 15:16:19 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/29 15:16:31 [unexpected end of JSON input]
[INFO][ruleinterface.go:43]2024/07/29 15:16:31 [rules num:1, load rules cost time:26338900 ns]
[INFO][ruleinterface.go:53]2024/07/29 15:16:31 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/29 15:17:48 [unexpected end of JSON input]
[INFO][ruleinterface.go:43]2024/07/29 15:17:48 [rules num:1, load rules cost time:36393700 ns]
[INFO][ruleinterface.go:53]2024/07/29 15:17:48 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/29 15:17:55 [unexpected end of JSON input]
[INFO][ruleinterface.go:43]2024/07/29 15:17:55 [rules num:1, load rules cost time:24857200 ns]
[INFO][ruleinterface.go:53]2024/07/29 15:17:55 [execute rule cost 0 ns]
[ERROR][InRequestApi.go:66]2024/07/29 15:18:09 [unexpected end of JSON input]
[INFO][ruleinterface.go:43]2024/07/29 15:18:09 [rules num:1, load rules cost time:32095600 ns]
[INFO][ruleinterface.go:53]2024/07/29 15:18:09 [execute rule cost 0 ns]

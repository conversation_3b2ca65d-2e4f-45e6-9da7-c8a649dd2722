[INFO][job.go:11]2024/07/28 21:30:04 [添加定时任务2024-07-28 21:30:04]
[INFO][job.go:11]2024/07/28 21:37:03 [添加定时任务2024-07-28 21:37:03]
[INFO][job.go:11]2024/07/28 22:05:23 [添加定时任务2024-07-28 22:05:23]
[ERROR][InRequestApi.go:52]2024/07/28 22:10:52 [发送请求失败:%v <nil>]
[ERROR][ruleengine.go:64]2024/07/28 22:11:11 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[ERROR][InRequestApi.go:52]2024/07/28 22:11:53 [发送请求失败:%v <nil>]
[ERROR][ruleengine.go:64]2024/07/28 22:11:53 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[INFO][job.go:11]2024/07/28 22:12:17 [添加定时任务2024-07-28 22:12:17]
[ERROR][InRequestApi.go:52]2024/07/28 22:13:06 [发送请求失败:%v <nil>]
[ERROR][ruleengine.go:64]2024/07/28 22:15:27 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[INFO][job.go:11]2024/07/28 22:15:42 [添加定时任务2024-07-28 22:15:42]
[INFO][job.go:11]2024/07/28 22:15:57 [添加定时任务2024-07-28 22:15:57]
[ERROR][InRequestApi.go:52]2024/07/28 22:16:06 [发送请求失败:%v <nil>]
[ERROR][ruleengine.go:64]2024/07/28 22:16:06 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[ERROR][InRequestApi.go:52]2024/07/28 22:16:25 [发送请求失败:%v <nil>]
[ERROR][ruleengine.go:64]2024/07/28 22:16:32 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[INFO][job.go:11]2024/07/28 22:16:40 [添加定时任务2024-07-28 22:16:40]
[ERROR][InRequestApi.go:52]2024/07/28 22:16:48 [发送请求失败:%v <nil>]
[ERROR][ruleengine.go:64]2024/07/28 22:16:48 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[ERROR][InRequestApi.go:52]2024/07/28 22:17:04 [发送请求失败:%v <nil>]
[ERROR][ruleengine.go:64]2024/07/28 22:17:04 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[ERROR][InRequestApi.go:52]2024/07/28 22:17:24 [发送请求失败:%v <nil>]
[ERROR][ruleengine.go:64]2024/07/28 22:17:32 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[INFO][job.go:11]2024/07/28 22:19:13 [添加定时任务2024-07-28 22:19:13]
[ERROR][InRequestApi.go:52]2024/07/28 22:19:46 [发送请求失败:%v <nil>]
[ERROR][ruleengine.go:64]2024/07/28 22:20:14 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[INFO][job.go:11]2024/07/28 22:20:23 [添加定时任务2024-07-28 22:20:23]
[ERROR][ruleengine.go:63]2024/07/28 22:20:48 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[INFO][job.go:11]2024/07/28 22:21:04 [添加定时任务2024-07-28 22:21:04]
[ERROR][ruleengine.go:63]2024/07/28 22:21:07 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[ERROR][ruleengine.go:63]2024/07/28 22:21:09 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[ERROR][ruleengine.go:63]2024/07/28 22:24:04 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[INFO][job.go:11]2024/07/28 22:24:09 [添加定时任务2024-07-28 22:24:09]
[ERROR][ruleengine.go:63]2024/07/28 22:25:36 [Recovered from panic: runtime error: invalid memory address or nil pointer dereference]
[INFO][job.go:11]2024/07/28 22:25:55 [添加定时任务2024-07-28 22:25:55]
[INFO][ruleinterface.go:101]2024/07/28 22:26:00 [rules num:1, load rules cost time:41068700 ns]
[ERROR][ruleengine.go:63]2024/07/28 22:26:00 [Recovered from panic: [rule: "Zopiclone Tablets use with caution if liver function is abnormal" executed, error:
 line 1, column 176, code: testreports.IsAnyReportValid(), Not found method: "testreports.IsAnyReportValid(..)" ]]
[INFO][ruleinterface.go:101]2024/07/28 22:26:16 [rules num:1, load rules cost time:6295750200 ns]
[ERROR][ruleengine.go:63]2024/07/28 22:26:16 [Recovered from panic: [rule: "Zopiclone Tablets use with caution if liver function is abnormal" executed, error:
 line 1, column 176, code: testreports.IsAnyReportValid(), Not found method: "testreports.IsAnyReportValid(..)" ]]
[INFO][job.go:11]2024/07/28 22:26:37 [添加定时任务2024-07-28 22:26:37]
[INFO][ruleinterface.go:101]2024/07/28 22:26:54 [rules num:1, load rules cost time:*********** ns]
[ERROR][ruleengine.go:63]2024/07/28 22:27:23 [Recovered from panic: [rule: "Zopiclone Tablets use with caution if liver function is abnormal" executed, error:
 line 1, column 176, code: testreports.IsAnyReportValid(), Not found method: "testreports.IsAnyReportValid(..)" ]]

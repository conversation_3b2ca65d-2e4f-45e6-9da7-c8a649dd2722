package kbservice

import (
	"context"
	"encoding/json"
	"fmt"
	"kmbservice/controller/utils"
	"kmbservice/pkg/logging"
	"kmbservice/repository"
	"strconv"
	"strings"
)

type DiseaseCharacteristics struct {
	Gender                       string `json:"gender"`                       //性别
	AgeGroup                     string `json:"ageGroup"`                     //年龄组
	PhysiologicalFunctionalGroup string `json:"physiologicalFunctionalGroup"` //诊断组
	ChangesInWorkQuality         string `json:"changesInWorkQuality"`         //工作质量情况
	ChangesInSleepQuality        string `json:"changesInSleepQuality"`        //睡眠质量情况
	ImpactOnSocialFunction       string `json:"impactOnSocialFunction"`       //社会功能影响程度
	SleepDurationAtNight         string `json:"sleepDurationAtNight"`         //晚上睡眠时间
	TotalSleepTime               string `json:"totalSleepTime"`               //总睡眠时间
}
type Symptom struct {
	SignOrSymptom                           string `json:"signOrSymptom"`                           //征象|症状
	NatureOfPsychiatricSymptoms             string `json:"natureOfPsychiatricSymptoms"`             //精神类症状性质
	TemporalPatterns                        string `json:"temporalPatterns"`                        //时间模式
	StartTimeCharacterOfSignOrSymptom       string `json:"startTimeCharacterOfSignOrSymptom"`       //症状发生时间
	DurationOfSignOrSymptoms                string `json:"durationOfSignOrSymptoms"`                //症状持续时间
	SymptomFrequency                        string `json:"symptomFrequency"`                        //症状发作频率
	DurationOfSignOrSymptomsUpperLimit      int    `json:"durationOfSignOrSymptomsUpperLimit"`      //持续时间上限
	DurationOfSignOrSymptomsLowerLimit      int    `json:"durationOfSignOrSymptomsLowerLimit"`      //持续时间下限
	DurationOfSignOrSymptoms_UpperLimitUnit string `json:"durationOfSignOrSymptoms_UpperLimitUnit"` //上限单位
	DurationOfSignOrSymptoms_LowerLimitUnit string `json:"durationOfSignOrSymptoms_LowerLimitUnit"` //下限单位
	SymptomFrequencyUpperLimit              int    `json:"symptomFrequencyUpperLimit"`              //频率上限
	SymptomFrequencyLowerLimit              int    `json:"symptomFrequencyLowerLimit"`              //频率下限
	SymptomFrequency_UpperLimitUnit         string `json:"symptomFrequency_UpperLimitUnit"`         //上限单位
	SymptomFrequency_LowerLimitUnit         string `json:"symptomFrequency_LowerLimitUnit"`         //下限单位
}
type Simplesymn struct {
	SignOrSymptom     string `json:"signOrSymptom"`     //征象|症状
	SymptomIdentifier string `json:"symptomIdentifier"` //症状编码
}

// 乳腺专科症状查询病种
type BreastParam struct {
	BreastSignOrSymptom             int64  `json:"breast_sign_or_symptom"`               //乳腺征象|症状
	BreastDiseaseOrSyndrome         int64  `json:"breast_disease_or_syndrome"`           //病种
	BreastLocalTemperatureSituation int64  `json:"breast_local_temperature_situation"`   //温度趋势
	BreastTextureOfTheMmass         int64  `json:"breast_texture_of_the_mmass"`          //肿块质地
	BreastMobilityOfTheMass         int64  `json:"breast_mobility_of_the_masse"`         //移动度
	BreastTumorRADSClassification   int64  `json:"breast_tumor_rads_classification"`     //RADS分级
	BreastTNMStagingOfPrimaryTumors int64  `json:"breast_tnm_staging_of_primary_tumors"` //T
	TNMStagingOfRegionalLymphNodes  int64  `json:"tnm_staging_of_regional_lymph_nodes"`  //N
	TNMStagingForDistantMetastasis  int64  `json:"tnm_staging_for_distant_metastasis"`   //M
	LuminalTyping                   int64  `json:"luminal_typing"`                       //luminal
	BreastType                      string `json:"breast_type"`                          //查询类别
}

// RASD与TNM分期
type BreastBRASDTNM struct {
}

func GetDiseaseBySymAndDisChar(character DiseaseCharacteristics, symptoms []Symptom) string {
	// 构建疾病特征部分的Cypher语句
	whereClauses := make([]string, 0)
	queryTemplate := ""
	characterCypher := fmt.Sprintf(`MATCH (d: Disease_or_Syndrome)-[:diseasesRelatedCharacteristics]->(c: Disease_Characteristics)-[:mainSymptomsRelatedToDiseases]->(s: Sign_Or_Symptom) `)
	groupql := GeneratecypherByDisCharactor(character, "c") //拼接诊断组特征没有Where
	if len(groupql) > 0 {
		whereClauses = append(whereClauses, groupql)
		fmt.Sprintf("诊断组QL：", characterCypher)
	}
	symptomql := GeneratecypherBySymptoms(symptoms, "s") //拼接症状特征
	if len(symptomql) > 0 {
		whereClauses = append(whereClauses, symptomql)
		fmt.Printf("症状分组QL：", characterCypher)
	}
	// 组合所有条件
	if len(whereClauses) > 0 {
		queryTemplate = strings.Join(whereClauses, " AND ")
	}
	//cypher := fmt.Sprintf(`MATCH %s RETURN DISTINCT d`, strings.Join(conditions, "\n"))
	cypher := characterCypher + "where " + queryTemplate + "RETURN DISTINCT d"
	fmt.Printf(cypher)
	return cypher
}
func ConvertToKeyValue(input string) (string, error) {
	// 解析 JSON 字符串
	var data []struct {
		Keys   []string `json:"Keys"`
		Values []string `json:"Values"`
	}
	if err := json.Unmarshal([]byte(input), &data); err != nil {
		return "", err
	}

	// 构建键值对 map
	kvMap := make(map[string]string)
	for _, keys := range data {
		for j, key := range keys.Keys {
			// 确保 Values 中有对应的值
			if j < len(keys.Values) {
				kvMap[key] = keys.Values[j]
			}
		}
	}
	// 将 map 编码为 JSON 格式字符串
	jsonData, err := json.Marshal(kvMap)
	if err != nil {
		return "", err
	}

	return string(jsonData), nil
}

// 根据疾病组特征生成where后面条件，ch代表诊断组别名
func GeneratecypherByDisCharactor(character DiseaseCharacteristics, ch string) string {
	// ch 判断作为诊断组别名
	whereClauses := make([]string, 0)
	if character.Gender != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("(%s.gender = '%s'", ch, character.Gender))
	}
	if character.AgeGroup != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("  %s.ageGroup = '%s'", ch, character.AgeGroup))
	}
	if character.PhysiologicalFunctionalGroup != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("  %s.physiologicalFunctionalGroup='%s'", ch, character.PhysiologicalFunctionalGroup))
	}
	if character.ChangesInWorkQuality != "" {
		whereClauses = append(whereClauses, fmt.Sprintf(" %s.changesInWorkQuality = '%s'", ch, character.ChangesInWorkQuality))
	}
	if character.ChangesInSleepQuality != "" {
		whereClauses = append(whereClauses, fmt.Sprintf(" %s.changesInSleepQuality = '%s'", ch, character.ChangesInSleepQuality))
	}
	if character.ImpactOnSocialFunction != "" {
		whereClauses = append(whereClauses, fmt.Sprintf(" %s.impactOnSocialFunction ='%s'", ch, character.ImpactOnSocialFunction))
	}
	if character.SleepDurationAtNight != "" {
		whereClauses = append(whereClauses, fmt.Sprintf(" %s.sleepDurationAtNight = '%s'", ch, character.SleepDurationAtNight))
	}
	if character.TotalSleepTime != "" {
		whereClauses = append(whereClauses, fmt.Sprintf(" %s.totalSleepTime = '%s'", ch, character.TotalSleepTime))
	}
	if len(whereClauses) > 0 {
		queryTemplate := strings.Join(whereClauses, " AND ") + ")"
		return queryTemplate
	}
	return ""
}
func GeneratecypherBySymptoms(symptoms []Symptom, ch string) string {
	var conditions []string
	for _, symptom := range symptoms {
		SignOrSymptom := fmt.Sprintf(`(%s.signOrSymptom='%s' AND `, ch, symptom.SignOrSymptom)
		durationConditions := generateDurationConditions(symptom.DurationOfSignOrSymptomsUpperLimit, symptom.DurationOfSignOrSymptomsLowerLimit, "月")
		frequencyConditions := generateFrequencyConditions(symptom.SymptomFrequencyUpperLimit, symptom.SymptomFrequencyLowerLimit, "次/周")
		symptomCypher := fmt.Sprintf(` %s AND %s`, durationConditions, frequencyConditions)
		conditions = append(conditions, SignOrSymptom+symptomCypher)
	}
	if len(conditions) > 0 {
		queryTemplate := "(" + strings.Join(conditions, " AND ") + ")"
		return queryTemplate
	}
	return ""
}
func generateFrequencyConditions(upperLimit, lowerLimit int, unit string) string {
	var conditions []string
	// 构建频率条件部分的Cypher语句
	if upperLimit >= 0 {
		conditions = append(conditions, fmt.Sprintf(`(s.symptomFrequency_UpperLimitUnit='%s' AND s.symptomFrequency_LowerLimitUnit<>'%s' AND s.symptomFrequency_UpperLimit>=%d)`, unit, unit, upperLimit))
	}
	if lowerLimit >= 0 {
		conditions = append(conditions, fmt.Sprintf(`(s.symptomFrequency_UpperLimitUnit='%s' AND s.symptomFrequency_LowerLimitUnit='%s' AND s.symptomFrequency_UpperLimit>=%d AND s.symptomFrequency_LowerLimit<%d)`, unit, unit, upperLimit, lowerLimit))
	}
	conditions = append(conditions, fmt.Sprintf(`(s.symptomFrequency_UpperLimitUnit<>'%s' AND s.symptomFrequency_LowerLimitUnit='%s' AND s.symptomFrequency_LowerLimit<%d)`, unit, unit, lowerLimit))
	return strings.Join(conditions, " OR ")
}
func generateDurationConditions(upperLimit, lowerLimit int, unit string) string {
	var conditions []string
	// 构建持续时间条件部分的Cypher语句
	if upperLimit >= 0 {
		conditions = append(conditions, fmt.Sprintf(`(s.durationOfSignOrSymptoms_UpperLimitUnit='%s' AND s.durationOfSignOrSymptoms_LowerLimitUnit<>'%s' AND s.durationOfSignOrSymptoms_UpperLimit>=%d)`, unit, unit, upperLimit))
	}
	if lowerLimit >= 0 {
		conditions = append(conditions, fmt.Sprintf(`(s.durationOfSignOrSymptoms_UpperLimitUnit='%s' AND s.durationOfSignOrSymptoms_LowerLimitUnit='%s' AND s.durationOfSignOrSymptoms_UpperLimit>=%d AND s.durationOfSignOrSymptoms_LowerLimit<%d)`, unit, unit, upperLimit, lowerLimit))
	}
	conditions = append(conditions, fmt.Sprintf(`(s.durationOfSignOrSymptoms_UpperLimitUnit<>'%s' AND s.durationOfSignOrSymptoms_LowerLimitUnit='%s' AND s.durationOfSignOrSymptoms_LowerLimit<%d)`, unit, unit, lowerLimit))
	return strings.Join(conditions, " OR ")
}

// 根据人的特征获取相关诊断信息
func GetDiseaseByDisChar(condition DiseaseCharacteristics) string {
	// 定义查询语句模板
	queryTemplate := "MATCH (d:Disease_or_Syndrome)-[:diseasesRelatedCharacteristics]->(c:Disease_Characteristics)"
	whereClauses := make([]string, 0)

	if condition.Gender != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("c.gender = '%s'", condition.Gender))
	}
	if condition.AgeGroup != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("c.ageGroup = '%s'", condition.AgeGroup))
	}
	if condition.PhysiologicalFunctionalGroup != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("c.PhysiologicalFunctionalGroup = '%s'", condition.PhysiologicalFunctionalGroup))
	}
	if condition.ChangesInWorkQuality != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("c.changesInWorkQuality = '%s'", condition.ChangesInWorkQuality))
	}
	if condition.ChangesInSleepQuality != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("c.changesInSleepQuality = '%s'", condition.ChangesInSleepQuality))
	}
	if condition.ImpactOnSocialFunction != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("c.ImpactOnSocialFunction ='%s'", condition.ImpactOnSocialFunction))
	}
	if condition.SleepDurationAtNight != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("c.sleepDurationAtNight = '%s'", condition.SleepDurationAtNight))
	}
	if condition.TotalSleepTime != "" {
		whereClauses = append(whereClauses, fmt.Sprintf("c.totalSleepTime = '%s'", condition.TotalSleepTime))
	}
	// 生成查询语句
	if len(whereClauses) > 0 {
		queryTemplate += "WHERE " + strings.Join(whereClauses, " AND ")
	}
	query := queryTemplate + " return DISTINCT d.classificationOfAcuteDegreeOfDisease,d.code,d.diseaseClassification,d.diseaseOrSyndrome,d.name,d.tcmDisease"

	return query
}

/*
下面是新增加的内容，为精神专科知识图谱提供推理
*/
// 根据症状查询诊断 flag 1:主要症状 2 次要症状
//func GetDiseases(symns []Simplesymn, flag int, breast BreastParam, breastType string) string {
//	ctx := context.Background()
//	queryTemplate := ""
//	whereTemplate := ""
//	switch flag {
//	case 1:
//		{
//			whereClauses := make([]string, 0)
//			selectClauses := make([]string, 0)
//			for i, symn := range symns {
//				characterCypher := fmt.Sprintf(`(d:Disease_or_Syndrome)-[:mainSymptomsRelatedToDiseases]->(%s:Sign_Or_Symptom)`, "s"+strconv.Itoa(i))
//				selectClauses = append(selectClauses, characterCypher)
//				SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%s'`, "s"+strconv.Itoa(i), symn.SignOrSymptom)
//				whereClauses = append(whereClauses, SignOrSymptom)
//			}
//
//			if len(selectClauses) > 0 {
//				queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")
//
//			}
//			if len(whereClauses) > 0 {
//				whereTemplate = strings.Join(whereClauses, " AND ")
//
//			}
//			//判断乳腺专科是否有参数传过来，false就是有不为空的
//			sign := isEmptyStats(breast)
//			if sign == false {
//				switch breastType {
//				case "1":
//					//查病种
//					whereClauses := make([]string, 0)
//					selectClauses := make([]string, 0)
//					for _, symn := range symns {
//						characterCypher := fmt.Sprintf(`(d:Disease_or_Syndrome)-[:mainSymptomsRelatedToDiseases]->(%s:Sign_Or_Symptom)`, "s")
//						selectClauses = append(selectClauses, characterCypher)
//						SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%s'`, "s", symn.SignOrSymptom)
//						whereClauses = append(whereClauses, SignOrSymptom)
//					}
//					if len(selectClauses) > 0 {
//						queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")
//					}
//					if len(whereClauses) > 0 {
//						whereTemplate = strings.Join(whereClauses, " AND ")
//					}
//
//					var parts []string
//					if breast.BreastSignOrSymptom != 0 {
//						parts = append(parts, fmt.Sprintf("s.signOrSymptom = %d", breast.BreastSignOrSymptom))
//					}
//					if breast.BreastLocalTemperatureSituation != 0 {
//						parts = append(parts, fmt.Sprintf("s.localTemperatureSituation = %d", breast.BreastLocalTemperatureSituation))
//					}
//					if breast.BreastTextureOfTheMmass != 0 {
//						parts = append(parts, fmt.Sprintf("s.textureOfTheMmass = %d", breast.BreastTextureOfTheMmass))
//					}
//					if breast.BreastMobilityOfTheMass != 0 {
//						parts = append(parts, fmt.Sprintf("s.mobilityOfTheMass = %d", breast.BreastMobilityOfTheMass))
//					}
//					if breast.BreastTumorRADSClassification != 0 {
//						parts = append(parts, fmt.Sprintf("s.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
//					}
//					if breast.BreastTNMStagingOfPrimaryTumors != 0 {
//						parts = append(parts, fmt.Sprintf("s.tnmStagingPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
//					}
//					if breast.TNMStagingOfRegionalLymphNodes != 0 {
//						parts = append(parts, fmt.Sprintf("s.tnmStagingRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
//					}
//					if breast.TNMStagingForDistantMetastasis != 0 {
//						parts = append(parts, fmt.Sprintf("s.tnmStagingDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
//					}
//					//使用AND拼接
//					result := strings.Join(parts, " AND ")
//					queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, result, " RETURN DISTINCT  d.diseaseOrSyndrome")
//					fmt.Println("")
//				case "2":
//					//根据症状疾病推荐病种
//					whereClauses := make([]string, 0)
//					selectClauses := make([]string, 0)
//					for _, symn := range symns {
//						characterCypher := fmt.Sprintf(`(d:Disease_or_Syndrome)-[:mainSymptomsRelatedToDiseases]->(%s:Sign_Or_Symptom)`, "s")
//						selectClauses = append(selectClauses, characterCypher)
//						SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%s'`, "s", symn.SignOrSymptom)
//						whereClauses = append(whereClauses, SignOrSymptom)
//					}
//					if len(selectClauses) > 0 {
//						queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")
//					}
//					if len(whereClauses) > 0 {
//						whereTemplate = strings.Join(whereClauses, " AND ")
//					}
//
//					var parts []string
//					if breast.BreastSignOrSymptom != 0 {
//						parts = append(parts, fmt.Sprintf("s.signOrSymptom = %d", breast.BreastSignOrSymptom))
//					}
//					if breast.BreastLocalTemperatureSituation != 0 {
//						parts = append(parts, fmt.Sprintf("s.localTemperatureSituation = %d", breast.BreastLocalTemperatureSituation))
//					}
//					if breast.BreastTextureOfTheMmass != 0 {
//						parts = append(parts, fmt.Sprintf("s.textureOfTheMmass = %d", breast.BreastTextureOfTheMmass))
//					}
//					if breast.BreastMobilityOfTheMass != 0 {
//						parts = append(parts, fmt.Sprintf("s.mobilityOfTheMass = %d", breast.BreastMobilityOfTheMass))
//					}
//					if breast.BreastTumorRADSClassification != 0 {
//						parts = append(parts, fmt.Sprintf("s.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
//					}
//					if breast.BreastTNMStagingOfPrimaryTumors != 0 {
//						parts = append(parts, fmt.Sprintf("s.tnmStagingPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
//					}
//					if breast.TNMStagingOfRegionalLymphNodes != 0 {
//						parts = append(parts, fmt.Sprintf("s.tnmStagingRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
//					}
//					if breast.TNMStagingForDistantMetastasis != 0 {
//						parts = append(parts, fmt.Sprintf("s.tnmStagingDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
//					}
//					//使用AND拼接
//					result := strings.Join(parts, " AND ")
//					queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, result, " RETURN DISTINCT s, d")
//				case "3":
//					//病种推荐量表
//					whereClauses := make([]string, 0)
//					selectClauses := make([]string, 0)
//					for _, symn := range symns {
//						characterCypher := fmt.Sprintf(`(d:Disease_or_Syndrome)-[:diseasesRelatedScaleAssessment]->(%s:Scale_Assessment)`, "sa")
//						selectClauses = append(selectClauses, characterCypher)
//						SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%s'`, "sa", symn.SignOrSymptom)
//						whereClauses = append(whereClauses, SignOrSymptom)
//					}
//					if len(selectClauses) > 0 {
//						queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")
//					}
//					if len(whereClauses) > 0 {
//						whereTemplate = strings.Join(whereClauses, " AND ")
//					}
//
//					var parts []string
//					if breast.BreastSignOrSymptom != 0 {
//						parts = append(parts, fmt.Sprintf("s.signOrSymptom = %d", breast.BreastSignOrSymptom))
//					}
//					if breast.BreastLocalTemperatureSituation != 0 {
//						parts = append(parts, fmt.Sprintf("s.localTemperatureSituation = %d", breast.BreastLocalTemperatureSituation))
//					}
//					if breast.BreastTextureOfTheMmass != 0 {
//						parts = append(parts, fmt.Sprintf("s.textureOfTheMmass = %d", breast.BreastTextureOfTheMmass))
//					}
//					if breast.BreastMobilityOfTheMass != 0 {
//						parts = append(parts, fmt.Sprintf("s.mobilityOfTheMass = %d", breast.BreastMobilityOfTheMass))
//					}
//					if breast.BreastTumorRADSClassification != 0 {
//						parts = append(parts, fmt.Sprintf("s.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
//					}
//					if breast.BreastTNMStagingOfPrimaryTumors != 0 {
//						parts = append(parts, fmt.Sprintf("s.tnmStagingPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
//					}
//					if breast.TNMStagingOfRegionalLymphNodes != 0 {
//						parts = append(parts, fmt.Sprintf("s.tnmStagingRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
//					}
//					if breast.TNMStagingForDistantMetastasis != 0 {
//						parts = append(parts, fmt.Sprintf("s.tnmStagingDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
//					}
//					//使用AND拼接
//					result := strings.Join(parts, " AND ")
//					queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, result, " RETURN DISTINCT sa.scaleAssessment")
//				case "4":
//					//病种推荐西药
//					whereClauses := make([]string, 0)
//					selectClauses := make([]string, 0)
//					for _, symn := range symns {
//						characterCypher := fmt.Sprintf(`(m:Western_Medicine)-[:treat]->(%s:Disease_or_Syndrome)`, "d")
//						selectClauses = append(selectClauses, characterCypher)
//						SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%s'`, "sa", symn.SignOrSymptom)
//						whereClauses = append(whereClauses, SignOrSymptom)
//					}
//					if len(selectClauses) > 0 {
//						queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")
//					}
//					if len(whereClauses) > 0 {
//						whereTemplate = strings.Join(whereClauses, " AND ")
//					}
//
//					var parts []string
//					if breast.BreastSignOrSymptom != 0 {
//						parts = append(parts, fmt.Sprintf("s.signOrSymptom = %d", breast.BreastSignOrSymptom))
//					}
//					if breast.BreastLocalTemperatureSituation != 0 {
//						parts = append(parts, fmt.Sprintf("s.localTemperatureSituation = %d", breast.BreastLocalTemperatureSituation))
//					}
//					if breast.BreastTextureOfTheMmass != 0 {
//						parts = append(parts, fmt.Sprintf("s.textureOfTheMmass = %d", breast.BreastTextureOfTheMmass))
//					}
//					if breast.BreastMobilityOfTheMass != 0 {
//						parts = append(parts, fmt.Sprintf("s.mobilityOfTheMass = %d", breast.BreastMobilityOfTheMass))
//					}
//					if breast.BreastTumorRADSClassification != 0 {
//						parts = append(parts, fmt.Sprintf("s.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
//					}
//					if breast.BreastTNMStagingOfPrimaryTumors != 0 {
//						parts = append(parts, fmt.Sprintf("s.tnmStagingPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
//					}
//					if breast.TNMStagingOfRegionalLymphNodes != 0 {
//						parts = append(parts, fmt.Sprintf("s.tnmStagingRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
//					}
//					if breast.TNMStagingForDistantMetastasis != 0 {
//						parts = append(parts, fmt.Sprintf("s.tnmStagingDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
//					}
//					//使用AND拼接
//					result := strings.Join(parts, " AND ")
//					queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, result, " RETURN DISTINCT m.Western_Medicine")
//				}
//			} else {
//				queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, whereTemplate, " RETURN DISTINCT  d.diseaseOrSyndrome LIMIT 10")
//			}
//			//queryTemplate = "MATCH(d:Disease_or_Syndrome)-[:mainSymptomsRelatedToDiseases]->(s:Sign_Or_Symptom)\tWHERE s.signOrSymptom  = 1787380799578534596 AND s.localTemperatureSituation=1803267889180966436 AND s.textureOfTheMmass=1803183493236915603 AND s.mobilityOfTheMass=1800390904058461069 RETURN DISTINCT d.diseaseOrSyndrome"
//			logging.Info("GetDiseases-1: ", queryTemplate)
//			fmt.Println(queryTemplate)
//			result, err := repository.Runresult(ctx, queryTemplate, nil)
//			if !err {
//				// 如果没有返回结果，返回空的 JSON 对象
//				return "{}"
//			} else {
//				// 如果有记录返回，将 result 转换为 JSON 格式并返回
//
//				logging.Info("result: ", result)
//				var jsonData interface{}
//				err := json.Unmarshal([]byte(result), &jsonData)
//				if err != nil {
//					// 解析失败，返回空的 JSON 对象
//					logging.Info("解析 JSON 数据失败：", err)
//					return "{}"
//				}
//				// 将 JSON 对象转换回 JSON 字符串
//				jsonResult, err := json.Marshal(jsonData)
//				if err != nil {
//					// 转换失败，返回空的 JSON 对象
//					logging.Info("转换 JSON 数据失败：", err)
//					return "{}"
//				}
//
//				return string(jsonResult)
//			}
//		}
//	case 2:
//		{
//			whereClauses := make([]string, 0)
//			selectClauses := make([]string, 0)
//			queryTemplate := ""
//
//			for i, symn := range symns {
//				characterCypher := fmt.Sprintf(`(d:Disease_or_Syndrome)-[:secondarySymptomsRelatedToTheDisease]->(%s:Sign_Or_Symptom)`, "s"+strconv.Itoa(i))
//				selectClauses = append(selectClauses, characterCypher)
//				SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%s'`, "s"+strconv.Itoa(i), symn.SignOrSymptom)
//				whereClauses = append(whereClauses, SignOrSymptom)
//			}
//
//			if len(selectClauses) > 0 {
//				queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")
//
//			}
//			if len(whereClauses) > 0 {
//				whereTemplate = strings.Join(whereClauses, " AND ")
//
//			}
//			queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, whereTemplate, " RETURN DISTINCT  d.diseaseOrSyndrome LIMIT 10")
//			logging.Info("GetDiseases-2: ", queryTemplate)
//			result, err := repository.Runresult(ctx, queryTemplate, nil)
//			if !err {
//				// 如果没有返回结果，返回空的 JSON 对象
//				return "{}"
//			} else {
//				// 如果有记录返回，将 result 转换为 JSON 格式并返回
//
//				logging.Info("result: ", result)
//				var jsonData interface{}
//				err := json.Unmarshal([]byte(result), &jsonData)
//				if err != nil {
//					// 解析失败，返回空的 JSON 对象
//					logging.Info("解析 JSON 数据失败：", err)
//					return "{}"
//				}
//				// 将 JSON 对象转换回 JSON 字符串
//				jsonResult, err := json.Marshal(jsonData)
//				if err != nil {
//					// 转换失败，返回空的 JSON 对象
//					logging.Info("转换 JSON 数据失败：", err)
//					return "{}"
//				}
//
//				return string(jsonResult)
//			}
//		}
//	default:
//		return "{}"
//	}
//}
func GetDiseases(symns []Simplesymn, flag int) string {
	ctx := context.Background()
	queryTemplate := ""
	whereTemplate := ""
	switch flag {
	case 1:
		{
			whereClauses := make([]string, 0)
			selectClauses := make([]string, 0)
			for i, symn := range symns {
				characterCypher := fmt.Sprintf(`(d:Disease_or_Syndrome)-[:mainSymptomsRelatedToDiseases]->(%s:Sign_Or_Symptom)`, "s"+strconv.Itoa(i))
				selectClauses = append(selectClauses, characterCypher)
				SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%s'`, "s"+strconv.Itoa(i), symn.SignOrSymptom)
				whereClauses = append(whereClauses, SignOrSymptom)
			}

			if len(selectClauses) > 0 {
				queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")

			}
			if len(whereClauses) > 0 {
				whereTemplate = strings.Join(whereClauses, " AND ")

			}
			queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, whereTemplate, " RETURN DISTINCT  d.diseaseOrSyndrome LIMIT 10")
			logging.Info("GetDiseases-1: ", queryTemplate)
			fmt.Println(queryTemplate)
			result, err := repository.Runresult(ctx, queryTemplate, nil)
			if !err {
				// 如果没有返回结果，返回空的 JSON 对象
				return "{}"
			} else {
				// 如果有记录返回，将 result 转换为 JSON 格式并返回

				logging.Info("result: ", result)
				var jsonData interface{}
				err := json.Unmarshal([]byte(result), &jsonData)
				if err != nil {
					// 解析失败，返回空的 JSON 对象
					logging.Info("解析 JSON 数据失败：", err)
					return "{}"
				}
				// 将 JSON 对象转换回 JSON 字符串
				jsonResult, err := json.Marshal(jsonData)
				if err != nil {
					// 转换失败，返回空的 JSON 对象
					logging.Info("转换 JSON 数据失败：", err)
					return "{}"
				}

				return string(jsonResult)
			}
		}
	case 2:
		{
			whereClauses := make([]string, 0)
			selectClauses := make([]string, 0)
			queryTemplate := ""

			for i, symn := range symns {
				characterCypher := fmt.Sprintf(`(d:Disease_or_Syndrome)-[:secondarySymptomsRelatedToTheDisease]->(%s:Sign_Or_Symptom)`, "s"+strconv.Itoa(i))
				selectClauses = append(selectClauses, characterCypher)
				SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%s'`, "s"+strconv.Itoa(i), symn.SignOrSymptom)
				whereClauses = append(whereClauses, SignOrSymptom)
			}

			if len(selectClauses) > 0 {
				queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")

			}
			if len(whereClauses) > 0 {
				whereTemplate = strings.Join(whereClauses, " AND ")

			}
			queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, whereTemplate, " RETURN DISTINCT  d.diseaseOrSyndrome LIMIT 10")
			logging.Info("GetDiseases-2: ", queryTemplate)
			result, err := repository.Runresult(ctx, queryTemplate, nil)
			if !err {
				// 如果没有返回结果，返回空的 JSON 对象
				return "{}"
			} else {
				// 如果有记录返回，将 result 转换为 JSON 格式并返回

				logging.Info("result: ", result)
				var jsonData interface{}
				err := json.Unmarshal([]byte(result), &jsonData)
				if err != nil {
					// 解析失败，返回空的 JSON 对象
					logging.Info("解析 JSON 数据失败：", err)
					return "{}"
				}
				// 将 JSON 对象转换回 JSON 字符串
				jsonResult, err := json.Marshal(jsonData)
				if err != nil {
					// 转换失败，返回空的 JSON 对象
					logging.Info("转换 JSON 数据失败：", err)
					return "{}"
				}

				return string(jsonResult)
			}
		}
	default:
		return "{}"
	}
}

/*
下面是新增加的内容，为精神专科知识图谱提供推理
*/
// 根据乳腺症状查询诊断 flag 1:主要症状 2 次要症状
//乳腺专科只取soid
type Item struct {
	Keys   []string `json:"Keys"`
	Values []int64  `json:"Values"`
}

func GetBreastDiseases(symns []Simplesymn, flag int, breastlist []BreastParam, breastType string) string {
	ctx := context.Background()
	queryTemplate := ""
	whereTemplate := ""
	switch flag {
	case 1:
		{
			switch breastType {
			case "1":
				whereClauses := make([]string, 0)
				selectClauses := make([]string, 0)
				resultlist := make([]string, 0) //用来存储结果的列表
				for _, breast := range breastlist {
					characterCypher := fmt.Sprintf(`(d:Disease_or_Syndrome)-[:mainSymptomsRelatedToDiseases]->(%s:Sign_Or_Symptom)`, "s")
					selectClauses = append(selectClauses, characterCypher)
					SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%d'`, "s", breast.BreastSignOrSymptom)
					whereClauses = append(whereClauses, SignOrSymptom)
					if len(selectClauses) > 0 {
						queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")
					}
					if len(whereClauses) > 0 {
						whereTemplate = strings.Join(whereClauses, " AND ")
					}
					queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, whereTemplate, "RETURN DISTINCT d.diseaseOrSyndrome")
					result, err := repository.Runresult(ctx, queryTemplate, nil)
					if !err {
						return "{}"
					} else {
						// 定义对象结构
						type Item struct {
							Values []int64  `json:"Values"`
							Keys   []string `json:"Keys"`
							Names  string   `json:"Names"` // 新增的Names字段，omitempty表示当值为空时不进行序列化
							//Names  string   `json:"Names,omitempty"` // 新增的Names字段，omitempty表示当值为空时不进行序列化
						}
						var data []Item
						err := json.Unmarshal([]byte(result), &data)
						if err != nil {
							fmt.Println("Error parsing JSON:", err)
							return ""
						}
						// 添加新的Names字段
						for i, v := range data {
							//查询soid对应的Name
							str := strconv.FormatInt(v.Values[0], 10)
							name, _ := utils.OtherGetNameBySoid(str)
							data[i].Names = name // 随机生成长度为10的字符串
						}
						// 转换回JSON字符串
						newJSON, err := json.Marshal(data)
						if err != nil {
							fmt.Println("Error marshalling JSON:", err)
							return ""
						}

						// 将新生成的JSON字节数组转换为字符串并打印
						newJSONString := string(newJSON)
						return newJSONString
					}
				}
				resultstr := strings.Join(resultlist, ", ")
				return resultstr
			case "2":
				whereClauses := make([]string, 0)
				selectClauses := make([]string, 0)
				//resultlist := make([]string, 0) //用来存储结果的列表
				//var resultstr string            //最终返回的字符
				var dataall []Itemd
				var fliterlistL []Itemd
				for _, breast := range breastlist {
					characterCypher := fmt.Sprintf(`(d:Disease_or_Syndrome)-[:mainSymptomsRelatedToDiseases]->(%s:Sign_Or_Symptom)`, "s")
					selectClauses = append(selectClauses, characterCypher)
					//拼接where语句
					var parts []string
					if breast.BreastSignOrSymptom != 0 {
						parts = append(parts, fmt.Sprintf("s.signOrSymptom = %d", breast.BreastSignOrSymptom))
					}
					if breast.BreastDiseaseOrSyndrome != 0 {
						parts = append(parts, fmt.Sprintf("s.diseaseOrSymptom = %d", breast.BreastDiseaseOrSyndrome))
					}
					if breast.BreastLocalTemperatureSituation != 0 {
						parts = append(parts, fmt.Sprintf("s.localTemperatureSituation = %d", breast.BreastLocalTemperatureSituation))
					}
					if breast.BreastTextureOfTheMmass != 0 {
						parts = append(parts, fmt.Sprintf("s.textureOfTheMmass = %d", breast.BreastTextureOfTheMmass))
					}
					if breast.BreastMobilityOfTheMass != 0 {
						parts = append(parts, fmt.Sprintf("s.mobilityOfTheMass = %d", breast.BreastMobilityOfTheMass))
					}
					if breast.BreastTumorRADSClassification != 0 {
						parts = append(parts, fmt.Sprintf("d.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
					}
					if breast.BreastTNMStagingOfPrimaryTumors != 0 {
						parts = append(parts, fmt.Sprintf("d.tnmStagingPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
					}
					if breast.TNMStagingOfRegionalLymphNodes != 0 {
						parts = append(parts, fmt.Sprintf("d.tnmStagingRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
					}
					if breast.TNMStagingForDistantMetastasis != 0 {
						parts = append(parts, fmt.Sprintf("d.tnmStagingDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
					}
					//使用AND拼接
					resurnresult := strings.Join(parts, " AND ")
					SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%d'`, "s", breast.BreastSignOrSymptom)
					whereClauses = append(whereClauses, SignOrSymptom)
					if len(selectClauses) > 0 {
						queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")
					}
					if len(whereClauses) > 0 {
						whereTemplate = strings.Join(whereClauses, " AND ")
					}
					queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, resurnresult, "RETURN DISTINCT d.diseaseOrSyndrome")
					result, err := repository.Runresult(ctx, queryTemplate, nil)
					//result := `[{"Values":[1803351230037882739],"Keys":["li.instanceCode"]},{"Values":[1803351230037882740],"Keys":["li.instanceCode"]},{"Values":[1803351230037882741],"Keys":["li.instanceCode"]},{"Values":[1803351230037882742],"Keys":["li.instanceCode"]}]`
					//err := true
					if !err {
						return "{}"
					} else {
						var data []Item
						err := json.Unmarshal([]byte(result), &data)
						if err != nil {
							fmt.Println("Error parsing JSON:", err)
							return ""
						}
						// 构建转换后的数据结构 []Itemstr
						var itemsStr []Itemd
						for _, item := range data {
							var valuesStr []string
							for _, v := range item.Values {
								valuesStr = append(valuesStr, strconv.FormatInt(v, 10))
							}
							itemsStr = append(itemsStr, Itemd{
								Keys:   item.Keys,
								Values: valuesStr,
							})
						}
						// 添加新键值对
						for i, v := range itemsStr {
							name, _ := utils.OtherGetNameBySoid(v.Values[0])
							itemsStr[i].Values = append(itemsStr[i].Values, name)
							itemsStr[i].Keys = append(itemsStr[i].Keys, "name")
						}
						dataall = append(dataall, itemsStr...)
						fliterlistL = removeDuplicates(dataall)
					}
				}
				resultstrr, err := json.Marshal(fliterlistL)
				if err != nil {
					fmt.Println("Error marshalling JSON:", err)
					return ""
				}
				eee := string(resultstrr)
				return eee
			case "3": //根据症状、疾病属性确定推荐病种（eg.RADS分级、TNM分期）
				whereClauses := make([]string, 0)
				selectClauses := make([]string, 0)
				//resultlist := make([]string, 0) //用来存储结果的列表
				//var resultstr string            //最终返回的字符
				var dataall []Itemd
				var fliterlistL []Itemd
				for _, breast := range breastlist {
					characterCypher := fmt.Sprintf(`MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedScaleAssessment]->(sa:Scale_Assessment)`)
					selectClauses = append(selectClauses, characterCypher)
					//拼接where语句
					var parts []string
					if breast.BreastSignOrSymptom != 0 {
						parts = append(parts, fmt.Sprintf("d.signOrSymptom = %d", breast.BreastSignOrSymptom))
					}
					if breast.BreastDiseaseOrSyndrome != 0 {
						parts = append(parts, fmt.Sprintf("d.diseaseOrSyndrome = %d", breast.BreastDiseaseOrSyndrome))
					}
					if breast.BreastLocalTemperatureSituation != 0 {
						parts = append(parts, fmt.Sprintf("d.localTemperatureSituation = %d", breast.BreastLocalTemperatureSituation))
					}
					if breast.BreastTextureOfTheMmass != 0 {
						parts = append(parts, fmt.Sprintf("d.textureOfTheMmass = %d", breast.BreastTextureOfTheMmass))
					}
					if breast.BreastMobilityOfTheMass != 0 {
						parts = append(parts, fmt.Sprintf("d.mobilityOfTheMass = %d", breast.BreastMobilityOfTheMass))
					}
					if breast.BreastTumorRADSClassification != 0 {
						parts = append(parts, fmt.Sprintf("d.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
					}
					if breast.BreastTNMStagingOfPrimaryTumors != 0 {
						parts = append(parts, fmt.Sprintf("d.TNMStagingOfPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
					}
					if breast.TNMStagingOfRegionalLymphNodes != 0 {
						parts = append(parts, fmt.Sprintf("d.TNMStagingOfRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
					}
					if breast.TNMStagingForDistantMetastasis != 0 {
						parts = append(parts, fmt.Sprintf("d.TNMStagingForDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
					}
					//使用AND拼接
					resurnresult := strings.Join(parts, " AND ")
					SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%d'`, "s", breast.BreastSignOrSymptom)
					whereClauses = append(whereClauses, SignOrSymptom)
					if len(selectClauses) > 0 {
						queryTemplate = strings.Join(selectClauses, " , ")
					}
					if len(whereClauses) > 0 {
						whereTemplate = strings.Join(whereClauses, " AND ")
					}
					queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, resurnresult, "RETURN DISTINCT sa.scaleAssessment")
					result, err := repository.Runresult(ctx, queryTemplate, nil)
					//result := `[{"Values":[1803351230037882739],"Keys":["li.instanceCode"]},{"Values":[1803351230037882740],"Keys":["li.instanceCode"]},{"Values":[1803351230037882741],"Keys":["li.instanceCode"]},{"Values":[1803351230037882742],"Keys":["li.instanceCode"]}]`
					//err := true
					if !err {
						return "{}"
					} else {
						var data []Item
						err := json.Unmarshal([]byte(result), &data)
						if err != nil {
							fmt.Println("Error parsing JSON:", err)
							return ""
						}
						// 构建转换后的数据结构 []Itemstr
						var itemsStr []Itemd
						for _, item := range data {
							var valuesStr []string
							for _, v := range item.Values {
								valuesStr = append(valuesStr, strconv.FormatInt(v, 10))
							}
							itemsStr = append(itemsStr, Itemd{
								Keys:   item.Keys,
								Values: valuesStr,
							})
						}
						// 添加新键值对
						for i, v := range itemsStr {
							name, _ := utils.CheckTreatServiceName(v.Values[0])
							itemsStr[i].Values = append(itemsStr[i].Values, name)
							itemsStr[i].Keys = append(itemsStr[i].Keys, "name")
						}
						dataall = append(dataall, itemsStr...)
						fliterlistL = removeDuplicates(dataall)
					}
				}
				resultstrr, err := json.Marshal(fliterlistL)
				if err != nil {
					fmt.Println("Error marshalling JSON:", err)
					return ""
				}
				eee := string(resultstrr)
				return eee
			}

			whereClauses := make([]string, 0)
			selectClauses := make([]string, 0)
			for i, symn := range symns {
				characterCypher := fmt.Sprintf(`(d:Disease_or_Syndrome)-[:mainSymptomsRelatedToDiseases]->(%s:Sign_Or_Symptom)`, "s"+strconv.Itoa(i))
				selectClauses = append(selectClauses, characterCypher)
				SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%s'`, "s"+strconv.Itoa(i), symn.SignOrSymptom)
				whereClauses = append(whereClauses, SignOrSymptom)
			}

			if len(selectClauses) > 0 {
				queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")

			}
			if len(whereClauses) > 0 {
				whereTemplate = strings.Join(whereClauses, " AND ")

			}
			//判断乳腺专科是否有参数传过来，false就是有不为空的
			//sign := false
			//if sign == false {
			//	switch breastType {
			//	case "1":
			//		//查病种
			//		whereClauses := make([]string, 0)
			//		selectClauses := make([]string, 0)
			//		for _, symn := range symns {
			//			characterCypher := fmt.Sprintf(`(d:Disease_or_Syndrome)-[:mainSymptomsRelatedToDiseases]->(%s:Sign_Or_Symptom)`, "s")
			//			selectClauses = append(selectClauses, characterCypher)
			//			SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%s'`, "s", symn.SignOrSymptom)
			//			whereClauses = append(whereClauses, SignOrSymptom)
			//		}
			//		if len(selectClauses) > 0 {
			//			queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")
			//		}
			//		if len(whereClauses) > 0 {
			//			whereTemplate = strings.Join(whereClauses, " AND ")
			//		}
			//
			//		var parts []string
			//		if breast.BreastSignOrSymptom != 0 {
			//			parts = append(parts, fmt.Sprintf("s.signOrSymptom = %d", breast.BreastSignOrSymptom))
			//		}
			//		if breast.BreastLocalTemperatureSituation != 0 {
			//			parts = append(parts, fmt.Sprintf("s.localTemperatureSituation = %d", breast.BreastLocalTemperatureSituation))
			//		}
			//		if breast.BreastTextureOfTheMmass != 0 {
			//			parts = append(parts, fmt.Sprintf("s.textureOfTheMmass = %d", breast.BreastTextureOfTheMmass))
			//		}
			//		if breast.BreastMobilityOfTheMass != 0 {
			//			parts = append(parts, fmt.Sprintf("s.mobilityOfTheMass = %d", breast.BreastMobilityOfTheMass))
			//		}
			//		if breast.BreastTumorRADSClassification != 0 {
			//			parts = append(parts, fmt.Sprintf("s.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
			//		}
			//		if breast.BreastTNMStagingOfPrimaryTumors != 0 {
			//			parts = append(parts, fmt.Sprintf("s.tnmStagingPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
			//		}
			//		if breast.TNMStagingOfRegionalLymphNodes != 0 {
			//			parts = append(parts, fmt.Sprintf("s.tnmStagingRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
			//		}
			//		if breast.TNMStagingForDistantMetastasis != 0 {
			//			parts = append(parts, fmt.Sprintf("s.tnmStagingDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
			//		}
			//		//使用AND拼接
			//		result := strings.Join(parts, " AND ")
			//		queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, result, " RETURN DISTINCT  d.diseaseOrSyndrome")
			//		fmt.Println("")
			//	case "2":
			//		//根据症状疾病推荐病种
			//		whereClauses := make([]string, 0)
			//		selectClauses := make([]string, 0)
			//		for _, symn := range symns {
			//			characterCypher := fmt.Sprintf(`(d:Disease_or_Syndrome)-[:mainSymptomsRelatedToDiseases]->(%s:Sign_Or_Symptom)`, "s")
			//			selectClauses = append(selectClauses, characterCypher)
			//			SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%s'`, "s", symn.SignOrSymptom)
			//			whereClauses = append(whereClauses, SignOrSymptom)
			//		}
			//		if len(selectClauses) > 0 {
			//			queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")
			//		}
			//		if len(whereClauses) > 0 {
			//			whereTemplate = strings.Join(whereClauses, " AND ")
			//		}
			//
			//		var parts []string
			//		if breast.BreastSignOrSymptom != 0 {
			//			parts = append(parts, fmt.Sprintf("s.signOrSymptom = %d", breast.BreastSignOrSymptom))
			//		}
			//		if breast.BreastLocalTemperatureSituation != 0 {
			//			parts = append(parts, fmt.Sprintf("s.localTemperatureSituation = %d", breast.BreastLocalTemperatureSituation))
			//		}
			//		if breast.BreastTextureOfTheMmass != 0 {
			//			parts = append(parts, fmt.Sprintf("s.textureOfTheMmass = %d", breast.BreastTextureOfTheMmass))
			//		}
			//		if breast.BreastMobilityOfTheMass != 0 {
			//			parts = append(parts, fmt.Sprintf("s.mobilityOfTheMass = %d", breast.BreastMobilityOfTheMass))
			//		}
			//		if breast.BreastTumorRADSClassification != 0 {
			//			parts = append(parts, fmt.Sprintf("s.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
			//		}
			//		if breast.BreastTNMStagingOfPrimaryTumors != 0 {
			//			parts = append(parts, fmt.Sprintf("s.tnmStagingPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
			//		}
			//		if breast.TNMStagingOfRegionalLymphNodes != 0 {
			//			parts = append(parts, fmt.Sprintf("s.tnmStagingRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
			//		}
			//		if breast.TNMStagingForDistantMetastasis != 0 {
			//			parts = append(parts, fmt.Sprintf("s.tnmStagingDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
			//		}
			//		//使用AND拼接
			//		result := strings.Join(parts, " AND ")
			//		queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, result, " RETURN DISTINCT s, d")
			//	case "3":
			//		//病种推荐量表
			//		whereClauses := make([]string, 0)
			//		selectClauses := make([]string, 0)
			//		for _, symn := range symns {
			//			characterCypher := fmt.Sprintf(`(d:Disease_or_Syndrome)-[:diseasesRelatedScaleAssessment]->(%s:Scale_Assessment)`, "sa")
			//			selectClauses = append(selectClauses, characterCypher)
			//			SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%s'`, "sa", symn.SignOrSymptom)
			//			whereClauses = append(whereClauses, SignOrSymptom)
			//		}
			//		if len(selectClauses) > 0 {
			//			queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")
			//		}
			//		if len(whereClauses) > 0 {
			//			whereTemplate = strings.Join(whereClauses, " AND ")
			//		}
			//
			//		var parts []string
			//		if breast.BreastSignOrSymptom != 0 {
			//			parts = append(parts, fmt.Sprintf("s.signOrSymptom = %d", breast.BreastSignOrSymptom))
			//		}
			//		if breast.BreastLocalTemperatureSituation != 0 {
			//			parts = append(parts, fmt.Sprintf("s.localTemperatureSituation = %d", breast.BreastLocalTemperatureSituation))
			//		}
			//		if breast.BreastTextureOfTheMmass != 0 {
			//			parts = append(parts, fmt.Sprintf("s.textureOfTheMmass = %d", breast.BreastTextureOfTheMmass))
			//		}
			//		if breast.BreastMobilityOfTheMass != 0 {
			//			parts = append(parts, fmt.Sprintf("s.mobilityOfTheMass = %d", breast.BreastMobilityOfTheMass))
			//		}
			//		if breast.BreastTumorRADSClassification != 0 {
			//			parts = append(parts, fmt.Sprintf("s.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
			//		}
			//		if breast.BreastTNMStagingOfPrimaryTumors != 0 {
			//			parts = append(parts, fmt.Sprintf("s.tnmStagingPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
			//		}
			//		if breast.TNMStagingOfRegionalLymphNodes != 0 {
			//			parts = append(parts, fmt.Sprintf("s.tnmStagingRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
			//		}
			//		if breast.TNMStagingForDistantMetastasis != 0 {
			//			parts = append(parts, fmt.Sprintf("s.tnmStagingDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
			//		}
			//		//使用AND拼接
			//		result := strings.Join(parts, " AND ")
			//		queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, result, " RETURN DISTINCT sa.scaleAssessment")
			//	case "4":
			//		//病种推荐西药
			//		whereClauses := make([]string, 0)
			//		selectClauses := make([]string, 0)
			//		for _, symn := range symns {
			//			characterCypher := fmt.Sprintf(`(m:Western_Medicine)-[:treat]->(%s:Disease_or_Syndrome)`, "d")
			//			selectClauses = append(selectClauses, characterCypher)
			//			SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%s'`, "sa", symn.SignOrSymptom)
			//			whereClauses = append(whereClauses, SignOrSymptom)
			//		}
			//		if len(selectClauses) > 0 {
			//			queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")
			//		}
			//		if len(whereClauses) > 0 {
			//			whereTemplate = strings.Join(whereClauses, " AND ")
			//		}
			//
			//		var parts []string
			//		if breast.BreastSignOrSymptom != 0 {
			//			parts = append(parts, fmt.Sprintf("s.signOrSymptom = %d", breast.BreastSignOrSymptom))
			//		}
			//		if breast.BreastLocalTemperatureSituation != 0 {
			//			parts = append(parts, fmt.Sprintf("s.localTemperatureSituation = %d", breast.BreastLocalTemperatureSituation))
			//		}
			//		if breast.BreastTextureOfTheMmass != 0 {
			//			parts = append(parts, fmt.Sprintf("s.textureOfTheMmass = %d", breast.BreastTextureOfTheMmass))
			//		}
			//		if breast.BreastMobilityOfTheMass != 0 {
			//			parts = append(parts, fmt.Sprintf("s.mobilityOfTheMass = %d", breast.BreastMobilityOfTheMass))
			//		}
			//		if breast.BreastTumorRADSClassification != 0 {
			//			parts = append(parts, fmt.Sprintf("s.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
			//		}
			//		if breast.BreastTNMStagingOfPrimaryTumors != 0 {
			//			parts = append(parts, fmt.Sprintf("s.tnmStagingPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
			//		}
			//		if breast.TNMStagingOfRegionalLymphNodes != 0 {
			//			parts = append(parts, fmt.Sprintf("s.tnmStagingRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
			//		}
			//		if breast.TNMStagingForDistantMetastasis != 0 {
			//			parts = append(parts, fmt.Sprintf("s.tnmStagingDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
			//		}
			//		//使用AND拼接
			//		result := strings.Join(parts, " AND ")
			//		queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, result, " RETURN DISTINCT m.Western_Medicine")
			//	}
			//} else {
			//	queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, whereTemplate, " RETURN DISTINCT  d.diseaseOrSyndrome LIMIT 10")
			//}
			queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, whereTemplate, " RETURN DISTINCT  d.diseaseOrSyndrome LIMIT 10")
			//queryTemplate = "MATCH(d:Disease_or_Syndrome)-[:mainSymptomsRelatedToDiseases]->(s:Sign_Or_Symptom)\tWHERE s.signOrSymptom  = 1787380799578534596 AND s.localTemperatureSituation=1803267889180966436 AND s.textureOfTheMmass=1803183493236915603 AND s.mobilityOfTheMass=1800390904058461069 RETURN DISTINCT d.diseaseOrSyndrome"
			logging.Info("GetDiseases-1: ", queryTemplate)
			fmt.Println(queryTemplate)
			result, err := repository.Runresult(ctx, queryTemplate, nil)
			if !err {
				// 如果没有返回结果，返回空的 JSON 对象
				return "{}"
			} else {
				// 如果有记录返回，将 result 转换为 JSON 格式并返回

				logging.Info("result: ", result)
				var jsonData interface{}
				err := json.Unmarshal([]byte(result), &jsonData)
				if err != nil {
					// 解析失败，返回空的 JSON 对象
					logging.Info("解析 JSON 数据失败：", err)
					return "{}"
				}
				// 将 JSON 对象转换回 JSON 字符串
				jsonResult, err := json.Marshal(jsonData)
				if err != nil {
					// 转换失败，返回空的 JSON 对象
					logging.Info("转换 JSON 数据失败：", err)
					return "{}"
				}

				return string(jsonResult)
			}
		}
	case 2:
		{
			whereClauses := make([]string, 0)
			selectClauses := make([]string, 0)
			queryTemplate := ""

			for i, symn := range symns {
				characterCypher := fmt.Sprintf(`(d:Disease_or_Syndrome)-[:secondarySymptomsRelatedToTheDisease]->(%s:Sign_Or_Symptom)`, "s"+strconv.Itoa(i))
				selectClauses = append(selectClauses, characterCypher)
				SignOrSymptom := fmt.Sprintf(` %s.signOrSymptom = '%s'`, "s"+strconv.Itoa(i), symn.SignOrSymptom)
				whereClauses = append(whereClauses, SignOrSymptom)
			}

			if len(selectClauses) > 0 {
				queryTemplate = "MATCH" + strings.Join(selectClauses, " , ")

			}
			if len(whereClauses) > 0 {
				whereTemplate = strings.Join(whereClauses, " AND ")

			}
			queryTemplate = fmt.Sprintf("%s WHERE %s %s", queryTemplate, whereTemplate, " RETURN DISTINCT  d.diseaseOrSyndrome LIMIT 10")
			logging.Info("GetDiseases-2: ", queryTemplate)
			result, err := repository.Runresult(ctx, queryTemplate, nil)
			if !err {
				// 如果没有返回结果，返回空的 JSON 对象
				return "{}"
			} else {
				// 如果有记录返回，将 result 转换为 JSON 格式并返回

				logging.Info("result: ", result)
				var jsonData interface{}
				err := json.Unmarshal([]byte(result), &jsonData)
				if err != nil {
					// 解析失败，返回空的 JSON 对象
					logging.Info("解析 JSON 数据失败：", err)
					return "{}"
				}
				// 将 JSON 对象转换回 JSON 字符串
				jsonResult, err := json.Marshal(jsonData)
				if err != nil {
					// 转换失败，返回空的 JSON 对象
					logging.Info("转换 JSON 数据失败：", err)
					return "{}"
				}

				return string(jsonResult)
			}
		}
	default:
		return "{}"
	}
}

// 判断乳腺专科的入参是否为空
func isEmptyStats(stats BreastParam) bool {
	return stats.BreastSignOrSymptom == 0 &&
		stats.BreastDiseaseOrSyndrome == 0 &&
		stats.BreastMobilityOfTheMass == 0 &&
		stats.BreastLocalTemperatureSituation == 0 &&
		stats.BreastTextureOfTheMmass == 0 &&
		stats.BreastTumorRADSClassification == 0 &&
		stats.BreastTNMStagingOfPrimaryTumors == 0 &&
		stats.TNMStagingOfRegionalLymphNodes == 0 &&
		stats.TNMStagingForDistantMetastasis == 0
}

// 根据分组和症状查询量表
func GetTables(diseasegroup string, symns []Simplesymn) string {
	ctx := context.Background()
	queryTemplate := ""
	characterCypher := fmt.Sprintf(`MATCH(ps:Psychologic_Rating_Scale)-[:scaleApplyToTheClinicalFinding]->(s:Sign_Or_Symptom),(ps)-[:scaleApplyToTheClinicalFinding]->(c:Disease_Characteristics)`)
	var values []string
	for _, symn := range symns {
		values = append(values, fmt.Sprintf("'%s'", symn.SignOrSymptom))
	}
	inClause := fmt.Sprintf("s.signOrSymptom IN [%s]", strings.Join(values, ","))
	agegrop := fmt.Sprintf("c.ageGroup = '%s'", diseasegroup)
	queryTemplate = fmt.Sprintf("%s WHERE %s AND %s %s", characterCypher, agegrop, inClause, " RETURN DISTINCT ps.typesOfScales ")
	logging.Info("GetTables: ", queryTemplate)
	result, err := repository.Runresult(ctx, queryTemplate, nil)
	if !err {
		// 如果没有返回结果，返回空的 JSON 对象
		return "{}"
	} else {
		// 如果有记录返回，将 result 转换为 JSON 格式并返回

		logging.Info("result: ", result)
		var jsonData interface{}
		err := json.Unmarshal([]byte(result), &jsonData)
		if err != nil {
			// 解析失败，返回空的 JSON 对象
			logging.Info("解析 JSON 数据失败：", err)
			return "{}"
		}
		// 将 JSON 对象转换回 JSON 字符串
		jsonResult, err := json.Marshal(jsonData)
		if err != nil {
			// 转换失败，返回空的 JSON 对象
			logging.Info("转换 JSON 数据失败：", err)
			return "{}"
		}

		return string(jsonResult)
	}
}

// 根据分组和疾病查询表
func GetTablesByDis(diseasegroup string, values []string) string {
	//start := time.Now()
	whereClauses := make([]string, 0)
	selectClauses := make([]string, 0)
	ctx := context.Background()
	queryTemplate := ""
	tempwhere := ""
	tempselect := ""
	characterCypher := fmt.Sprintf(`MATCH(ps:Psychologic_Rating_Scale)-[:scaleApplyToTheClinicalFinding]->(c:Disease_Characteristics) `)
	selectClauses = append(selectClauses, characterCypher)
	agegrop := fmt.Sprintf("c.ageGroup = '%s'", diseasegroup)
	for i, value := range values {
		// 在这里处理每个值 value
		tempselect = fmt.Sprintf(`(%s:Disease_or_Syndrome)-[:diseasesRelatedScaleAssessment]->(sa:Scale_Assessment)-[:use]->(ps)`, "d"+strconv.Itoa(i))
		selectClauses = append(selectClauses, tempselect)
		tempwhere = fmt.Sprintf(`%s.diseaseOrSyndrome = %s`, "d"+strconv.Itoa(i), value)
		whereClauses = append(whereClauses, tempwhere)
	}
	inClause := fmt.Sprintf("%s", strings.Join(whereClauses, ` or `))
	select1 := fmt.Sprintf("%s", strings.Join(selectClauses, `,`))
	queryTemplate = fmt.Sprintf(" %s WHERE %s AND  ( %s )  %s", select1, agegrop, inClause, " RETURN DISTINCT ps.typesOfScales ")
	logging.Info("GetTablesByDis: ", queryTemplate)
	//elapsed := time.Since(start)
	//fmt.Printf("GetTablesByDis执行代码消耗时间-语句拼接：%s\n", elapsed)
	result, err := repository.Runresult(ctx, queryTemplate, nil)
	//elapsed = time.Since(start)
	//fmt.Printf("GetTablesByDis执行代码消耗时间-执行查询时间：%s\n", elapsed)
	if !err {
		// 如果没有返回结果，返回空的 JSON 对象
		return "{}"
	} else {
		// 如果有记录返回，将 result 转换为 JSON 格式并返回

		logging.Info("result: ", result)
		var jsonData interface{}
		err := json.Unmarshal([]byte(result), &jsonData)
		if err != nil {
			// 解析失败，返回空的 JSON 对象
			logging.Info("解析 JSON 数据失败：", err)
			return "{}"
		}
		// 将 JSON 对象转换回 JSON 字符串
		jsonResult, err := json.Marshal(jsonData)
		if err != nil {
			// 转换失败，返回空的 JSON 对象
			logging.Info("转换 JSON 数据失败：", err)
			return "{}"
		}

		return string(jsonResult)
	}
}
func GettcmBydiseaseGroup(diseasegroup []Simplesymn) string {
	ctx := context.Background()
	queryTemplate := ""
	characterCypher := fmt.Sprintf(`MATCH(d:Disease_or_Syndrome) `)
	var values []string
	for _, symn := range diseasegroup {
		values = append(values, fmt.Sprintf("'%s'", symn.SignOrSymptom))
	}
	inClause := fmt.Sprintf("d.diseaseOrSyndrome IN [%s]", strings.Join(values, ","))
	queryTemplate = fmt.Sprintf("%s WHERE %s %s ", characterCypher, inClause, " RETURN DISTINCT d.tcmDisease  ")
	logging.Info("GettcmBydiseaseGroup: ", queryTemplate)
	result, err := repository.Runresult(ctx, queryTemplate, nil)
	if !err {
		// 如果没有返回结果，返回空的 JSON 对象
		return "{}"
	} else {
		// 如果有记录返回，将 result 转换为 JSON 格式并返回

		logging.Info("result: ", result)
		var jsonData interface{}
		err := json.Unmarshal([]byte(result), &jsonData)
		if err != nil {
			// 解析失败，返回空的 JSON 对象
			logging.Info("解析 JSON 数据失败：", err)
			return "{}"
		}
		// 将 JSON 对象转换回 JSON 字符串
		jsonResult, err := json.Marshal(jsonData)
		if err != nil {
			// 转换失败，返回空的 JSON 对象
			logging.Info("转换 JSON 数据失败：", err)
			return "{}"
		}

		return string(jsonResult)
	}
}

// 根据中医疾病获取证候
func GettcmSymBytcmDis(inClause string) string {
	ctx := context.Background()
	queryTemplate := ""
	characterCypher := fmt.Sprintf(`MATCH(d)-[:diseasesRelatedSyndrome]->(ts:TCM_Syndrome) `)
	inClause1 := fmt.Sprintf("d.tcmDisease IN %s", inClause)
	queryTemplate = fmt.Sprintf("%s WHERE %s  %s", characterCypher, inClause1, " RETURN DISTINCT ts.tcmSyndrome ")
	logging.Info("GettcmSymBytcmDis: ", queryTemplate)
	result, err := repository.Runresult(ctx, queryTemplate, nil)
	if !err {
		// 如果没有返回结果，返回空的 JSON 对象
		return "{}"
	} else {
		// 如果有记录返回，将 result 转换为 JSON 格式并返回
		logging.Info("result: ", result)
		var jsonData interface{}
		err := json.Unmarshal([]byte(result), &jsonData)
		if err != nil {
			// 解析失败，返回空的 JSON 对象
			logging.Info("解析 JSON 数据失败：", err)
			return "{}"
		}
		// 将 JSON 对象转换回 JSON 字符串
		jsonResult, err := json.Marshal(jsonData)
		if err != nil {
			// 转换失败，返回空的 JSON 对象
			logging.Info("转换 JSON 数据失败：", err)
			return "{}"
		}
		return string(jsonResult)
	}
}

func GetRecomendation(diseaseOrSyndrome string, flag int, breast BreastParam) interface{} {
	ctx := context.Background()
	queryTemplate := ""
	inClause1 := ""
	retstr := ""
	characterCypher := ""
	switch flag {
	case 1: //检查
		{
			characterCypher = fmt.Sprintf(`MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedImagingExamination]->(ie:Imaging_Examination_Items) `)
			inClause1 = fmt.Sprintf("d.diseaseOrSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN DISTINCT ie `)
		}
	case 2: //检验
		{
			characterCypher = fmt.Sprintf(`MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedLaboratoryExamination]->(lp:Laboratory_Procedure) `)
			inClause1 = fmt.Sprintf("d.diseaseOrSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN DISTINCT lp `)
		}
	case 3: //推荐电生理检查
		{
			characterCypher = fmt.Sprintf(`MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedElectrophysiologicStudy]->(e:Electrophysiologic_Study_Item) `)
			inClause1 = fmt.Sprintf("d.diseaseOrSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN DISTINCT e `)
		}
	case 4: //物理治疗
		{
			characterCypher = fmt.Sprintf(`MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedPhysicalTherapy]->(pt:Physical_Therapy) `)
			inClause1 = fmt.Sprintf("d.diseaseOrSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN DISTINCT pt `)
		}
	case 5: //心理治疗
		{
			characterCypher = fmt.Sprintf(`MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedPsychotherapy]->(ps:Psychotherapy)`)
			inClause1 = fmt.Sprintf("d.diseaseOrSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN DISTINCT ps `)
		}
	case 6: //西药
		{
			characterCypher = fmt.Sprintf(`MATCH(m:Western_Medicine)-[:treat]->(d:Disease_or_Syndrome)`)
			inClause1 = fmt.Sprintf("d.diseaseOrSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN DISTINCT m `)
		}
	case 7: //中成药
		{
			characterCypher = fmt.Sprintf(`MATCH(ts:TCM_Syndrome)-[:methodOfTreatment]->(tc:Treatment_With_Chinese_Herbs)-[:use]->(pd:Chinese_Patent_Drug)`)
			inClause1 = fmt.Sprintf("ts.tcmSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN DISTINCT pd `)
		}
	case 8: //方剂
		{
			characterCypher = fmt.Sprintf(`MATCH(ts:TCM_Syndrome)-[:methodOfTreatment]->(tc:Treatment_With_Chinese_Herbs)-[:use]->(p:Prescription)-[:hasPart]->(fp:Fomulations_Of_Prescription)-[:hasMedicines]->(cp:Chinese_Herb_Pieces)`)
			inClause1 = fmt.Sprintf("ts.tcmSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN  p.name, fp.name, cp.name `)
		}
	default:
		{
			return "{}"
		}
	}
	queryTemplate = fmt.Sprintf("%s WHERE %s  %s", characterCypher, inClause1, retstr)
	//queryTemplate = "MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedLaboratoryExamination]->(lp:Laboratory_Procedure),(lp)-[:includingLaboratoryExaminationItem]->(li:Laboratory_Examination_Item)\nWHERE   d.diseaseOrSyndrome = 1693425559169904997 AND d.tumorRADSClassification = 1803185983244467628 AND d.TNMStagingOfPrimaryTumors = 1800395451933899689 AND  d.TNMStagingOfRegionalLymphNodes = 1800396875244755894 AND  d.TNMStagingForDistantMetastasis = 1800396995059243962\t\t\t\t\t\t\t\nRETURN DISTINCT li"
	//如果乳腺专科的传参不为空的话则拼接乳腺专科的参数
	sign := isEmptyStats(breast)
	if sign == false {
		//拼接查询语句
		characterCypher = fmt.Sprintf(`MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedLaboratoryExamination]->(lp:Laboratory_Procedure),(lp)-[:includingLaboratoryExaminationItem]->(li:Laboratory_Examination_Item)`)
		//查询条件
		var parts []string
		if breast.BreastSignOrSymptom != 0 {
			parts = append(parts, fmt.Sprintf("d.signOrSymptom = %d", breast.BreastSignOrSymptom))
		}
		if breast.BreastDiseaseOrSyndrome != 0 {
			parts = append(parts, fmt.Sprintf("d.diseaseOrSyndrome = %d", breast.BreastDiseaseOrSyndrome))
		}
		if breast.BreastTumorRADSClassification != 0 {
			parts = append(parts, fmt.Sprintf("d.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
		}
		if breast.BreastTNMStagingOfPrimaryTumors != 0 {
			parts = append(parts, fmt.Sprintf("d.tnmStagingPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
		}
		if breast.TNMStagingOfRegionalLymphNodes != 0 {
			parts = append(parts, fmt.Sprintf("d.tnmStagingRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
		}
		if breast.TNMStagingForDistantMetastasis != 0 {
			parts = append(parts, fmt.Sprintf("d.tnmStagingDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
		}
		//使用AND拼接
		result := strings.Join(parts, " AND ")
		retstr = "RETURN DISTINCT li"
		queryTemplate = fmt.Sprintf("%s WHERE %s %s", characterCypher, result, retstr)
	}
	logging.Info("GetRecomendation-", flag, queryTemplate)
	result, err := repository.Runresult(ctx, queryTemplate, nil)
	if !err {
		// 如果没有返回结果，返回空的 JSON 对象
		return "{}"
	} else {
		//乳腺癌的直接返回
		if sign == false {
			return result
		}

		// 如果有记录返回，将 result 转换为 JSON 格式并返回
		logging.Info("result: ", result)

		var data []Data
		err := json.Unmarshal([]byte(result), &data)
		if err != nil {
			logging.Error("Error unmarshalling JSON: %v", err)
		}
		//type:8特别处理：
		if flag == 8 {
			// 定义输入结构体
			type Input struct {
				Values []string `json:"Values"`
				Keys   []string `json:"Keys"`
			}
			//// 定义输出结构体
			//type Output map[string]string
			//// 解析 JSON 字符串
			//var inputs []Input
			//if err := json.Unmarshal([]byte(result), &inputs); err != nil {
			//	logging.Error("Error parsing JSON:", err)
			//}
			//
			//// 转换为目标数据结构
			//outputMap := make(map[string][]string)
			//for _, input := range inputs {
			//	if len(input.Values) > 0 {
			//		key := input.Values[0] // 使用第一项作为键
			//		values := input.Values[1:]
			//		outputMap[key] = append(outputMap[key], values...)
			//	}
			//}
			//
			//// 格式化输出
			//finalOutput := make(map[string]string)
			//for key, values := range outputMap {
			//	finalOutput[key] = strings.Join(values, ",")
			//}
			//return finalOutput

			type Output map[string]map[string]string
			// 解析 JSON 字符串
			var inputs []Input
			if err := json.Unmarshal([]byte(result), &inputs); err != nil {
				logging.Error("Error parsing JSON:", err)
			}

			// 转换为目标数据结构
			outputMap := make(Output)
			for _, input := range inputs {
				if len(input.Values) == 3 {
					pName := input.Values[0]
					if _, exists := outputMap[pName]; !exists {
						outputMap[pName] = make(map[string]string)
					}
					outputMap[pName]["fp"] = input.Values[1]
					outputMap[pName]["cp"] = input.Values[2]
				}
			}
			return outputMap
		}

		rsplist := []Props{}
		for _, item := range data {
			for _, value := range item.Values {
				props := value.Props
				rsplist = append(rsplist, props)
				fmt.Printf("{%s %s %s %s}\n", props.BiospecimenType, props.Code, props.LaboratoryProcedure, props.Name)
			}
		}
		return rsplist
	}

}

// 乳腺癌病种推荐
func BreastGetRecomendation(diseaseOrSyndrome string, flag int, breastlist []BreastParam) interface{} {
	ctx := context.Background()
	queryTemplate := ""
	inClause1 := ""
	retstr := ""
	characterCypher := ""
	switch flag {
	case 1: //检查
		{
			characterCypher = fmt.Sprintf(`MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedImagingExamination]->(ie:Imaging_Examination_Items) `)
			var dataall []Itemd
			var fliterlistL []Itemd
			for _, breast := range breastlist {
				//拼接参数
				var parts []string
				if breast.BreastSignOrSymptom != 0 {
					parts = append(parts, fmt.Sprintf("d.signOrSymptom = %d", breast.BreastSignOrSymptom))
				}
				if breast.BreastDiseaseOrSyndrome != 0 {
					parts = append(parts, fmt.Sprintf("d.diseaseOrSyndrome = %d", breast.BreastDiseaseOrSyndrome))
				}
				if breast.BreastTumorRADSClassification != 0 {
					parts = append(parts, fmt.Sprintf("d.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
				}
				if breast.BreastTNMStagingOfPrimaryTumors != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingOfPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
				}
				if breast.TNMStagingOfRegionalLymphNodes != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingOfRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
				}
				if breast.TNMStagingForDistantMetastasis != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingForDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
				}
				if breast.LuminalTyping != 0 {
					parts = append(parts, fmt.Sprintf("d.luminalTyping = %d", breast.LuminalTyping))
				}
				//使用AND拼接
				//resurnresult := strings.Join(parts, " AND ")
				inClause1 = fmt.Sprintf("d.diseaseOrSyndrome = %d", breast.BreastDiseaseOrSyndrome)
				retstr = fmt.Sprintf(`RETURN DISTINCT ie.imagingExaminationItem `)
				queryTemplate = fmt.Sprintf("%s WHERE %s  %s", characterCypher, inClause1, retstr)
				result, err := repository.Runresult(ctx, queryTemplate, nil)
				if !err {
					// 如果没有返回结果，返回空的 JSON 对象
					return "{}"
				} else {
					var data []Item
					err := json.Unmarshal([]byte(result), &data)
					if err != nil {
						fmt.Println("Error parsing JSON:", err)
						return ""
					}
					// 构建转换后的数据结构 []Itemstr
					var itemsStr []Itemd
					for _, item := range data {
						var valuesStr []string
						for _, v := range item.Values {
							valuesStr = append(valuesStr, strconv.FormatInt(v, 10))
						}
						itemsStr = append(itemsStr, Itemd{
							Keys:   item.Keys,
							Values: valuesStr,
						})
					}
					// 添加新键值对
					for i, v := range itemsStr {
						name, _ := utils.CheckTreatServiceName(v.Values[0])
						itemsStr[i].Values = append(itemsStr[i].Values, name)
						itemsStr[i].Keys = append(itemsStr[i].Keys, "name")
					}
					dataall = append(dataall, itemsStr...)
					fliterlistL = removeDuplicates(dataall)
				}
			}
			resultstrr, err := json.Marshal(fliterlistL)
			if err != nil {
				fmt.Println("Error marshalling JSON:", err)
				return ""
			}
			eee := string(resultstrr)
			return eee
		}
	case 2: //检验
		{
			characterCypher = fmt.Sprintf(`MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedLaboratoryExamination]->(lp:Laboratory_Procedure),(lp)-[:includingLaboratoryExaminationItem]->(li:Laboratory_Examination_Item)`)
			var dataall []Itemd
			var fliterlistL []Itemd
			for _, breast := range breastlist {
				//拼接参数
				var parts []string
				if breast.BreastSignOrSymptom != 0 {
					parts = append(parts, fmt.Sprintf("d.signOrSymptom = %d", breast.BreastSignOrSymptom))
				}
				if breast.BreastDiseaseOrSyndrome != 0 {
					parts = append(parts, fmt.Sprintf("d.diseaseOrSyndrome = %d", breast.BreastDiseaseOrSyndrome))
				}
				if breast.BreastTumorRADSClassification != 0 {
					parts = append(parts, fmt.Sprintf("d.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
				}
				if breast.BreastTNMStagingOfPrimaryTumors != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingOfPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
				}
				if breast.TNMStagingOfRegionalLymphNodes != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingOfRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
				}
				if breast.TNMStagingForDistantMetastasis != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingForDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
				}
				//使用AND拼接
				resurnresult := strings.Join(parts, " AND ")
				retstr = fmt.Sprintf(`RETURN DISTINCT li.laboratoryExaminationItem`)
				queryTemplate = fmt.Sprintf("%s WHERE %s  %s", characterCypher, resurnresult, retstr)
				result, err := repository.Runresult(ctx, queryTemplate, nil)
				if !err {
					// 如果没有返回结果，返回空的 JSON 对象
					return "{}"
				} else {
					var data []Item
					err := json.Unmarshal([]byte(result), &data)
					if err != nil {
						fmt.Println("Error parsing JSON:", err)
						return ""
					}
					// 构建转换后的数据结构 []Itemstr
					var itemsStr []Itemd
					for _, item := range data {
						var valuesStr []string
						for _, v := range item.Values {
							valuesStr = append(valuesStr, strconv.FormatInt(v, 10))
						}
						itemsStr = append(itemsStr, Itemd{
							Keys:   item.Keys,
							Values: valuesStr,
						})
					}
					// 添加新键值对
					for i, v := range itemsStr {
						name, _ := utils.OtherGetNameBySoid(v.Values[0])
						itemsStr[i].Values = append(itemsStr[i].Values, name)
						itemsStr[i].Keys = append(itemsStr[i].Keys, "name")
					}
					dataall = append(dataall, itemsStr...)
					fliterlistL = removeDuplicates(dataall)
				}
			}
			resultstrr, err := json.Marshal(fliterlistL)
			if err != nil {
				fmt.Println("Error marshalling JSON:", err)
				return ""
			}
			eee := string(resultstrr)
			return eee
		}
	case 3: //推荐电生理检查
		{
			characterCypher = fmt.Sprintf(`MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedElectrophysiologicStudy]->(e:Electrophysiologic_Study_Item)`)
			var dataall []Itemd
			var fliterlistL []Itemd
			for _, breast := range breastlist {
				//拼接参数
				var parts []string
				if breast.BreastSignOrSymptom != 0 {
					parts = append(parts, fmt.Sprintf("d.signOrSymptom = %d", breast.BreastSignOrSymptom))
				}
				if breast.BreastDiseaseOrSyndrome != 0 {
					parts = append(parts, fmt.Sprintf("d.diseaseOrSyndrome = %d", breast.BreastDiseaseOrSyndrome))
				}
				if breast.BreastTumorRADSClassification != 0 {
					parts = append(parts, fmt.Sprintf("d.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
				}
				if breast.BreastTNMStagingOfPrimaryTumors != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingOfPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
				}
				if breast.TNMStagingOfRegionalLymphNodes != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingOfRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
				}
				if breast.TNMStagingForDistantMetastasis != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingForDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
				}
				//使用AND拼接
				resurnresult := strings.Join(parts, " AND ")
				inClause1 = fmt.Sprintf("d.diseaseOrSyndrome in %s", diseaseOrSyndrome)
				retstr = fmt.Sprintf(`RETURN DISTINCT e.electrophysiologicStudyType `)
				queryTemplate = fmt.Sprintf("%s WHERE %s  %s", characterCypher, resurnresult, retstr)
				result, err := repository.Runresult(ctx, queryTemplate, nil)
				if !err {
					// 如果没有返回结果，返回空的 JSON 对象
					return "{}"
				} else {
					var data []Item
					err := json.Unmarshal([]byte(result), &data)
					if err != nil {
						fmt.Println("Error parsing JSON:", err)
						return ""
					}
					// 构建转换后的数据结构 []Itemstr
					var itemsStr []Itemd
					for _, item := range data {
						var valuesStr []string
						for _, v := range item.Values {
							valuesStr = append(valuesStr, strconv.FormatInt(v, 10))
						}
						itemsStr = append(itemsStr, Itemd{
							Keys:   item.Keys,
							Values: valuesStr,
						})
					}
					// 添加新键值对
					for i, v := range itemsStr {
						name, _ := utils.OtherGetNameBySoid(v.Values[0])
						itemsStr[i].Values = append(itemsStr[i].Values, name)
						itemsStr[i].Keys = append(itemsStr[i].Keys, "name")
					}
					dataall = append(dataall, itemsStr...)
					fliterlistL = removeDuplicates(dataall)
				}
			}
			resultstrr, err := json.Marshal(fliterlistL)
			if err != nil {
				fmt.Println("Error marshalling JSON:", err)
				return ""
			}
			eee := string(resultstrr)
			return eee

		}
	case 4: //物理治疗
		{
			characterCypher = fmt.Sprintf(`MATCH (d:Disease_or_Syndrome)-[:diseasesRelatedPhysicalTherapy]->(pt:Physical_Therapy)`)
			var dataall []Itemd
			var fliterlistL []Itemd
			for _, breast := range breastlist {
				//拼接参数
				var parts []string
				if breast.BreastSignOrSymptom != 0 {
					parts = append(parts, fmt.Sprintf("d.signOrSymptom = %d", breast.BreastSignOrSymptom))
				}
				if breast.BreastDiseaseOrSyndrome != 0 {
					parts = append(parts, fmt.Sprintf("d.diseaseOrSyndrome = %d", breast.BreastDiseaseOrSyndrome))
				}
				if breast.BreastTumorRADSClassification != 0 {
					parts = append(parts, fmt.Sprintf("d.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
				}
				if breast.BreastTNMStagingOfPrimaryTumors != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingOfPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
				}
				if breast.TNMStagingOfRegionalLymphNodes != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingOfRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
				}
				if breast.TNMStagingForDistantMetastasis != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingForDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
				}
				//使用AND拼接
				resurnresult := strings.Join(parts, " AND ")
				inClause1 = fmt.Sprintf("d.diseaseOrSyndrome in %s", diseaseOrSyndrome)
				retstr = fmt.Sprintf(`RETURN DISTINCT pt.instanceCode`)
				queryTemplate = fmt.Sprintf("%s WHERE %s  %s", characterCypher, resurnresult, retstr)
				result, err := repository.Runresult(ctx, queryTemplate, nil)
				if !err {
					// 如果没有返回结果，返回空的 JSON 对象
					return "{}"
				} else {
					var data []Item
					err := json.Unmarshal([]byte(result), &data)
					if err != nil {
						fmt.Println("Error parsing JSON:", err)
						return ""
					}
					// 构建转换后的数据结构 []Itemstr
					var itemsStr []Itemd
					for _, item := range data {
						var valuesStr []string
						for _, v := range item.Values {
							valuesStr = append(valuesStr, strconv.FormatInt(v, 10))
						}
						itemsStr = append(itemsStr, Itemd{
							Keys:   item.Keys,
							Values: valuesStr,
						})
					}
					// 添加新键值对
					for i, v := range itemsStr {
						name, _ := utils.OtherGetNameBySoid(v.Values[0])
						itemsStr[i].Values = append(itemsStr[i].Values, name)
						itemsStr[i].Keys = append(itemsStr[i].Keys, "name")
					}
					dataall = append(dataall, itemsStr...)
					fliterlistL = removeDuplicates(dataall)
				}
			}
			resultstrr, err := json.Marshal(fliterlistL)
			if err != nil {
				fmt.Println("Error marshalling JSON:", err)
				return ""
			}
			eee := string(resultstrr)
			return eee
		}
	case 5: //心理治疗
		{
			characterCypher = fmt.Sprintf(`MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedPsychotherapy]->(ps:Psychotherapy)`)
			var dataall []Itemd
			var fliterlistL []Itemd
			for _, breast := range breastlist {
				//拼接参数
				var parts []string
				if breast.BreastSignOrSymptom != 0 {
					parts = append(parts, fmt.Sprintf("d.signOrSymptom = %d", breast.BreastSignOrSymptom))
				}
				if breast.BreastDiseaseOrSyndrome != 0 {
					parts = append(parts, fmt.Sprintf("d.diseaseOrSyndrome = %d", breast.BreastDiseaseOrSyndrome))
				}
				if breast.BreastTumorRADSClassification != 0 {
					parts = append(parts, fmt.Sprintf("d.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
				}
				if breast.BreastTNMStagingOfPrimaryTumors != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingOfPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
				}
				if breast.TNMStagingOfRegionalLymphNodes != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingOfRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
				}
				if breast.TNMStagingForDistantMetastasis != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingForDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
				}
				//使用AND拼接
				resurnresult := strings.Join(parts, " AND ")
				inClause1 = fmt.Sprintf("d.diseaseOrSyndrome in %s", diseaseOrSyndrome)
				retstr = fmt.Sprintf(`RETURN DISTINCT ps.psychotherapyItem`)
				queryTemplate = fmt.Sprintf("%s WHERE %s  %s", characterCypher, resurnresult, retstr)
				result, err := repository.Runresult(ctx, queryTemplate, nil)
				if !err {
					// 如果没有返回结果，返回空的 JSON 对象
					return "{}"
				} else {
					var data []Item
					err := json.Unmarshal([]byte(result), &data)
					if err != nil {
						fmt.Println("Error parsing JSON:", err)
						return ""
					}
					// 构建转换后的数据结构 []Itemstr
					var itemsStr []Itemd
					for _, item := range data {
						var valuesStr []string
						for _, v := range item.Values {
							valuesStr = append(valuesStr, strconv.FormatInt(v, 10))
						}
						itemsStr = append(itemsStr, Itemd{
							Keys:   item.Keys,
							Values: valuesStr,
						})
					}
					// 添加新键值对
					for i, v := range itemsStr {
						name, _ := utils.CheckTreatServiceName(v.Values[0])
						itemsStr[i].Values = append(itemsStr[i].Values, name)
						itemsStr[i].Keys = append(itemsStr[i].Keys, "name")
					}
					dataall = append(dataall, itemsStr...)
					fliterlistL = removeDuplicates(dataall)
				}
			}
			resultstrr, err := json.Marshal(fliterlistL)
			if err != nil {
				fmt.Println("Error marshalling JSON:", err)
				return ""
			}
			eee := string(resultstrr)
			return eee
		}
	case 6: //西药
		{
			characterCypher = fmt.Sprintf(`MATCH(m:Western_Medicine)-[:treat]->(d:Disease_or_Syndrome)`)
			var dataall []Itemd
			var fliterlistL []Itemd
			inClause1 = fmt.Sprintf("d.diseaseOrSyndrome in %s", diseaseOrSyndrome)
			retstr = fmt.Sprintf(`RETURN DISTINCT m.westernMedicine`)
			for _, breast := range breastlist {
				//拼接参数
				var parts []string
				if breast.BreastSignOrSymptom != 0 {
					parts = append(parts, fmt.Sprintf("d.signOrSymptom = %d", breast.BreastSignOrSymptom))
				}
				if breast.BreastDiseaseOrSyndrome != 0 {
					parts = append(parts, fmt.Sprintf("d.diseaseOrSyndrome = %d", breast.BreastDiseaseOrSyndrome))
				}
				if breast.BreastTumorRADSClassification != 0 {
					parts = append(parts, fmt.Sprintf("d.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
				}
				if breast.BreastTNMStagingOfPrimaryTumors != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingOfPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
				}
				if breast.TNMStagingOfRegionalLymphNodes != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingOfRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
				}
				if breast.TNMStagingForDistantMetastasis != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingForDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
				}
				if breast.LuminalTyping != 0 {
					parts = append(parts, fmt.Sprintf("d.luminalTyping = %d", breast.LuminalTyping))
				}
				//使用AND拼接
				resurnresult := strings.Join(parts, " AND ")
				queryTemplate = fmt.Sprintf("%s WHERE %s  %s", characterCypher, resurnresult, retstr)
				result, err := repository.Runresult(ctx, queryTemplate, nil)
				if !err {
					// 如果没有返回结果，返回空的 JSON 对象
					return "{}"
				} else {
					var data []Item
					err := json.Unmarshal([]byte(result), &data)
					if err != nil {
						fmt.Println("Error parsing JSON:", err)
						return ""
					}
					// 构建转换后的数据结构 []Itemstr
					var itemsStr []Itemd
					for _, item := range data {
						var valuesStr []string
						for _, v := range item.Values {
							valuesStr = append(valuesStr, strconv.FormatInt(v, 10))
						}
						itemsStr = append(itemsStr, Itemd{
							Keys:   item.Keys,
							Values: valuesStr,
						})
					}
					// 添加新键值对
					for i, v := range itemsStr {
						name, _ := utils.CheckMedicalServiceName(v.Values[0])
						itemsStr[i].Values = append(itemsStr[i].Values, name)
						itemsStr[i].Keys = append(itemsStr[i].Keys, "name")
					}
					dataall = append(dataall, itemsStr...)
					fliterlistL = removeDuplicates(dataall)
				}
			}
			resultstrr, err := json.Marshal(fliterlistL)
			if err != nil {
				fmt.Println("Error marshalling JSON:", err)
				return ""
			}
			eee := string(resultstrr)
			return eee
		}
	case 7: //中成药
		{
			characterCypher = fmt.Sprintf(`MATCH(ts:TCM_Syndrome)-[:methodOfTreatment]->(tc:Treatment_With_Chinese_Herbs)-[:use]->(pd:Chinese_Patent_Drug)`)
			var dataall []Itemd
			var fliterlistL []Itemd
			for _, breast := range breastlist {
				//拼接参数
				var parts []string
				if breast.BreastSignOrSymptom != 0 {
					parts = append(parts, fmt.Sprintf("d.signOrSymptom = %d", breast.BreastSignOrSymptom))
				}
				if breast.BreastDiseaseOrSyndrome != 0 {
					parts = append(parts, fmt.Sprintf("d.diseaseOrSyndrome = %d", breast.BreastDiseaseOrSyndrome))
				}
				if breast.BreastTumorRADSClassification != 0 {
					parts = append(parts, fmt.Sprintf("d.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
				}
				if breast.BreastTNMStagingOfPrimaryTumors != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingOfPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
				}
				if breast.TNMStagingOfRegionalLymphNodes != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingOfRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
				}
				if breast.TNMStagingForDistantMetastasis != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingForDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
				}
				//使用AND拼接
				//resurnresult := strings.Join(parts, " AND ")
				inClause1 = fmt.Sprintf("ts.tcmSyndrome = %d", breast.BreastSignOrSymptom)
				retstr = fmt.Sprintf(`RETURN DISTINCT pd.chinesePatentDrug`)
				queryTemplate = fmt.Sprintf("%s WHERE %s  %s", characterCypher, inClause1, retstr)
				result, err := repository.Runresult(ctx, queryTemplate, nil)
				if !err {
					// 如果没有返回结果，返回空的 JSON 对象
					return "{}"
				} else {
					var data []Item
					err := json.Unmarshal([]byte(result), &data)
					if err != nil {
						fmt.Println("Error parsing JSON:", err)
						return ""
					}
					// 构建转换后的数据结构 []Itemstr
					var itemsStr []Itemd
					for _, item := range data {
						var valuesStr []string
						for _, v := range item.Values {
							valuesStr = append(valuesStr, strconv.FormatInt(v, 10))
						}
						itemsStr = append(itemsStr, Itemd{
							Keys:   item.Keys,
							Values: valuesStr,
						})
					}
					// 添加新键值对
					for i, v := range itemsStr {
						name, _ := utils.CheckMedicalServiceName(v.Values[0])
						itemsStr[i].Values = append(itemsStr[i].Values, name)
						itemsStr[i].Keys = append(itemsStr[i].Keys, "name")
					}
					dataall = append(dataall, itemsStr...)
					fliterlistL = removeDuplicates(dataall)
				}
			}
			resultstrr, err := json.Marshal(fliterlistL)
			if err != nil {
				fmt.Println("Error marshalling JSON:", err)
				return ""
			}
			eee := string(resultstrr)
			return eee

		}
	case 8: //方剂
		{
			characterCypher = fmt.Sprintf(`MATCH(ts:TCM_Syndrome)-[:methodOfTreatment]->(tc:Treatment_With_Chinese_Herbs)-[:use]->(p:Prescription)-[:hasPart]->(fp:Fomulations_Of_Prescription)-[:hasMedicines]->(cp:Chinese_Herb_Pieces)`)
			//var dataall []Itemd
			//var fliterlistL []Itemd
			for _, breast := range breastlist {
				//拼接参数
				var parts []string
				if breast.BreastSignOrSymptom != 0 {
					parts = append(parts, fmt.Sprintf("d.signOrSymptom = %d", breast.BreastSignOrSymptom))
				}
				if breast.BreastDiseaseOrSyndrome != 0 {
					parts = append(parts, fmt.Sprintf("d.diseaseOrSyndrome = %d", breast.BreastDiseaseOrSyndrome))
				}
				if breast.BreastTumorRADSClassification != 0 {
					parts = append(parts, fmt.Sprintf("d.tumorRADSClassification = %d", breast.BreastTumorRADSClassification))
				}
				if breast.BreastTNMStagingOfPrimaryTumors != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingOfPrimaryTumors = %d", breast.BreastTNMStagingOfPrimaryTumors))
				}
				if breast.TNMStagingOfRegionalLymphNodes != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingOfRegionalLymphNodes = %d", breast.TNMStagingOfRegionalLymphNodes))
				}
				if breast.TNMStagingForDistantMetastasis != 0 {
					parts = append(parts, fmt.Sprintf("d.TNMStagingForDistantMetastasis = %d", breast.TNMStagingForDistantMetastasis))
				}
				//使用AND拼接
				//resurnresult := strings.Join(parts, " AND ")
				inClause1 = fmt.Sprintf("ts.tcmSyndrome = %d", breast.BreastSignOrSymptom)
				retstr = fmt.Sprintf(`RETURN DISTINCT p.prescription, cp.chineseHerbPieces,cp.dosage,cp.unit `)
				queryTemplate = fmt.Sprintf("%s WHERE %s  %s", characterCypher, inClause1, retstr)
				result, err := repository.Runresult(ctx, queryTemplate, nil)
				if !err {
					// 如果没有返回结果，返回空的 JSON 对象
					return "{}"
				} else {
					// 解析 JSON 数据
					var originalData []Input
					err2 := json.Unmarshal([]byte(result), &originalData)
					if err2 != nil {
						fmt.Println("Error unmarshaling JSON:", err)
						return ""
					}

					// 用于存储输出数据的临时map
					tempMap := make(map[string][]Detail)

					for _, item := range originalData {
						prescription := item.Values[0].(string)

						var chineseHerbPieces string
						switch v := item.Values[1].(type) {
						case int64:
							chineseHerbPieces = fmt.Sprintf("%d", v)
						case float64:
							chineseHerbPieces = fmt.Sprintf("%.0f", v)
						case string:
							chineseHerbPieces = v
						case int:
							chineseHerbPieces = strconv.Itoa(v)
						default:
							chineseHerbPieces = fmt.Sprintf("%v", v)
						}

						var dosage string
						switch v := item.Values[2].(type) {
						case int64:
							dosage = fmt.Sprintf("%.1f", float64(v))
						case float64:
							dosage = fmt.Sprintf("%.1f", v)
						case string:
							dosage = v
						case int:
							dosage = fmt.Sprintf("%.1f", float64(v))
						default:
							dosage = fmt.Sprintf("%v", v)
						}

						var unit string
						switch v := item.Values[3].(type) {
						case int64:
							unit = fmt.Sprintf("%d", v)
						case float64:
							unit = fmt.Sprintf("%.0f", v)
						case string:
							unit = v
						case int:
							unit = strconv.Itoa(v)
						default:
							unit = fmt.Sprintf("%v", v)
						}
						//查询对应的药名与soid
						utilss, _ := utils.OtherGetNameBySoid(unit)
						if utilss == "" {
							utilss = "g"
						}
						chineseHerbPiecess, err4 := utils.CheckMedicalServiceName(chineseHerbPieces)
						if err4 == nil {
							chineseHerbPiecess = ""
						}
						detail := Detail{
							ChineseHerbPieces: chineseHerbPiecess,
							Dosage:            dosage,
							Unit:              utilss,
						}

						tempMap[prescription] = append(tempMap[prescription], detail)
					}

					// 转换成最终的输出结构
					var outputData []Outputs
					for prescription, details := range tempMap {
						outputData = append(outputData, Outputs{
							Prescription: prescription,
							Detail:       details,
						})
					}

					// 将结果转换成JSON并打印
					outputJSON, err := json.MarshalIndent(outputData, "", "  ")
					if err != nil {
						fmt.Println("Error:", err)
					} else {
						fmt.Println(string(outputJSON))
					}
					return string(outputJSON)
				}
			}
		}
	default:
		{
			return "{}"
		}
	}
	queryTemplate = fmt.Sprintf("%s WHERE %s  %s", characterCypher, inClause1, retstr)
	//queryTemplate = "MATCH(d:Disease_or_Syndrome)-[:diseasesRelatedLaboratoryExamination]->(lp:Laboratory_Procedure),(lp)-[:includingLaboratoryExaminationItem]->(li:Laboratory_Examination_Item)\nWHERE   d.diseaseOrSyndrome = 1693425559169904997 AND d.tumorRADSClassification = 1803185983244467628 AND d.TNMStagingOfPrimaryTumors = 1800395451933899689 AND  d.TNMStagingOfRegionalLymphNodes = 1800396875244755894 AND  d.TNMStagingForDistantMetastasis = 1800396995059243962\t\t\t\t\t\t\t\nRETURN DISTINCT li"
	//如果乳腺专科的传参不为空的话则拼接乳腺专科的参数
	//queryTemplate = "\nMATCH  \n(d:Disease_or_Syndrome)-[:diseasesRelatedLaboratoryExamination]->(lp:Laboratory_Procedure),\t//疾病相关实验室检查\n(lp)-[:includingLaboratoryExaminationItem]->(li:Laboratory_Examination_Item)\t\t//实验室检查的项目\nWHERE   \nd.diseaseOrSyndrome = 1693425559169904997 AND d.tumorRADSClassification = 1803185983244467628 AND  \n    d.TNMStagingOfPrimaryTumors = 1800395451933899689 AND  d.TNMStagingOfRegionalLymphNodes = 1800396875244755894 AND  d.TNMStagingForDistantMetastasis = 1800396995059243962\t\t\t\t\t\t\t\nRETURN DISTINCT li.instanceCode"
	sign := false
	logging.Info("GetRecomendation-", flag, queryTemplate)
	result, err := repository.Runresult(ctx, queryTemplate, nil)
	if !err {
		// 如果没有返回结果，返回空的 JSON 对象
		return "{}"
	} else {
		//乳腺癌的直接返回
		if sign == false {
			return result
		}

		// 如果有记录返回，将 result 转换为 JSON 格式并返回
		logging.Info("result: ", result)

		var data []Data
		err := json.Unmarshal([]byte(result), &data)
		if err != nil {
			logging.Error("Error unmarshalling JSON: %v", err)
		}
		//type:8特别处理：
		if flag == 8 {
			// 定义输入结构体
			type Input struct {
				Values []string `json:"Values"`
				Keys   []string `json:"Keys"`
			}

			type Output map[string]map[string]string
			// 解析 JSON 字符串
			var inputs []Input
			if err := json.Unmarshal([]byte(result), &inputs); err != nil {
				logging.Error("Error parsing JSON:", err)
			}

			// 转换为目标数据结构
			outputMap := make(Output)
			for _, input := range inputs {
				if len(input.Values) == 3 {
					pName := input.Values[0]
					if _, exists := outputMap[pName]; !exists {
						outputMap[pName] = make(map[string]string)
					}
					outputMap[pName]["fp"] = input.Values[1]
					outputMap[pName]["cp"] = input.Values[2]
				}
			}
			return outputMap
		}
		rsplist := []Props{}
		for _, item := range data {
			for _, value := range item.Values {
				props := value.Props
				rsplist = append(rsplist, props)
				fmt.Printf("{%s %s %s %s}\n", props.BiospecimenType, props.Code, props.LaboratoryProcedure, props.Name)
			}
		}
		return rsplist
	}

}

// 检验报告响应体格式
// 定义 Props 结构体
type Props struct {
	BiospecimenType     string `json:"biospecimenType"`
	Code                string `json:"code"`
	LaboratoryProcedure string `json:"laboratoryProcedure"`
	Name                string `json:"name"`
	GenericNameOfDrug   string `json:"genericNameOfDrug"`
	SoID                string `json:"soid"`
}

type Propss struct {
	BiospecimenType     string `json:"biospecimenType"`
	Code                string `json:"code"`
	LaboratoryProcedure string `json:"laboratoryProcedure"`
	Name                string `json:"name"`
}

// 定义 Value 结构体
type Value struct {
	ElementID string   `json:"ElementId"`
	ID        int      `json:"Id"`
	Labels    []string `json:"Labels"`
	Props     Props    `json:"Props"`
}

// 定义 Data 结构体
type Data struct {
	Keys   []string `json:"Keys"`
	Values []Value  `json:"Values"`
}

// 定义响应结构体
type Response struct {
	Status  int    `json:"status"`
	Message string `json:"message"`
	Data    []Data `json:"data"`
}

type Output struct {
	Name    string `json:"name"`
	Content string `json:"content"`
}

type Itemd struct {
	Values []string `json:"Values"`
	Keys   []string `json:"Keys"`
}

// 对列表进行屈去重
func removeDuplicates(dataall []Itemd) []Itemd {
	seen := make(map[string]struct{})
	var result []Itemd

	for _, item := range dataall {
		valuesJSON, _ := json.Marshal(item.Values)
		valuesStr := string(valuesJSON)

		if _, exists := seen[valuesStr]; !exists {
			seen[valuesStr] = struct{}{}
			result = append(result, item)
		}
	}

	return result
}

// 解析药剂的结构体
// 原始结构体
// 原始结构体
type Input struct {
	Values []interface{} `json:"Values"`
	Keys   []string      `json:"Keys"`
}

// 新的结构体
type Detail struct {
	ChineseHerbPieces interface{} `json:"chineseHerbPieces"`
	Dosage            interface{} `json:"dosage"`
	Unit              interface{} `json:"unit"`
}

type Outputs struct {
	Prescription string   `json:"prescription"`
	Detail       []Detail `json:"detail"`
}

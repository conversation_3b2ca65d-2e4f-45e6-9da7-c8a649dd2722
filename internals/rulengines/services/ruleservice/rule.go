package ruleservice

import (
	"errors"
	"fmt"
	"github.com/bilibili/gengine/builder"
	"github.com/bilibili/gengine/context"
	"github.com/bilibili/gengine/engine"
	"kmbservice/dsl"
	"kmbservice/pkg/logging"
	"kmbservice/repository/models"
	"time"
)

const diabetesRxRuleStr = `
rule "Diabetes Medication Recommendation for Elderly Patients with Renal Impairment" salience 100
begin
    if patient.Age >= 60 &&
       patient.Duration >= 5 &&
       patient.HbA1c > 8.0 &&
       patient.EGFR < 60.0 {
       patient.AddMessage("01","二甲双胍 + DPP-4 抑制剂：密切监测肾功能")
    }
end
rule "Diabetes Medication Recommendation for Elderly Patients with Normal Renal Function" salience 200
begin
    if patient.Age >= 60 &&
            patient.Duration >= 5 &&
            patient.HbA1c > 8.0 &&
            patient.EGFR >= 60.0 {
			patient.AddMessage("02","二甲双胍 + GLP-1 受体激动剂：密切监测心血管指标")
    } 
end
rule "Diabetes Medication Recommendation for Younger Patients with Uncontrolled HbA1c" salience 300
begin
   if patient.Age < 60 &&
            patient.Duration < 5 &&
            patient.HbA1c > 8.0 &&
            patient.EGFR >= 60.0 {
            patient.AddMessage("03","二甲双胍 + 胰岛素分泌促进剂：定期监测血糖波动")
    } 
end
rule "age test" salience 500
begin
   if patient.Age > 60  {
            patient.AddMessage("04","患者大于60岁，请注意三高情况。")
    } 
end

rule "MaritalStatus test" salience 600
begin
   if patient.MaritalStatus == "未婚" && patient.Gender=="女"{
            patient.AddMessage("05","患者为未婚女性，阴部B超是否确认？。")
    } 
end
`

// MaritalStatus
func GetTests(patientid string) ([]dsl.TestResult, error) {
	var results []dsl.TestResult
	db := models.GetDb()
	query := "SELECT test_id AS id, test_name AS test_name, test_result AS result, test_ref AS reference, test_unit AS unit FROM lis_report_item WHERE patid = ?"
	if err := db.Raw(query, patientid).Scan(&results).Error; err != nil {
		return nil, err
	}

	// 检查是否有结果
	if len(results) == 0 {
		return nil, nil
	}
	return results, nil
}
func Getrule(ruleid int64) string {
	rulecontents := models.GetContent(ruleid)
	return rulecontents.RuleContent
}
func ExecRule(rulecontents string) string {
	return rulecontents
}

func Recommend(str string) {
	fmt.Println(str)
}
func EvaluateDiabetesRx(patient *dsl.PatientRx, ruleid int64) error {
	fmt.Println(ruleid)
	dataContext := context.NewDataContext()
	dataContext.Add("patient", patient)
	dataContext.Add("Out", Recommend)
	ruleBuilder := builder.NewRuleBuilder(dataContext)
	start1 := time.Now().UnixNano()
	temp := Getrule(ruleid)
	fmt.Println(temp)
	if temp == "" {
		logging.Error("获取规则失败")
		return errors.New("获取规则失败")
	}
	err := ruleBuilder.BuildRuleFromString(temp)
	if err != nil {
		panic(err)
		return err
	}
	end1 := time.Now().UnixNano()
	logging.Info(fmt.Sprintf("rules num:%d, load rules cost time:%d ns", len(ruleBuilder.Kc.RuleEntities), end1-start1))
	eng := engine.NewGengine()
	start := time.Now().UnixNano()
	err = eng.Execute(ruleBuilder, true)
	if err != nil {
		panic(err)
		return err
	}
	end := time.Now().UnixNano()
	ms := (end - start)
	logging.Info(fmt.Sprintf("execute rule cost %d ns", ms))
	for _, msg := range patient.Msg {
		fmt.Printf("ID: %s, Name: %s\n", msg.MessageID, msg.MessageDesc)
	}
	return nil
}

package ruleservice

import (
	"errors"
	"fmt"
	"github.com/bilibili/gengine/builder"
	"github.com/bilibili/gengine/context"
	"github.com/bilibili/gengine/engine"
	"kmbservice/dsl"
	"kmbservice/pkg/logging"
	"time"
)

// 患者与药物规则
func ExecPatientOrMedicine(ruleid int64, patient *dsl.Patient, medicines []*dsl.Medicine) error {
	var err error
	dataContext := context.NewDataContext()
	dataContext.Add("patient", patient)
	medicineStore := dsl.NewMedicineStore()
	start1 := time.Now().UnixNano()
	for _, med := range medicines {
		medicineStore.AddMedicine(med)
		fmt.Println("med_id:", med.DrugID)
	}
	medicineStore.AddMedicineList(medicines)
	dataContext.Add("medicines", medicineStore)
	fmt.Println("drug_id:", medicineStore.GetMedicineByID("CDO6E01"))
	fmt.Println("age:", patient.Age > 65)
	fmt.Println("ageunit:", patient.AgeUnit == "3")

	ruleBuilder := builder.NewRuleBuilder(dataContext)
	temp := Getrule(ruleid)
	if temp == "" {
		logging.Error("获取规则失败")
		return errors.New("获取规则失败")
	}
	err = ruleBuilder.BuildRuleFromString(temp)
	if err != nil {
		panic(err)
		return err
	}
	end1 := time.Now().UnixNano()
	logging.Info(fmt.Sprintf("rules num:%d, load rules cost time:%d ns", len(ruleBuilder.Kc.RuleEntities), end1-start1))
	eng := engine.NewGengine()
	start := time.Now().UnixNano()
	err = eng.Execute(ruleBuilder, true)
	if err != nil {
		panic(err)
		return err
	}
	end := time.Now().UnixNano()
	ms := (end - start)
	logging.Info(fmt.Sprintf("execute rule cost %d ns", ms))

	for _, msg := range patient.Msg {
		fmt.Printf("ID: %s, Name: %s\n", msg.MessageID, msg.MessageDesc)
	}
	return nil
}

// 药品与检验报告之间规则
func ExectestReportsWithMedicine(ruleid int64, testReports []*dsl.TestReportDetail, medicines []*dsl.Medicine, patient *dsl.Patient) error {
	var err error
	dataContext := context.NewDataContext()
	dataContext.Add("patient", patient)
	medicineStore := dsl.NewMedicineStore()
	testReportDetailStore := dsl.NewTestReportDetailStore()
	testReportDetailMapStore := dsl.NewTestReportDetailMapStore()
	start1 := time.Now().UnixNano()
	for _, med := range medicines {
		medicineStore.AddMedicine(med)
	}
	//传入报告的值
	for _, testReport := range testReports {
		testReportDetailStore.AddReport(testReport)
	}
	for _, testReport := range testReports {
		testReportDetailMapStore.AddReportMapForID(testReport)
	}
	for _, testReport := range testReports {
		testReportDetailMapStore.AddReportMapForTestItemID(testReport)
	}

	dataContext.Add("medicines", medicineStore)
	dataContext.Add("testReports", testReports)
	dataContext.Add("testReports", testReportDetailStore)
	dataContext.Add("testReports", testReportDetailMapStore)
	fmt.Println("drug_id:", medicineStore.GetMedicineByID("CDO6E01"))

	ruleBuilder := builder.NewRuleBuilder(dataContext)
	temp := Getrule(ruleid)
	if temp == "" {
		logging.Error("获取规则失败")
		return errors.New("获取规则失败")
	}

	err = ruleBuilder.BuildRuleFromString(temp)
	if err != nil {
		panic(err)
		return err
	}
	end1 := time.Now().UnixNano()
	logging.Info(fmt.Sprintf("rules num:%d, load rules cost time:%d ns", len(ruleBuilder.Kc.RuleEntities), end1-start1))
	eng := engine.NewGengine()
	start := time.Now().UnixNano()
	err = eng.Execute(ruleBuilder, true)
	if err != nil {
		panic(err)
		return err
	}
	end := time.Now().UnixNano()
	ms := (end - start)
	logging.Info(fmt.Sprintf("execute rule cost %d ns", ms))
	for _, msg := range patient.Msg {
		fmt.Printf("ID: %s, Name: %s\n", msg.MessageID, msg.MessageDesc)
	}
	return nil
}

// 【3】病人与疾病的关系
func ExecPatientWithDisease(ruleid int64, patient *dsl.Patient, disease []*dsl.Disease, codelist []string) error {
	var err error
	dataContext := context.NewDataContext()
	dataContext.Add("patient", patient)
	diseaseMapStore := dsl.NewDiseaseMapStore()
	start1 := time.Now().UnixNano()
	for _, d := range disease {
		diseaseMapStore.AddReportMap(d)
	}
	if len(codelist) > 0 {
		diseaseMapStore.AddDiagnosisCodeList(codelist)
	}
	dataContext.Add("disease", diseaseMapStore)
	ruleBuilder := builder.NewRuleBuilder(dataContext)
	temp := Getrule(ruleid)
	if temp == "" {
		logging.Error("获取规则失败")
		return errors.New("获取规则失败")
	}
	err = ruleBuilder.BuildRuleFromString(temp)
	if err != nil {
		return err
	}
	end1 := time.Now().UnixNano()
	logging.Info(fmt.Sprintf("rules num:%d, load rules cost time:%d ns", len(ruleBuilder.Kc.RuleEntities), end1-start1))
	eng := engine.NewGengine()
	start := time.Now().UnixNano()
	err = eng.Execute(ruleBuilder, true)
	if err != nil {
		return err
	}
	end := time.Now().UnixNano()
	ms := (end - start)
	logging.Info(fmt.Sprintf("execute rule cost %d ns", ms))
	for _, msg := range patient.Msg {
		fmt.Printf("ID: %s, Name: %s\n", msg.MessageID, msg.MessageDesc)
	}
	return nil
}

// 【4】药品与检查之间的关系
func ExecMedicineWithImageReport(ruleid int64, patient *dsl.Patient, medicines []*dsl.Medicine, imageReport []*dsl.ImageReport) error {
	var err error
	dataContext := context.NewDataContext()
	dataContext.Add("patient", patient)
	medicineStore := dsl.NewMedicineStore()
	imageReportStore := dsl.NewImageReportMapStore()
	start1 := time.Now().UnixNano()
	for _, d := range medicines {
		medicineStore.AddMedicine(d)
	}
	for _, i := range imageReport {
		imageReportStore.AddImageReportForID(i)
	}
	for _, i := range imageReport {
		imageReportStore.AddImageReportForMedtechRptTypeID(i)
	}
	flasg := imageReportStore.CheckMedtechRptTypeID("123456789")
	fmt.Println(flasg)
	dataContext.Add("medicines", medicineStore)
	dataContext.Add("imageReport", imageReportStore)
	ruleBuilder := builder.NewRuleBuilder(dataContext)
	temp := Getrule(ruleid)
	if temp == "" {
		logging.Error("获取规则失败")
		return errors.New("获取规则失败")
	}
	err = ruleBuilder.BuildRuleFromString(temp)
	if err != nil {
		return err
	}
	end1 := time.Now().UnixNano()
	logging.Info(fmt.Sprintf("rules num:%d, load rules cost time:%d ns", len(ruleBuilder.Kc.RuleEntities), end1-start1))
	eng := engine.NewGengine()
	start := time.Now().UnixNano()
	err = eng.Execute(ruleBuilder, true)
	if err != nil {
		return err
	}
	end := time.Now().UnixNano()
	ms := (end - start)
	logging.Info(fmt.Sprintf("execute rule cost %d ns", ms))
	for _, msg := range patient.Msg {
		fmt.Printf("ID: %s, Name: %s\n", msg.MessageID, msg.MessageDesc)
	}
	return nil
}

// 【5】药品与医嘱
func ExecMedicineWithOrder(ruleid int64, patient *dsl.Patient, medicines []*dsl.Medicine, order *dsl.Order) error {
	var err error
	dataContext := context.NewDataContext()
	dataContext.Add("patient", patient)
	dataContext.Add("medicines", medicines)
	dataContext.Add("medicalOrder", order)
	start1 := time.Now().UnixNano()
	ruleBuilder := builder.NewRuleBuilder(dataContext)
	temp := Getrule(ruleid)
	if temp == "" {
		logging.Error("获取规则失败")
		return errors.New("获取规则失败")
	}
	//flags := order.IsExceedDays("10")
	//fmt.Println(flags)

	err = ruleBuilder.BuildRuleFromString(temp)
	if err != nil {
		return err
	}
	end1 := time.Now().UnixNano()
	logging.Info(fmt.Sprintf("rules num:%d, load rules cost time:%d ns", len(ruleBuilder.Kc.RuleEntities), end1-start1))
	eng := engine.NewGengine()
	start := time.Now().UnixNano()
	err = eng.Execute(ruleBuilder, true)
	if err != nil {
		return err
	}
	end := time.Now().UnixNano()
	ms := (end - start)
	logging.Info(fmt.Sprintf("execute rule cost %d ns", ms))
	for _, msg := range patient.Msg {
		fmt.Printf("ID: %s, Name: %s\n", msg.MessageID, msg.MessageDesc)
	}
	return nil
}

func ExecJusttest(ruleid int64, patient *dsl.Patient, medicines []*dsl.Medicine, testReports []*dsl.TestReportDetail) error {
	var err error
	defer func() {
		if r := recover(); r != nil {
			logging.Error("Recovered from panic:", r)
			err = errors.New(fmt.Sprintf("Recovered from panic: %v", r))
		}
	}()
	dataContext := context.NewDataContext()
	dataContext.Add("testReports", testReports)
	dataContext.Add("patient", patient)
	medicineStore := dsl.NewMedicineStore()
	testReportDetailStore := dsl.NewTestReportDetailStore()
	start1 := time.Now().UnixNano()
	for _, med := range medicines {
		medicineStore.AddMedicine(med)
		fmt.Println("med_id:", med.DrugID)
	}
	//传入报告的值
	for _, testReport := range testReports {
		testReportDetailStore.AddReport(testReport)
		fmt.Println("id:", testReport.ReportID)
	}
	dataContext.Add("medicines", medicineStore)
	dataContext.Add("testreports", testReportDetailStore)
	fmt.Println("drug_id:", medicineStore.GetMedicineByID("CDO6E01"))

	ruleBuilder := builder.NewRuleBuilder(dataContext)
	temp := Getrule(ruleid)
	if temp == "" {
		logging.Error("获取规则失败")
		return errors.New("获取规则失败")
	}
	err = ruleBuilder.BuildRuleFromString(temp)
	if err != nil {
		return err
	}
	end1 := time.Now().UnixNano()
	logging.Info(fmt.Sprintf("rules num:%d, load rules cost time:%d ns", len(ruleBuilder.Kc.RuleEntities), end1-start1))
	eng := engine.NewGengine()
	start := time.Now().UnixNano()
	err = eng.Execute(ruleBuilder, true)
	if err != nil {
		return err
	}
	end := time.Now().UnixNano()
	ms := (end - start)
	logging.Info(fmt.Sprintf("execute rule cost %d ns", ms))
	return nil
}

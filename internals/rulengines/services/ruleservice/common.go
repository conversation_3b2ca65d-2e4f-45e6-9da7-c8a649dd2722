package ruleservice

import (
	"kmbservice/dsl"
)

func AddmedicineStore(medicines []*dsl.Medicine) *dsl.MedicineStore {
	medicineStore := dsl.NewMedicineStore()
	for _, med := range medicines {
		medicineStore.AddMedicine(med)
	}
	medicineStore.AddMedicineList(medicines)
	return medicineStore
}

func AddDiseaseStore(disease []*dsl.Disease) *dsl.DiseaseStore {
	diseaseMapStore := dsl.NewDiseaseMapStore()
	for _, d := range disease {
		diseaseMapStore.AddReportMap(d)
	}
	return diseaseMapStore
}

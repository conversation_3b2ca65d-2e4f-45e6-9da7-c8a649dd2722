package routers

import (
	"github.com/gin-gonic/gin"
	"kmbservice/controller"
)

func InitRuleRouter() *gin.Engine {
	r := gin.New()
	r.Use(gin.Logger())
	r.Use(gin.Recovery())
	r.Use(controller.AuthMiddleware()) // 使用认证中间件
	gin.SetMode("")

	//科研智能体
	//kmb_take := r.Group("api")

	r.GET("/test", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "Router 测试！",
		})
	})
	r.POST("/dropDB", controller.DeldbHandler)
	return r
}

package routers

import (
	"github.com/gin-gonic/gin"
	handlerRuleExec "kmbservice/api/handler/rule-exec"
	closedloopHandler "kmbservice/api/handler/rule-maintain/closedloop"
	staticKnowledgeBaseHandler "kmbservice/api/handler/staticKnowledgeBase"
	staticKnowledgeBaseThirdHandler "kmbservice/api/handler/staticKnowledgeBase/third"
	"kmbservice/api/logic"
	ruleMaintainLogic "kmbservice/api/logic/rule-maintain"
	"kmbservice/api/logic/staticKnowledgeBase"
	staticKnowledgeBaseThirdLogic "kmbservice/api/logic/staticKnowledgeBase/third"
	"kmbservice/controller"
	"kmbservice/middleware"
	"kmbservice/pkg/logging"
	"kmbservice/pkg/setting"
	"os"
)

func InitRouter() {
	gin.SetMode(setting.ServerSetting.RunMode)
	router := gin.Default()
	router.Use(gin.Recovery())
	router.Use(gin.Logger())
	router.Use(middleware.Cors())
	//router.Use(apilog.ApiLogger())
	//旧接口
	{
		//规则引擎调用
		router.POST("/Loginin", controller.GetAuth)
		router.POST("/CheckRules", controller.CheckRules)
		//router.POST("/RuleExec", controller.RuleExec)
		//静态知识库
		router.POST("/DrugKnow", controller.GetDrugInfo)  //药物静态知识库
		router.POST("/GetCmeInfo", controller.GetCmeInfo) //方剂
		router.POST("/KnbDoc", controller.KnbDoc)         //病种
	}
	{
		router.POST("/test", logic.Test)
	}
	//规则
	ruleTake := router.Group("api")
	{
		ruleEngineGroup := ruleTake.Group("/engine")
		{
			ruleEngineGroup.POST("/rule_exec", handlerRuleExec.RuleExecHandler)
			ruleEngineGroup.POST("/obj_property/config/add", handlerRuleExec.RuleExecHandler)
		}
		//规则维护
		ruleMaintainGroup := ruleTake.Group("/rule_maintain")
		{
			ruleMaintainGroup.POST("/domain_object/get", ruleMaintainLogic.GetDomainObject)                       //领域对象获取
			ruleMaintainGroup.POST("/domain_object_stats/get", ruleMaintainLogic.GetDomainObjectStats)            //领域对象属性获取
			ruleMaintainGroup.POST("/noumenon/get", ruleMaintainLogic.GetNoumenon)                                //知识本体获取
			ruleMaintainGroup.POST("/noumenon_slot/get", ruleMaintainLogic.GetNoumenonSlot)                       //知识本体slot获取
			ruleMaintainGroup.POST("/rule/create/new", ruleMaintainLogic.RuleCreateNew)                           //规则创建
			ruleMaintainGroup.POST("/rule/edit", ruleMaintainLogic.RuleEdit)                                      //规则详情编辑
			ruleMaintainGroup.POST("/method/maintain", ruleMaintainLogic.RuleMethodMaintain)                      //规则方法维护接口
			ruleMaintainGroup.POST("/mapping_relation/get", ruleMaintainLogic.GetRuleContentMapping)              //规则映射关系查询
			ruleMaintainGroup.POST("/mapping_relation/edit", ruleMaintainLogic.EditRuleContentMapping)            //规则映射关系编辑
			ruleMaintainGroup.POST("/rule_detail/get", ruleMaintainLogic.GetDetailList)                           //查看规则数据列表
			ruleMaintainGroup.POST("/prompt_message/get", ruleMaintainLogic.GetPromptMessage)                     //编码信息获取
			ruleMaintainGroup.POST("/particulars_message/get", ruleMaintainLogic.GetParticularsMessage)           //查看规则详情
			ruleMaintainGroup.POST("/status/change", ruleMaintainLogic.RuleDeStatusChange)                        //规则状态【修改】
			ruleMaintainGroup.POST("/splicing_rule", ruleMaintainLogic.RuleSplicing)                              //规则组合创建
			ruleMaintainGroup.POST("/edit_rule", ruleMaintainLogic.RuleGroupEdit)                                 //规则组合编辑
			ruleMaintainGroup.POST("/rule_group/list", ruleMaintainLogic.GetRuleGroupList)                        //规则组列表获取
			ruleMaintainGroup.POST("/rule_group/detail_info", ruleMaintainLogic.RuleGroupDetailInfo)              //规则组详情查询
			ruleMaintainGroup.POST("/rule_group/sort_info", ruleMaintainLogic.RuleGroupSortInfo)                  //规则类别获取
			ruleMaintainGroup.POST("/rule_group/delete", ruleMaintainLogic.RuleGroupDelete)                       //规则删除
			ruleMaintainGroup.POST("/rule/sun/delete", ruleMaintainLogic.Rulesundelete)                           //删除明细
			ruleMaintainGroup.POST("/rule_detail/status/get", ruleMaintainLogic.RuleDetailStatusGet)              //状态查询
			ruleMaintainGroup.POST("/rule_detail/copy", ruleMaintainLogic.RuleDetailCopy)                         //规则数据克隆
			ruleMaintainGroup.POST("/rule_detail/version/change", ruleMaintainLogic.RuleDetailVersionChangeLogic) //规则数据历史版本切换
			ruleMaintainGroup.POST("/rule_detail/delete", ruleMaintainLogic.RuleDetailDelete)                     //规则数据删除
			//规则闭环
			ruleMaintainGroup.POST("/rule_group/closed_loop/list", closedloopHandler.RuleClosedLoopListHandler)     //规则列表获取
			ruleMaintainGroup.POST("/rule_group/closed_loop/detail", closedloopHandler.RuleClosedLoopDetailHandler) //规则详情查询
		}
		//静态知识库
		KnowledgeBase := ruleTake.Group("/static_knowledge_base")
		{
			KnowledgeBase.POST("/synthesis/search", staticKnowledgeBase.SynthesisSearch) //知识库综合搜索
			//知识库id查询
			KnowledgeBase.POST("/DrugKnow", staticKnowledgeBaseHandler.DrugKnowLedgeHandler)                                //药品
			KnowledgeBase.POST("/GetCmeInfo", staticKnowledgeBaseHandler.PrescriptionKnowLedgeHandler)                      //方剂
			KnowledgeBase.POST("/KnbDoc", staticKnowledgeBaseHandler.KnbDocKnowLedgeHandler)                                //病种
			KnowledgeBase.POST("/laboratory_test_examination", staticKnowledgeBaseHandler.LaboratoryTestExaminationHandler) //检验检查
			//第三方code编码查询
			KnowledgeBase.POST("/third/DrugKnow", staticKnowledgeBaseThirdLogic.DrugKnowLedgeThird)                                         //第三方药物静态知识库
			KnowledgeBase.POST("/third/KnbDoc", staticKnowledgeBaseThirdLogic.KnbDocKnowLedgeThird)                                         //第三方病种
			KnowledgeBase.POST("/third/GetCmeInfo", staticKnowledgeBaseThirdLogic.PrescriptionKnowLedgeThird)                               //方剂
			KnowledgeBase.POST("/third/laboratory_test_examination", staticKnowledgeBaseThirdHandler.LaboratoryTestExaminationThirdHandler) //检验检查
		}
		//院方医嘱、报告、执行记录数据同步
		HospitalDataSynchronization := ruleTake.Group("/data-synchronization")
		{
			HospitalDataSynchronization.POST("/order/western-medicine-order")
		}
		err := router.Run(":9893")
		if err != nil {
			logging.Error(err.Error())
			os.Exit(1)
		}
	}
}

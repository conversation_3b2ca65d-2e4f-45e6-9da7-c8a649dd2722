package models

import (
	"errors"
	"time"
)

type DksKnbDicMapping struct {
	SnowflakeID   int64     `gorm:"primaryKey;autoIncrement:false;column:snowflake_id;default:snowflake_id()"` // 使用 snowflake_id() 作为默认值
	SourceDicID   int64     `gorm:"column:source_dic_id" json:"source_dic_id"`
	TargetDicID   int64     `gorm:"column:target_dic_id" json:"target_dic_id"`
	RelationOID   int64     `gorm:"column:relation_oid" json:"relation_oid"`
	UpdateDate    time.Time `gorm:"column:update_date;default:CURRENT_TIMESTAMP"`
	SourceCodeSys int64     `gorm:"column:source_codesys"`
	TargetCodeSys int64     `gorm:"column:target_codesys"`
	CreateUser    string    `gorm:"column:create_user;size:255"`
	Status        int16     `gorm:"column:status"`
}

func (DksKnbDicMapping) TableName() string {
	return "dks_knb_dic_mapping"
}

func (d *DksKnbDicMapping) GetoneBySoidAndSystemId(source_dic_id, source_codesys int64) (*DksKnbDicMapping, error) {
	var mdm DksKnbDicMapping
	result := db.Where("source_dic_id = ? AND source_codesys = ?", source_dic_id, source_codesys).First(&mdm)
	if result.Error != nil {
		return &mdm, result.Error
	}
	return &mdm, result.Error
}

func (d *DksKnbDicMapping) GetoneBySoidAndOrientation(orientation string, id int64) (*DksKnbDicMapping, error) {
	var mdm DksKnbDicMapping
	if orientation == "st" {
		result := db.Where("source_dic_id = ?", id).First(&mdm)
		if result.Error != nil {
			return &mdm, result.Error
		}
		return &mdm, result.Error
	}
	result := db.Where("target_dic_id = ?", id).First(&mdm)
	if result.Error != nil {
		return &mdm, result.Error
	}
	return &mdm, result.Error
}

func (d *DksKnbDicMapping) BothwayGetoneBySoidAndSystemId(orientation string, DicId, DicCodesys int64) (*DksKnbDicMapping, error) {
	var mdm DksKnbDicMapping
	switch orientation {
	case "st":
		result := db.Where("source_dic_id = ? AND source_codesys = ?", DicId, DicCodesys).First(&mdm)
		if result.Error != nil {
			return &mdm, result.Error
		}
		return &mdm, result.Error

	case "ts":
		result := db.Where("target_dic_id = ? AND target_codesys = ?", DicId, DicCodesys).First(&mdm)
		if result.Error != nil {
			return &mdm, result.Error
		}
		return &mdm, result.Error
	default:
		return &mdm, errors.New("fail")
	}
}

func (d *DksKnbDicMapping) GetTargetDicMapBySourceIDs(orientation string, IDs []string) (map[int64][]int64, error) {
	result := make(map[int64][]int64)
	var mappings []DksKnbDicMapping
	if orientation == "st" {
		if err := db.Where("source_dic_id IN ?", IDs).Find(&mappings).Error; err != nil {
			return nil, err
		}
		for _, mapping := range mappings {
			result[mapping.SourceDicID] = append(result[mapping.SourceDicID], mapping.TargetDicID)
		}
		return result, nil
	}
	if err := db.Where("target_dic_id IN ?", IDs).Find(&mappings).Error; err != nil {
		return nil, err
	}
	for _, mapping := range mappings {
		result[mapping.TargetDicID] = append(result[mapping.TargetDicID], mapping.SourceDicID)
	}
	return result, nil
}

func (d *DksKnbDicMapping) GetTargetDicMapBySourceIDsANDCodeSystemID(orientation, codeSystemID string, IDs []string) (map[int64][]int64, error) {
	result := make(map[int64][]int64)
	var mappings []DksKnbDicMapping
	if orientation == "st" {
		if err := db.Where("source_dic_id IN ? AND source_codesys = ?", IDs, codeSystemID).Find(&mappings).Error; err != nil {
			return nil, err
		}
		for _, mapping := range mappings {
			result[mapping.SourceDicID] = append(result[mapping.SourceDicID], mapping.TargetDicID)
		}
		return result, nil
	}
	if err := db.Where("target_dic_id IN ?", IDs).Find(&mappings).Error; err != nil {
		return nil, err
	}
	for _, mapping := range mappings {
		result[mapping.TargetDicID] = append(result[mapping.TargetDicID], mapping.SourceDicID)
	}
	return result, nil
}

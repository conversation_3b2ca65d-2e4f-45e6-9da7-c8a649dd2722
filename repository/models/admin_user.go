package models

import (
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type AdminUser struct {
	UserId   int8       `gorm:"primaryKey;autoIncrement;comment:编码"  json:"user_id"`
	Username string     `json:"username" gorm:"size:64;comment:用户名"`
	Password string     `json:"-" gorm:"size:128;comment:密码"`
	NickName string     `json:"nickName" gorm:"size:128;comment:昵称"`
	Phone    string     `json:"phone" gorm:"size:11;comment:手机号"`
	RoleId   int        `json:"roleId" gorm:"size:20;comment:角色ID"`
	Salt     string     `json:"-" gorm:"size:255;comment:加盐"`
	Avatar   string     `json:"avatar" gorm:"size:255;comment:头像"`
	Sex      string     `json:"sex" gorm:"size:255;comment:性别"`
	Email    string     `json:"email" gorm:"size:128;comment:邮箱"`
	DeptId   int        `json:"deptId" gorm:"size:20;comment:部门"`
	PostId   int        `json:"postId" gorm:"size:20;comment:岗位"`
	Remark   string     `json:"remark" gorm:"size:255;comment:备注"`
	Status   string     `json:"status" gorm:"size:4;comment:状态"`
	DeptIds  []int      `json:"deptIds" gorm:"-"`
	PostIds  []int      `json:"postIds" gorm:"-"`
	RoleIds  []int      `json:"roleIds" gorm:"-"`
	Dept     *AdminDept `json:"dept"`
}

func LoginIn(userid int8, password string) (*AdminUser, error) {
	var user AdminUser
	db := GetDb()

	// 执行查询，检查错误
	err := db.Select("user_id").Where(&AdminUser{UserId: userid, Password: password}).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // 用户不存在时返回 nil
		}
		return nil, err // 其他错误返回错误信息
	}

	if user.UserId == 0 {
		return nil, nil // 用户不存在时返回 nil
	}

	return &user, nil // 用户存在时返回用户信息
}

func AddUser(user AdminUser) error {
	db := GetDb()
	ok := db.Create(&user)
	if ok.Error != nil {
		return ok.Error
	}
	return nil
}
func ExistUserById(userid int8) (bool, error) {
	var user AdminUser
	db := GetDb()
	db.Select("user_id").Where(&AdminUser{UserId: userid}).First(&user)
	if user.UserId > 0 {
		return true, nil
	}
	return false, nil

}
func GetUser(pageNum int, pagesize int, maps interface{}) (users []AdminUser) {
	db := GetDb()
	db.Where(maps).Offset(pageNum).Limit(pagesize).Find(&users)
	return
}
func GetUserCount(maps interface{}) (count int64) {
	if err := GetDb().Model(&AdminUser{}).Where(maps).Count(&count).Error; err != nil {
		return 0
	}
	return count
}
func ExistUserByName(name string) (bool, error) {
	var user AdminUser
	db := GetDb()
	db.Select("username").Where(&AdminUser{Username: name}).First(&user)
	if user.UserId > 0 {
		return true, nil
	}
	return false, nil
}
func (*AdminUser) TableName() string {
	return "admin_user"
}

func (e *AdminUser) GetId() interface{} {
	return e.UserId
}

// Encrypt 加密
func (e *AdminUser) Encrypt() (err error) {
	if e.Password == "" {
		return
	}

	var hash []byte
	if hash, err = bcrypt.GenerateFromPassword([]byte(e.Password), bcrypt.DefaultCost); err != nil {
		return
	} else {
		e.Password = string(hash)
		return
	}
}

/*
func (e *AdminUser) BeforeCreate(_ *gorm.DB) error {
	return e.Encrypt()
}

func (e *AdminUser) BeforeUpdate(_ *gorm.DB) error {
	var err error
	if e.Password != "" {
		err = e.Encrypt()
	}
	return err
}

func (e *AdminUser) AfterFind(_ *gorm.DB) error {
	e.DeptIds = []int{e.DeptId}
	e.PostIds = []int{e.PostId}
	e.RoleIds = []int{e.RoleId}
	return nil
}
*/

package models

import (
	"time"
)

type DMFDataSets struct {
	DataValueSOID        int64     `gorm:"column:data_value_soid;primaryKey;autoIncrement:true"`
	DataValueCode        string    `gorm:"column:data_value_code;size:64;index"`
	CodeSystemID         *int64    `gorm:"column:codesystem_id"`
	DataValueENMeaning   string    `gorm:"column:data_value_en_meaning;size:255"`
	DataValueCNMeaning   string    `gorm:"column:data_value_cn_meaning;size:255"`
	PYCode               string    `gorm:"column:py_code;size:128"`
	WBCode               string    `gorm:"column:wb_code;size:128"`
	DataValueDescription string    `gorm:"column:data_value_description;size:2000"`
	SuperDataValueSOID   *int64    `gorm:"column:super_data_value_soid"`
	Status               int32     `gorm:"column:status"`
	IsDelete             int64     `gorm:"column:is_delete"`
	CreateDate           time.Time `gorm:"column:create_date"`
	UpdateDate           time.Time `gorm:"column:update_date"`
	UpdateUser           string    `gorm:"column:update_user;size:255"`
	CreateUser           string    `gorm:"column:create_user;size:255"`
	ConceptID            *int64    `gorm:"column:concept_id"`
	ConceptDescription   string    `gorm:"column:concept_description;size:255"`
	MppID                string    `gorm:"column:mppid;size:50"`
	CreatedAt            time.Time `gorm:"autoCreateTime"`
	UpdatedAt            time.Time `gorm:"autoUpdateTime"`
}

func (DMFDataSets) TableName() string {
	return "dmf_data_sets"
}

// FindByCodeSystemID 方法用于根据 codesystem_id 查询符合条件的数据
func (d *DMFDataSets) FindByCodeSystemID(codesystemID int64) ([]DMFDataSets, error) {
	var datasets []DMFDataSets
	result := db.Where("codesystem_id = ?", codesystemID).Find(&datasets)
	if result.Error != nil {
		return nil, result.Error
	}
	return datasets, nil
}

// 根据 data_value_code 查 data_value_soid
func (d *DMFDataSets) FindByDataValueCodeANDCodeSystemId(data_value_code string, codesystem_id int64) (*DMFDataSets, error) {
	var datasets DMFDataSets
	result := db.Where("data_value_code = ? AND codesystem_id = ?", data_value_code, codesystem_id).First(&datasets)
	if result.Error != nil {
		return nil, result.Error
	}
	return &datasets, nil
}

func (d *DMFDataSets) FindByCnMeaningANDCodeSystemId(data_value_cn_meaning string, codesystem_id int64) (*DMFDataSets, error) {
	var datasets DMFDataSets
	result := db.Where("data_value_cn_meaning = ? AND codesystem_id = ?", data_value_cn_meaning, codesystem_id).First(&datasets)
	if result.Error != nil {
		return nil, result.Error
	}
	return &datasets, nil
}

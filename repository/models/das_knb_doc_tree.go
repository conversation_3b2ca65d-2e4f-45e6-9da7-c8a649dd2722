package models

import (
	"kmbservice/pkg/logging"
	"time"
)

type DasKnbDocTree struct {
	SnowflakeID int64     `gorm:"column:snowflake_id"`
	ParentID    int64     `gorm:"column:parent_id"`
	Level       int       `gorm:"column:level"`
	Description string    `gorm:"column:description"`
	Status      string    `gorm:"column:status"`
	TypeID      int64     `gorm:"column:type_id"`
	Contents    string    `gorm:"column:contents"`
	TypeNum     string    `gorm:"column:type_num"`
	TypeName    string    `gorm:"column:type_name"`
	Class1      int       `gorm:"column:class1"`
	Pycode      string    `gorm:"column:pycode"`
	CreatedBy   int       `gorm:"column:created_by"`
	CreatedAt   time.Time `gorm:"column:created_at"`
	UpdatedBy   int       `gorm:"column:updated_by"`
	UpdatedAt   time.Time `gorm:"column:updated_at"`
}

func (DasKnbDocTree) TableName() string {
	return "das_knb_doc_tree"
}

func (d *DasKnbDocTree) GetBydescriptionToFind(description string) (DasKnbDocTree, error) {
	var drug DasKnbDocTree
	result := db.Where("description = ? AND level <= ?", description, 4).Find(&drug)
	if result.Error != nil {
		logging.Error("查表[drug_specification]错误")
	}
	return drug, result.Error
}

func (d *DasKnbDocTree) GetDrugSpecificationItems(parent_id int64) ([]DasKnbDocTree, error) {
	var items []DasKnbDocTree
	err := db.Where("parent_id = ?", parent_id).Find(&items).Error
	return items, err
}

func (d *DasKnbDocTree) GetBySnowflakeID(snowflake_id string) (DasKnbDocTree, error) {
	var drug DasKnbDocTree
	err := db.Where("snowflake_id = ?", snowflake_id).Find(&drug).Error
	return drug, err
}

func (d *DasKnbDocTree) FindByDescriptionLike(name string) ([]DasKnbDocTree, error) {
	var resources []DasKnbDocTree
	if err := db.Where("description LIKE ?", "%"+name+"%").Find(&resources).Error; err != nil {
		return nil, err
	}
	return resources, nil
}

func (d *DasKnbDocTree) FindByDescriptionLikeANDLevel(name, level string) ([]DasKnbDocTree, error) {
	var resources []DasKnbDocTree
	if err := db.Where("description LIKE ? AND level = ?", "%"+name+"%", level).Find(&resources).Error; err != nil {
		return nil, err
	}
	return resources, nil
}

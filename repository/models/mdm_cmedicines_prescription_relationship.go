package models

type MdmCmedicinesPrescriptionRelationship struct {
	ID           int64 `gorm:"column:id;primaryKey;autoIncrement:false;default:snowflake_id();comment:'雪花id'"`
	TcmID        int64 `gorm:"column:tcm_id;not null;comment:'经方主表id'"`
	InstanceCode int64 `gorm:"column:instance_code;not null;comment:'方剂图数据库id'"`
}

func (MdmCmedicinesPrescriptionRelationship) TableName() string {
	return "mdm_cmedicines_prescription_relationship"
}

func (d *MdmCmedicinesPrescriptionRelationship) GetByInstanceCode(instance_code int64) (*MdmCmedicinesPrescriptionRelationship, error) {
	var result MdmCmedicinesPrescriptionRelationship
	if err := db.Where("instance_code = ?", instance_code).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

package models

import (
	"fmt"
)

type CombinedMedicine struct {
	ID           int64  `gorm:"column:id" json:"id"`
	BusinessCode string `gorm:"column:business_code" json:"business_code"`
	ItemName     string `gorm:"column:item_name" json:"item_name"`
	ItemType     string `gorm:"column:item_type" json:"item_type"`
	HospitalId   string `gorm:"column:hospital_id" json:"hospital_id"`
}

func (CombinedMedicine) TableName() string {
	return "combined_medicines"
}

func (c *CombinedMedicine) ConvertToMedicineMap(input map[int64][]int64) (map[int64][]CombinedMedicine, error) {
	result := make(map[int64][]CombinedMedicine)
	for key, idList := range input {
		var medicineList []CombinedMedicine
		for _, id := range idList {
			medicine, err := GetCombinedMedicineByID(id)
			if err != nil {
				return nil, fmt.Errorf("error: %d: %v", id, err)
			}
			medicineList = append(medicineList, *medicine)
		}
		result[key] = medicineList
	}
	return result, nil
}

func GetCombinedMedicineByID(id int64) (*CombinedMedicine, error) {
	var medicine CombinedMedicine
	if err := db.First(&medicine, id).Error; err != nil {
		return nil, fmt.Errorf("error: %d: %w", id, err)
	}
	return &medicine, nil
}

func (c *CombinedMedicine) GetCombinedMedicineByCodeAndHospital(businessCode string, hospitalId string) (*CombinedMedicine, error) {
	var medicine CombinedMedicine
	err := db.Where("business_code = ? AND hospital_id = ?", businessCode, hospitalId).First(&medicine).Error
	if err != nil {
		return nil, err
	}
	return &medicine, nil
}

func (c *CombinedMedicine) FindCombinedMedicine(id int64, business_code, item_name, item_type, hospital_id string) ([]CombinedMedicine, error) {
	query := db.Model(&CombinedMedicine{})
	if id != 0 {
		query = query.Where("id = ?", id)
	}
	if business_code != "" {
		query = query.Where("business_code = ?", business_code)
	}
	if item_name != "" {
		query = query.Where("item_name = ?", item_name)
	}
	if item_type != "" {
		query = query.Where("item_type = ?", item_type)
	}
	if hospital_id != "" {
		query = query.Where("hospital_id = ?", hospital_id)
	}
	var results []CombinedMedicine
	err := query.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

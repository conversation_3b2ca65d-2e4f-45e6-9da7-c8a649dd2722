package models

// 病种与诊断结果关系表
type DiseaseDiagnosisRelationship struct {
	PrimaryDisciplineCode        string `gorm:"column:一级学科代码;type:varchar(255)" json:"primary_discipline_code"`
	PrimaryDisciplineName        string `gorm:"column:一级学科名称;type:varchar(255)" json:"primary_discipline_name"`
	SecondaryDisciplineCode      string `gorm:"column:二级学科代码;type:varchar(255)" json:"secondary_discipline_code"`
	SecondaryDisciplineName      string `gorm:"column:二级学科名称;type:varchar(255)" json:"secondary_discipline_name"`
	DiseaseCode                  string `gorm:"column:病种代码;type:varchar(255)" json:"disease_code"`
	DiseaseName                  string `gorm:"column:病种;type:varchar(255)" json:"disease_name"`
	DiagnosisName                string `gorm:"column:诊断名称;type:varchar(255)" json:"diagnosis_name"`
	InternalDiagnosisCode        string `gorm:"column:内部诊断编码;type:varchar(255)" json:"internal_diagnosis_code"`
	ChinaNationalStandard2_0     string `gorm:"column:中国国标2.0诊断;type:varchar(255)" json:"ChinaNationalStandard2_0"`
	F10                          string `gorm:"column:f10;type:varchar(255)" json:"f10"`
	ICD10NationalClinicalEdition string `gorm:"column:ICD-10 全国临床版;type:varchar(255)" json:"icd10_national_clinical_edition"`
	ICD11                        string `gorm:"column:ICD-11;type:varchar(255)" json:"icd11"`
}

func (DiseaseDiagnosisRelationship) TableName() string {
	return "disease_diagnosis_relationship"
}

func (d *DiseaseDiagnosisRelationship) CreateDiseaseDiagnosisRelationship(relationship *DiseaseDiagnosisRelationship) error {
	return db.Create(relationship).Error
}

func (d *DiseaseDiagnosisRelationship) GetDiseaseDiagnosisRelationship(primaryDisciplineCode string, diseaseCode string) (*DiseaseDiagnosisRelationship, error) {
	var relationship DiseaseDiagnosisRelationship
	if err := db.First(&relationship, "一级学科代码 = ? AND 病种代码 = ?", primaryDisciplineCode, diseaseCode).Error; err != nil {
		return nil, err
	}
	return &relationship, nil
}

func (d *DiseaseDiagnosisRelationship) UpdateDiseaseDiagnosisRelationship(relationship *DiseaseDiagnosisRelationship) error {
	return db.Save(relationship).Error
}

func (d *DiseaseDiagnosisRelationship) DeleteDiseaseDiagnosisRelationship(primaryDisciplineCode string, diseaseCode string) error {
	return db.Delete(&DiseaseDiagnosisRelationship{}, "一级学科代码 = ? AND 病种代码 = ?", primaryDisciplineCode, diseaseCode).Error
}

// 病种或者诊断结果查询对应的诊断编码
func (d *DiseaseDiagnosisRelationship) GetChinaNationalStandard20ByDiseaseOrDiagnosis(searchTerm string) ([]string, error) {
	var results []string
	err := db.Table("disease_diagnosis_relationship").
		Where("\"病种\" = ? OR \"诊断名称\" = ?", searchTerm, searchTerm).
		Pluck("\"中国国标2.0诊断\"", &results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

// 诊断编码查病种
func (d *DiseaseDiagnosisRelationship) GetDiseaseByDiagnosisNo(diagnosisNo string) (string, error) {
	var record DiseaseDiagnosisRelationship
	err := db.Where("\"中国国标2.0诊断\" = ?", diagnosisNo).First(&record).Error
	if err != nil {
		return "", err
	}
	return record.DiseaseName, nil
}

// 诊断编码列表查询病种
func (d *DiseaseDiagnosisRelationship) GetDiseasesByDiagnosisNos(diagnosisNos []string) ([]string, error) {
	var records []DiseaseDiagnosisRelationship
	err := db.Where("\"中国国标2.0诊断\" IN ?", diagnosisNos).Find(&records).Error
	if err != nil {
		return nil, err
	}
	var disease_code_list []string
	for _, record := range records {
		disease_code_list = append(disease_code_list, record.DiseaseCode)
	}
	return disease_code_list, nil
}

// 病种或者诊断结果查询对应的记录
func (d *DiseaseDiagnosisRelationship) GetChinaNationalStandard20ByDiseaseOrDiagnosisList(searchTerm string) ([]DiseaseDiagnosisRelationship, error) {
	var results []DiseaseDiagnosisRelationship
	err := db.Table("disease_diagnosis_relationship").
		Where("\"病种\" = ? OR \"诊断名称\" = ?", searchTerm, searchTerm).
		Pluck("\"中国国标2.0诊断\"", &results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

// 病种查询记录（模糊查询）
//func (d *DiseaseDiagnosisRelationship) GetChinaNationalStandard20ByDiseaseOrDiagnosisEs(searchTerm string) ([]DiseaseDiagnosisRelationship, error) {
//	db := GetDbHBIDB()
//	var relationships []DiseaseDiagnosisRelationship
//	if err := db.Where("病种 = ?", searchTerm).Find(&relationships).Error; err != nil {
//		return nil, err
//	}
//	return relationships, nil
//}

// 诊断结果查询记录
func (d *DiseaseDiagnosisRelationship) GetChinaNationalStandard20ByDiseaseOrDiagnosisLiDE(searchTerm string) ([]DiseaseDiagnosisRelationship, error) {
	var relationships []DiseaseDiagnosisRelationship
	if err := db.Where("诊断名称 = ?", searchTerm).Find(&relationships).Error; err != nil {
		return nil, err
	}
	return relationships, nil
}

func (d *DiseaseDiagnosisRelationship) GetChinaNationalStandard20ByDiseaseOrDiagnosisLiDELike(searchTerm string) ([]DiseaseDiagnosisRelationship, error) {
	var relationships []DiseaseDiagnosisRelationship
	// 使用原生SQL进行模糊查询
	searchTerm = "%" + searchTerm + "%"
	if err := db.Raw("SELECT * FROM disease_diagnosis_relationship WHERE 诊断名称 LIKE ?", searchTerm).Scan(&relationships).Error; err != nil {
		return nil, err
	}
	return relationships, nil
}

func (d *DiseaseDiagnosisRelationship) GetChinaNationalStandard20ByDiseaseOrDiagnosisEs(searchTerm string) ([]DiseaseDiagnosisRelationship, error) {
	var relationships []DiseaseDiagnosisRelationship
	// 使用原生SQL进行模糊查询
	searchTerm = "%" + searchTerm + "%"
	query := "SELECT * FROM disease_diagnosis_relationship WHERE 病种 LIKE ?"
	if err := db.Raw(query, searchTerm).Scan(&relationships).Error; err != nil {
		return nil, err
	}
	return relationships, nil
}

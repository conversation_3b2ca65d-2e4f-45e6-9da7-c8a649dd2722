package models

type NewRuleTree struct {
	ID           int64 `gorm:"primaryKey"`
	ParentID     *int64
	RuleName     string `gorm:"not null"`
	IsActive     bool   `gorm:"not null;default:true"`
	DisplayOrder int64
}

func (NewRuleTree) TableName() string {
	return "rule_tree"
}

// 返回所有数据
func (n *NewRuleTree) GetAllRuleTrees() ([]NewRuleTree, error) {
	var ruleTrees []NewRuleTree
	result := db.Find(&ruleTrees)
	if result.Error != nil {
		return nil, result.Error
	}
	return ruleTrees, nil
}

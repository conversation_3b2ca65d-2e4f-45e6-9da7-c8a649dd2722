package models

type RuleCodeSystemConf struct {
	ConfID       int64  `gorm:"column:conf_id;primaryKey;autoIncrement:false;comment:主键ID"` // 主键ID
	DicID        int64  `gorm:"column:dic_id;comment:字典id"`                                 // 字典id
	PropertyDesc string `gorm:"column:property_desc;size:255;comment:描述"`                   // 描述
	ObjName      string `gorm:"column:obj_name;size:255;comment:规则对象名称"`                    // 规则对象名称
	CorpName     string `gorm:"column:corp_name;size:255;comment:合作方描述"`                    // 合作方描述
	ObjDesc      string `gorm:"column:obj_desc;size:255;comment:规则对象描述"`                    // 规则对象描述
	CorpID       string `gorm:"column:corp_id;size:255;comment:合作方id"`                      // 合作方id
	DicName      string `gorm:"column:dic_name"`                                            //字典名称（属性字段名称）
}

func (RuleCodeSystemConf) TableName() string {
	return "rule_code_system_conf"
}

func (r *RuleCodeSystemConf) FindByDataValueCodeANDCodeSystemId(corp_id, obj_name, dic_name string) (*RuleCodeSystemConf, error) {
	var conf RuleCodeSystemConf
	result := db.Where("corp_id = ? AND obj_name = ? AND dic_name = ?", corp_id, obj_name, dic_name).First(&conf)
	if result.Error != nil {
		return nil, result.Error
	}
	return &conf, nil
}

func (r *RuleCodeSystemConf) FindRuleCodeSystemConf(dic_id int64, corp_id, obj_name, dic_name string) ([]RuleCodeSystemConf, error) {
	query := db.Model(&RuleCodeSystemConf{})
	if dic_id != 0 {
		query = query.Where("dic_id = ?", dic_id)
	}
	if corp_id != "" {
		query = query.Where("corp_id = ?", corp_id)
	}
	if obj_name != "" {
		query = query.Where("obj_name = ?", obj_name)
	}
	if dic_name != "" {
		query = query.Where("dic_name = ?", dic_name)
	}
	var results []RuleCodeSystemConf
	err := query.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

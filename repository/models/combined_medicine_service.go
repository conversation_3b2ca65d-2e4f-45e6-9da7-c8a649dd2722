package models

// 医嘱药品视图
type CombinedMedicineService struct {
	ServiceID               int64  `gorm:"column:service_id"`
	ServiceName             string `gorm:"column:service_name"`
	ServiceCode             string `gorm:"column:service_code"`
	MnemonicCode            string `gorm:"column:mnemonic_code"`
	Organization            string `gorm:"column:organization"`
	ClinicalServiceCode     string `gorm:"column:clinical_service_code"`
	ClinicalServiceCategory string `gorm:"column:clinical_service_category"`
	ServiceSpecification    string `gorm:"column:service_specification"`
	ServiceUnit             string `gorm:"column:service_unit"`
	Code                    string `gorm:"column:systemcodes"`
	DosageForm              string `gorm:"column:dosage_form"`
	Specification           string `gorm:"column:specification"`
	RecommendedDosage       string `gorm:"column:recommended_dosage"`
	Type                    int    `gorm:"column:type"`
}

func (CombinedMedicineService) TableName() string {
	return "combined_medicine_service"
}

func (c *CombinedMedicineService) GetDmfDataSetByCodeSystemID(rtype string) ([]CombinedMedicineService, error) {
	var results []CombinedMedicineService
	if err := db.Where("type = ?", rtype).Find(&results).Error; err != nil {
		return results, nil
	}
	return results, nil
}

func (c *CombinedMedicineService) GetDmfDataSetByCodeSystemIDPlus(rtype int, serviceName string) ([]CombinedMedicineService, error) {
	var relationships []CombinedMedicineService
	// 使用原生SQL进行模糊查询
	serviceName = "%" + serviceName + "%"
	if err := db.Raw("SELECT * from combined_medicine_service WHERE type=? AND service_name LIKE ?", rtype, serviceName).Scan(&relationships).Error; err != nil {
		return nil, err
	}
	return relationships, nil
}

// 通过检索码查询
func (c *CombinedMedicineService) GetDmfDataSetByCodeSystemIDJsCode(rtype int, jscode string) ([]CombinedMedicineService, error) {
	var relationships []CombinedMedicineService
	// 使用原生SQL进行模糊查询
	jscode = "%" + jscode + "%"
	if err := db.Raw("SELECT * from combined_medicine_service WHERE type=? AND mnemonic_code LIKE ?", rtype, jscode).Scan(&relationships).Error; err != nil {
		return nil, err
	}
	return relationships, nil
}

// 拼音检索
func (c *CombinedMedicineService) GetPyzt(rtype int16, serviceName string) (string, error) {
	var results string
	if err := db.Raw(`SELECT "usp_yy_getpyzt"(?, ?);`, serviceName, rtype).Scan(&results).Error; err != nil {
		return results, err
	}
	return results, nil
}

// service_id查询
func (c *CombinedMedicineService) GetByServiceID(service_id int64) (*CombinedMedicineService, error) {
	var results CombinedMedicineService
	if err := db.Where("service_id = ?", service_id).Find(&results).Error; err != nil {
		return &results, nil
	}
	return &results, nil
}

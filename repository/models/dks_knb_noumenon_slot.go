package models

import "time"

type DksKnbNoumenonSlot struct {
	SlotName                        string    `gorm:"column:slot_name;type:text;not null;comment:'属性名'"`
	PyCode                          string    `gorm:"column:py_code;size:32;comment:'拼音'"`
	WbCode                          string    `gorm:"column:wb_code;size:32;comment:'五笔'"`
	SlotDesc                        string    `gorm:"column:slot_desc;size:128;comment:'属性描述'"`
	CodingSystemSoid                int64     `gorm:"column:coding_system_soid;comment:'属性值的代码系统'"`
	MaxNumber                       float64   `gorm:"column:max_number;type:numeric(19,0);comment:'最大值'"`
	MinNumber                       float64   `gorm:"column:min_number;type:numeric(19,0);comment:'最小值'"`
	DefaultNumber                   string    `gorm:"column:default_number;size:32;comment:'默认值'"`
	DataType                        int64     `gorm:"column:data_type;type:numeric(19,0);comment:'数据类型 ( 0: 数字 1：字符 2 可以选择'"`
	Unit                            float64   `gorm:"column:unit;type:numeric(19,0);comment:'单位'"`
	Basic                           float64   `gorm:"column:basic;type:numeric(19,0);comment:'基线值'"`
	SlotOrder                       float64   `gorm:"column:slot_order;type:numeric(19,0);comment:'属性顺序'"`
	CreateDate                      time.Time `gorm:"column:create_date;comment:'创建日期'"`
	UpdateDate                      time.Time `gorm:"column:update_date;comment:'更新日期'"`
	UpdateUser                      int64     `gorm:"column:update_user;comment:'更新用户'"`
	Status                          int       `gorm:"column:status;comment:'状态'"`
	IsDelete                        int       `gorm:"column:is_delete;comment:'删除'"`
	Lable                           string    `gorm:"column:lable;size:64;comment:'标签'"`
	ReferenceUpperLimit             float64   `gorm:"column:reference_upper_limit;type:numeric(8,2);comment:'引用上限'"`
	ReferenceLowerLimit             float64   `gorm:"column:reference_lower_limit;type:numeric(8,2);comment:'引用下限'"`
	ReferenceQualitativeMaintenance float64   `gorm:"column:reference_qualitative_maintenance;type:numeric(19,0);comment:'引用定性'"`
	CreateUser                      int64     `gorm:"column:create_user;comment:'创建用户'"`
	NoumenonID                      int64     `gorm:"column:noumenon_id;comment:'本体ID'"`
	ConceptID                       int64     `gorm:"column:concept_id;comment:'slot对应的概念ID'"`
	GlobalID                        int64     `gorm:"column:global_id;primaryKey;autoIncrement:false;default:snowflake_id();comment:'属性OID'"`
	SlotKey                         string    `gorm:"column:slot_key;size:255;comment:'slot 本体英文名称，用于规则使用'"`
	SlotCode                        string    `gorm:"column:slot_code;size:255;comment:'Slot 人工编码'"`
}

// TableName 设置表名
func (DksKnbNoumenonSlot) TableName() string {
	return "dks_knb_noumenon_slot"
}

func (d *DksKnbNoumenonSlot) FindByGlobalID(globalID int64) (*DksKnbNoumenonSlot, error) {
	var noumenon DksKnbNoumenonSlot
	result := db.Where("global_id = ?", globalID).First(&noumenon)
	if result.Error != nil {
		return nil, result.Error
	}
	return &noumenon, nil
}

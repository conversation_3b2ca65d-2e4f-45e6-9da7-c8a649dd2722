package models

type DrugSpecificationItem struct {
	ItemID        int64  `gorm:"primaryKey;autoIncrement:false"` // Primary key, auto-incrementing is disabled
	Type          int16  `gorm:"type:smallint"`
	Name          string `gorm:"type:varchar(255)"`
	Specification string `gorm:"type:text"`
	SpeciID       int64  `gorm:"type:int8"`  // Foreign key to drug_specification table
	JSONB         []byte `gorm:"type:jsonb"` // Use a custom JSONB type
}

func (DrugSpecificationItem) TableName() string {
	return "drug_specification_item"
}

func (d *DrugSpecificationItem) GetDrugSpecificationItems(speciID int64) ([]DrugSpecificationItem, error) {
	var items []DrugSpecificationItem
	err := db.Where("speci_id = ?", speciID).Find(&items).Error
	return items, err
}

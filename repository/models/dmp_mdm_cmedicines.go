package models

import (
	"kmbservice/pkg/logging"
	"time"
)

type DmpMdmCMedicine struct {
	Name                   string    `gorm:"column:name"`
	OriginOfPrescription   string    `gorm:"column:originofprescription"`
	Formulas               string    `gorm:"column:formulas"`
	OriginalPrescription   string    `gorm:"column:originalprescription"`
	DosageForm             string    `gorm:"column:dosageform"`
	PreparationMethod      string    `gorm:"column:preparationmethod"`
	Route                  string    `gorm:"column:route"`
	Mechanism              string    `gorm:"column:mechanism"`
	Indications            string    `gorm:"column:indications"`
	EfficacyClassification string    `gorm:"column:efficacyclassification"`
	Effect                 string    `gorm:"column:effect"`
	Dialectical            string    `gorm:"column:dialectical"`
	AdditionAndSubtraction string    `gorm:"column:additionandsubtraction"`
	SquareSolution         string    `gorm:"column:squaresolution"`
	ClinicalApplication    string    `gorm:"column:clinicalapplication"`
	MedicationPrecautions  string    `gorm:"column:medicationprecautions"`
	DecoctingMethod        string    `gorm:"column:decoctingmethod"`
	GroupOID               int64     `gorm:"column:group_oid"`
	TCMID                  int64     `gorm:"column:tcm_id;primaryKey;autoIncrement:false"`
	CreateUser             int64     `gorm:"column:create_user"`
	Status                 string    `gorm:"column:status"`
	PreType                string    `gorm:"column:pretype"`
	ByStr                  string    `gorm:"column:bystr"`
	CreatedAt              time.Time `gorm:"column:created_at"`
	FunClass1              string    `gorm:"column:fun_class1"`
	FunClass2              string    `gorm:"column:fun_class2"`
}

func (DmpMdmCMedicine) TableName() string {
	return "dmp_mdm_cmedicines"
}

func (d *DmpMdmCMedicine) GetByName(name string) (DmpMdmCMedicine, error) {
	var drug DmpMdmCMedicine
	db := GetDb()
	result := db.Where("name = ?", name).Find(&drug)
	if result.Error != nil {
		logging.Error("查表[drug_specification]错误")
	}
	return drug, result.Error
}

func (d *DmpMdmCMedicine) GetByTCMID(tcm_id string) (DmpMdmCMedicine, error) {
	var drug DmpMdmCMedicine
	result := db.Where("tcm_id = ?", tcm_id).Find(&drug)
	return drug, result.Error
}

func (d *DmpMdmCMedicine) FindByNameLike(name string) ([]DmpMdmCMedicine, error) {
	var resources []DmpMdmCMedicine
	if err := db.Where("name LIKE ?", "%"+name+"%").Find(&resources).Error; err != nil {
		return nil, err
	}
	return resources, nil
}

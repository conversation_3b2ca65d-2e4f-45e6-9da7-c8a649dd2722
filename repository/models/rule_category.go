package models

type NewRuleCategory struct {
	ID                  int64  `gorm:"primaryKey"`
	CategoryName        string `gorm:"not null"`
	CategoryDescription string
}

func (NewRuleCategory) TableName() string {
	return "rule_category"
}

// 返回所有数据
func (n *NewRuleCategory) GetAllRuleCategory() ([]NewRuleCategory, error) {
	var ruleCategory []NewRuleCategory
	result := db.Find(&ruleCategory)
	if result.Error != nil {
		return nil, result.Error
	}
	return ruleCategory, nil
}

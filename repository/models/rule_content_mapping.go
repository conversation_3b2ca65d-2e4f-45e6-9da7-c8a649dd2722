package models

import (
	"gorm.io/gorm"
)

// RuleContentMapping 表对应的结构体
type RuleContentMapping struct {
	RuleContentMappingID  int64   `gorm:"primaryKey;column:rule_content_mapping_id"`
	RuleGroupID           int64   `gorm:"column:rule_group_id"`
	RuleContentDetailID   int64   `gorm:"column:rule_content_detail_id"`
	RuleGroupName         string  `gorm:"column:rule_group_name"`
	RuleContentDetailName string  `gorm:"column:rule_content_detail_name"`
	RuleGroupVersion      float64 `gorm:"column:rule_group_version"`
	RuleDetailVersion     float64 `gorm:"column:rule_detail_version"`
	SerialNumber          int64   `gorm:"column:serial_number"`
	RuleMainVersion       int64   `gorm:"column:rule_main_version" json:"rule_main_version"`               // 规则主版本号
	RuleSubVersion        int64   `gorm:"column:rule_sub_version" json:"rule_sub_version"`                 // 规则次版本号
	RuleDetailMainVersion int64   `gorm:"column:rule_detail_main_version" json:"rule_detail_main_version"` // 规则数据主版本号
	RuleDetailSubVersion  int64   `gorm:"column:rule_detail_sub_version" json:"rule_detail_sub_version"`   // 规则数据次版本号
}

func (RuleContentMapping) TableName() string {
	return "rule_content_mapping"
}

func (r *RuleContentMapping) GetAll() ([]RuleContentMapping, error) {
	var mappings []RuleContentMapping
	result := db.Find(&mappings)
	return mappings, result.Error
}

func (r *RuleContentMapping) GetByRuleGroupID(ruleGroupID int64) ([]RuleContentMapping, error) {
	var mappings []RuleContentMapping
	result := db.Where("rule_group_id = ?", ruleGroupID).Find(&mappings)
	return mappings, result.Error
}

func (r *RuleContentMapping) BatchInsertRuleContentMappings(mappings []RuleContentMapping) error {
	// 批量插入数据
	if err := db.Create(&mappings).Error; err != nil {
		return err
	}
	return nil
}

func (r *RuleContentMapping) BatchInsertRuleContentMappingsTx(tx *gorm.DB, mappings []RuleContentMapping) error {
	// 批量插入数据
	if err := tx.Create(&mappings).Error; err != nil {
		return err
	}
	return nil
}

func (r *RuleContentMapping) FindByIDAndVersion(rule_group_id int64, version float64) ([]RuleContentMapping, error) {
	var ruleContent []RuleContentMapping
	result := db.Where("rule_group_id = ? AND rule_group_version = ?", rule_group_id, version).Find(&ruleContent)
	if result.Error != nil {
		return nil, result.Error
	}
	return ruleContent, nil
}

func (r *RuleContentMapping) FindByIDAndAllVersion(rule_group_id, rule_main_version, rule_sub_version int64) ([]RuleContentMapping, error) {
	var ruleContent []RuleContentMapping
	result := db.Where("rule_group_id = ? AND rule_main_version = ? AND rule_sub_version = ?", rule_group_id, rule_main_version, rule_sub_version).Find(&ruleContent)
	if result.Error != nil {
		return nil, result.Error
	}
	return ruleContent, nil
}

func (r *RuleContentMapping) FindByIDAndAllMainSubVersion(rule_group_id, rule_main_version, rule_sub_version, rule_detail_main_version, rule_detail_sub_version int64) ([]RuleContentMapping, error) {
	var ruleContent []RuleContentMapping
	result := db.Where("rule_group_id = ? AND rule_main_version = ? AND rule_sub_version = ? AND rule_detail_main_version = ? AND rule_detail_sub_version = ?", rule_group_id, rule_main_version, rule_sub_version, rule_detail_main_version, rule_detail_sub_version).Find(&ruleContent)
	if result.Error != nil {
		return nil, result.Error
	}
	return ruleContent, nil
}

func (r *RuleContentMapping) FindByDetailIDAndVersion(rule_content_detail_id, rule_detail_main_version, rule_detail_sub_version int64) ([]RuleContentMapping, error) {
	var ruleContent []RuleContentMapping
	result := db.Where("rule_content_detail_id = ? AND rule_detail_main_version = ? AND rule_detail_sub_version = ?", rule_content_detail_id, rule_detail_main_version, rule_detail_sub_version).Find(&ruleContent)
	if result.Error != nil {
		return nil, result.Error
	}
	return ruleContent, nil
}

package models

import (
	"encoding/json"
	"fmt"
	"gorm.io/gorm"
	"kmbservice/api/enum"
	"strconv"
	"time"
)

type RuleContentDetail struct {
	DetailID          int64           `gorm:"column:detail_id;comment:明细ID"`
	ClassficationID   int64           `gorm:"column:classfication_id;comment:分类ID"`
	RuleName          string          `gorm:"not null;column:rule_name;comment:规则名称"`
	RuleDescription   string          `gorm:"column:rule_description;comment:规则描述"`
	UsageScenario     string          `gorm:"size:64;column:usage_scenario;comment:规则场景"`
	Status            int64           `gorm:"column:status;comment:状态"` //1：可用；0：不可用
	RuleContent       string          `gorm:"column:rule_content;comment:规则内容"`
	Revision          int64           `gorm:"column:revision;comment:乐观锁"`
	CreatedAt         int64           `gorm:"column:created_at;comment:创建时间"`
	CreatedBy         string          `gorm:"column:created_by;comment:创建人"`
	UpdatedAt         int64           `gorm:"column:updated_at;comment:更新时间"`
	UpdatedBy         string          `gorm:"column:updated_by;comment:更新人"`
	DeletedAt         int64           `gorm:"column:deleted_at;comment:删除时间"`
	DeletedBy         string          `gorm:"column:deleted_by;comment:删除人"`
	DetailVersionID   int64           `gorm:"column:detail_version_id;primaryKey"`
	Particulars       string          `gorm:"column:particulars;comment:规则详情"`
	Prompt            string          `gorm:"column:prompt;comment:提示语"`
	WarningType       string          `gorm:"column:warning_type;comment:警示类型"`
	ReqDataStruct     json.RawMessage `gorm:"column:req_data_struct;comment:入参结构"`
	Version           float64         `gorm:"column:version;type:numeric"` // 版本号，类型为数值
	ISUse             int64           `gorm:"column:is_use;comment:是否可用"`
	MainVersion       int64           `gorm:"column:main_version" json:"main_version"`                 // 主版本号
	SubVersion        int64           `gorm:"column:sub_version" json:"sub_version"`                   // 次版本号
	ReqRuleBodyStruct json.RawMessage `gorm:"column:req_rule_body_struct" json:"req_rule_body_struct"` //规则数据内容保存
	PublishingStatus  string          `gorm:"column:publishing_status;comment:发布状态" json:"publishing_status"`
	PublishingReject  int64           `gorm:"column:publishing_reject;comment:驳回标识" json:"publishing_reject"`
	IsDel             int64           `gorm:"column:is_del" json:"is_del"`
	RejectReason      string          `gorm:"column:reject_reason" json:"reject_reason"` //驳回原因
}

func (r *RuleContentDetail) TableName() string {
	return "rule_content_detail"
}

func (r *RuleContentDetail) QueryDataExceptGivenDetailIDs(detailIDs []int64) ([]RuleContentDetail, error) {
	var results []RuleContentDetail
	query := db.Not("detail_id IN (?)", detailIDs)
	err := query.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (r *RuleContentDetail) FindByDetailIDs(detailIDs []string) ([]RuleContentDetail, error) {
	var ruleContents []RuleContentDetail
	if err := db.Where("detail_id IN (?)", detailIDs).Find(&ruleContents).Error; err != nil {
		return nil, err
	}
	return ruleContents, nil
}

func (r *RuleContentDetail) FindByStatus(status int64) ([]RuleContentDetail, error) {
	var ruleContents []RuleContentDetail
	if err := db.Where("status = ?", status).Find(&ruleContents).Error; err != nil {
		return nil, err
	}
	return ruleContents, nil
}

func (r *RuleContentDetail) FindByStatusLimit(status int64, offset, limit int) ([]RuleContentDetail, error) {
	var ruleContents []RuleContentDetail
	if err := db.Where("status = ?", status).Limit(limit).Offset(offset).Find(&ruleContents).Error; err != nil {
		return nil, err
	}
	return ruleContents, nil
}

func (r *RuleContentDetail) FindActiveDetailsByIDs(detailIDs []int64, rule_detail_name string) ([]RuleContentDetail, error) {
	var details []RuleContentDetail
	if rule_detail_name != "" {
		if err := db.Where("detail_id IN (?) AND is_use = ? OR rule_name LIKE ? OR rule_description LIKE ?", detailIDs, 1, "%"+rule_detail_name+"%", "%"+rule_detail_name+"%").Find(&details).Error; err != nil {
			return nil, err
		}
		return details, nil
	} else {
		if err := db.Where("detail_id IN (?) AND is_use = ?", detailIDs, 1).Find(&details).Error; err != nil {
			return nil, err
		}
		return details, nil
	}
}

func (r *RuleContentDetail) FindActiveDetailsByIDsEdit(detailIDs []int64, rule_detail_name string) ([]RuleContentDetail, error) {
	var details []RuleContentDetail
	if rule_detail_name != "" {
		if err := db.Where("detail_id IN (?) AND is_use = ? OR rule_name LIKE ? OR rule_description LIKE ?", detailIDs, 1, "%"+rule_detail_name+"%", "%"+rule_detail_name+"%").Find(&details).Error; err != nil {
			return nil, err
		}
		return details, nil
	} else {
		for _, detailID := range detailIDs {
			var detail RuleContentDetail
			if err := db.Where("detail_id = ? AND is_use = ?", detailID, 1).Find(&detail).Error; err != nil {
				continue
			}
			details = append(details, detail)
		}
		return details, nil
	}
}

//	if ruleName != "" {
//		query = query.Where("rule_name LIKE ? OR rule_description LIKE ? AND is_use = ?", "%"+ruleName+"%", "%"+ruleName+"%", 1)
//	}

// 通过 detail_version_id 查询
func (r *RuleContentDetail) FindByDetailId(detail_id int64) (RuleContentDetail, error) {
	var datasets RuleContentDetail
	result := db.Where("detail_id = ? AND is_use = ?", detail_id, 1).Find(&datasets)
	if result.Error != nil {
		return datasets, result.Error
	}
	return datasets, nil
}

func (r *RuleContentDetail) FindByDetailIdANDAllVersion(detail_id, main_version, sub_version int64) (RuleContentDetail, error) {
	var datasets RuleContentDetail
	result := db.Where("detail_id = ? AND main_version = ? AND sub_version = ?", detail_id, main_version, sub_version).Find(&datasets)
	if result.Error != nil {
		return datasets, result.Error
	}
	return datasets, nil
}

func (r *RuleContentDetail) FindByDetailVersionID(detail_version_id int64) (RuleContentDetail, error) {
	var datasets RuleContentDetail
	result := db.Where("detail_version_id = ?", detail_version_id).Find(&datasets)
	if result.Error != nil {
		return datasets, result.Error
	}
	return datasets, nil
}

// 事务创建
func (n *RuleContentDetail) CreateRuleDetailTx(tx *gorm.DB, RuleGroupName, Ruledetail, Ruledesc, Particulars, p, w string, reqstruct, rulebody []byte, status int64) (int64, int64, error) {
	rule_detail_id := GetSnowflakeID()
	if tx == nil {
		return 0, 0, fmt.Errorf("failed to get database connection")
	}
	n.DetailID = rule_detail_id
	n.RuleName = RuleGroupName
	n.RuleDescription = Ruledesc
	n.RuleContent = Ruledetail
	n.Particulars = Particulars
	n.UpdatedAt = time.Now().Unix()
	n.Status = status
	n.DetailVersionID = GetSnowflakeID()
	n.Prompt = p
	n.WarningType = w
	n.ReqDataStruct = reqstruct
	n.MainVersion = 1
	n.SubVersion = 0
	n.ISUse = 0
	n.ReqRuleBodyStruct = rulebody
	n.PublishingStatus = "SAVED"
	return rule_detail_id, n.DetailVersionID, tx.Create(n).Error
}

// 事务创建（用于规则详情的编辑）
func (n *RuleContentDetail) EditRuleDetailTx(tx *gorm.DB, Detail int64, RuleGroupName, Ruledetail, Ruledesc, Particulars, p, w string, reqstruct, rulebody []byte, mainVersion, subVersion, status int64) (int64, int64, error) {
	//rulegroupid := GetSnowflakeID()
	if tx == nil {
		return 0, 0, fmt.Errorf("failed to get database connection")
	}
	n.DetailID = Detail
	n.RuleName = RuleGroupName
	n.RuleDescription = Ruledesc
	n.RuleContent = Ruledetail
	n.Particulars = Particulars
	n.UpdatedAt = time.Now().Unix()
	n.Status = status
	n.Prompt = p
	n.WarningType = w
	n.ReqDataStruct = reqstruct
	n.MainVersion = mainVersion
	n.SubVersion = subVersion
	n.ISUse = 1
	n.ReqRuleBodyStruct = rulebody
	n.PublishingStatus = enum.SAVED
	return Detail, n.DetailVersionID, tx.Create(n).Error
}

// 通过规则名称、描述、对象以及更新时间查询
func (n *RuleContentDetail) QueryRuleDetails(ruleName, startDate, endDate string, offset, limit int) ([]RuleContentDetail, error) {
	if db == nil {
		return nil, fmt.Errorf("failed to get database connection")
	}
	// 构建查询条件
	query := db.Model(&RuleContentDetail{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_description LIKE ?", "%"+ruleName+"%", "%"+ruleName+"%")
	}
	if startDate != "" && endDate != "" {
		query = query.Where("updated_at >= ? AND updated_at <= ?", startDate, endDate)
	}
	// 添加分页逻辑
	query = query.Offset(offset).Limit(limit)
	// 执行查询
	var results []RuleContentDetail
	err := query.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (n *RuleContentDetail) QueryRuleDetailsIfUse(ruleName, startDate, endDate, status, is_use string, excludeDetailIds []int64, offset, limit int) ([]RuleContentDetail, error) {
	// 构建查询条件
	intuse, _ := strconv.ParseInt(is_use, 10, 64)
	query := db.Model(&RuleContentDetail{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_description LIKE ?", "%"+ruleName+"%", "%"+ruleName+"%")
	}
	if startDate != "" && endDate != "" {
		query = query.Where("updated_at >= ? AND updated_at <= ?", startDate, endDate)
	}
	if is_use != "" {
		query = query.Where("is_use = ?", intuse)
	}
	if len(excludeDetailIds) > 0 {
		query = query.Not("detail_id IN (?)", excludeDetailIds)
	}
	query = query.Where("is_del = ?", 0)
	// 添加分页逻辑
	//query = query.Order("updated_at " + "desc").Offset(offset).Limit(limit)
	query = query.Order("detail_id ASC, is_use DESC, main_version DESC, updated_at ASC").Offset(offset).Limit(limit)
	// 执行查询
	var results []RuleContentDetail
	var err error
	switch status {
	case "":
		err = query.Find(&results).Error

	case "0":
		err = query.Where("status = ?", 0).Find(&results).Error

	case "1":
		err = query.Where("status = ?", 1).Find(&results).Error
	}
	if err != nil {
		return nil, err
	}
	return results, nil
}

// 查询所有数据
func (n *RuleContentDetail) GetAllRuleContentDetails() ([]RuleContentDetail, error) {
	var results []RuleContentDetail
	err := db.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (n *RuleContentDetail) QueryRuleDetailsSe(ruleName, startDate, endDate string, detailIdList []int64, offset, limit int) ([]RuleContentDetail, error) {
	if db == nil {
		return nil, fmt.Errorf("failed to get database connection")
	}
	// 构建查询条件
	query := db.Model(&RuleContentDetail{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_description LIKE ?", "%"+ruleName+"%", "%"+ruleName+"%")
	}
	if len(detailIdList) > 0 {
		query = query.Where("detail_id IN (?)", detailIdList)
	}
	if startDate != "" && endDate != "" {
		query = query.Where("updated_at >= ? AND updated_at <= ?", startDate, endDate)
	}
	// 添加分页逻辑
	query = query.Offset(offset).Limit(limit)
	// 执行查询
	var results []RuleContentDetail
	err := query.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

// 添加过滤是否可用
func (n *RuleContentDetail) QueryRuleDetailsSeIfUse(ruleName, startDate, endDate string, detailIdList, excludeDetailIds []int64, offset, limit int, status, publishing_status, is_use string) ([]RuleContentDetail, error) {
	if db == nil {
		return nil, fmt.Errorf("failed to get database connection")
	}
	// 构建查询条件
	var err error
	query := db.Model(&RuleContentDetail{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_description LIKE ?", "%"+ruleName+"%", "%"+ruleName+"%")
	}
	if len(detailIdList) > 0 {
		query = query.Where("detail_id IN (?)", detailIdList)
	}
	if startDate != "" && endDate != "" {
		query = query.Where("updated_at >= ? AND updated_at <= ?", startDate, endDate)
	}
	if len(excludeDetailIds) > 0 {
		query = query.Not("detail_id IN (?)", excludeDetailIds)
	}
	if publishing_status != "" {
		query = query.Where("publishing_status = ?", publishing_status)
	}
	if is_use != "" {
		intuse, _ := strconv.ParseInt(is_use, 10, 64)
		query = query.Where("is_use = ?", intuse)
	}
	if status != "" {
		intstatus, _ := strconv.ParseInt(status, 10, 64)
		query = query.Where("status = ?", intstatus)

	}
	query = query.Where("is_del = ?", 0)
	query = query.Order("detail_id ASC, is_use DESC, main_version DESC, updated_at ASC").Offset(offset).Limit(limit)
	// 执行查询
	var results []RuleContentDetail
	err = query.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (n *RuleContentDetail) QueryRuleDetailsSeIfUseEX(ruleName, startDate, endDate string, detailIdList []int64, EXIdList []int64, offset, limit int) ([]RuleContentDetail, error) {
	if db == nil {
		return nil, fmt.Errorf("failed to get database connection")
	}
	// 构建查询条件
	query := db.Model(&RuleContentDetail{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_description LIKE ? AND is_use = ?", "%"+ruleName+"%", "%"+ruleName+"%", 1)
	}
	if len(detailIdList) > 0 {
		query = query.Where("detail_id IN (?) AND is_use = ?", detailIdList, 1)
	}
	if len(EXIdList) > 0 {
		query = query.Where("detail_id NOT IN (?) AND is_use = ?", EXIdList, 1)
	}
	if startDate != "" && endDate != "" {
		query = query.Where("updated_at >= ? AND updated_at <= ? AND is_use = ?", startDate, endDate, 1)
	}
	// 添加分页逻辑
	query = query.Offset(offset).Limit(limit)
	// 执行查询
	var results []RuleContentDetail
	err := query.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

// 除去limit限制的条数
func (n *RuleContentDetail) QueryRuleDetailsTh(ruleName, startDate, endDate string, detailIdList []int64) ([]RuleContentDetail, error) {
	if db == nil {
		return nil, fmt.Errorf("failed to get database connection")
	}
	// 构建查询条件
	query := db.Model(&RuleContentDetail{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_description LIKE ?", "%"+ruleName+"%", "%"+ruleName+"%")
	}
	if len(detailIdList) > 0 {
		query = query.Where("detail_id IN (?)", detailIdList)
	}
	if startDate != "" && endDate != "" {
		query = query.Where("updated_at >= ? AND updated_at <= ?", startDate, endDate)
	}
	// 执行查询
	var results []RuleContentDetail
	err := query.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

// 添加过滤是否可用
func (n *RuleContentDetail) QueryRuleDetailsThIfUse(ruleName, startDate, endDate string, detailIdList, excludeDetailIds []int64, status, publishing_status, is_use string) ([]RuleContentDetail, error) {
	if db == nil {
		return nil, fmt.Errorf("failed to get database connection")
	}
	// 构建查询条件
	var (
		//intuse int64
		err error
	)
	query := db.Model(&RuleContentDetail{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_description LIKE ?", "%"+ruleName+"%", "%"+ruleName+"%")
	}
	if len(detailIdList) > 0 {
		query = query.Where("detail_id IN (?)", detailIdList)
	}
	if startDate != "" && endDate != "" {
		query = query.Where("updated_at >= ? AND updated_at <= ?", startDate, endDate)
	}
	if len(excludeDetailIds) > 0 {
		query = query.Not("detail_id IN (?)", excludeDetailIds)
	}
	if publishing_status != "" {
		query = query.Where("publishing_status = ?", publishing_status)
	}
	if is_use != "" {
		intuse, _ := strconv.ParseInt(is_use, 10, 64)
		query = query.Where("is_use = ?", intuse)
	}
	if status != "" {
		intstatus, _ := strconv.ParseInt(status, 10, 64)
		query = query.Where("status = ?", intstatus)
	}
	query = query.Where("is_del = ?", 0)

	// 执行查询
	var results []RuleContentDetail
	err = query.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (n *RuleContentDetail) QueryRuleDetailsThIfUseEX(ruleName, startDate, endDate string, detailIdList []int64, exlist []int64) ([]RuleContentDetail, error) {
	if db == nil {
		return nil, fmt.Errorf("failed to get database connection")
	}
	// 构建查询条件
	query := db.Model(&RuleContentDetail{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_description LIKE ? AND is_use = ?", "%"+ruleName+"%", "%"+ruleName+"%", 1)
	}
	if len(detailIdList) > 0 {
		query = query.Where("detail_id IN (?) AND is_use = ?", detailIdList, 1)
	}
	if len(exlist) > 0 {
		query = query.Where("detail_id NOT IN (?) AND is_use = ?", exlist, 1)
	}
	if startDate != "" && endDate != "" {
		query = query.Where("updated_at >= ? AND updated_at <= ? AND is_use = ?", startDate, endDate, 1)
	}
	// 执行查询
	var results []RuleContentDetail
	err := query.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (n *RuleContentDetail) QueryRuleDetailsFo(ruleName, startDate, endDate string) ([]RuleContentDetail, error) {
	if db == nil {
		return nil, fmt.Errorf("failed to get database connection")
	}
	// 构建查询条件
	query := db.Model(&RuleContentDetail{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_description LIKE ?", "%"+ruleName+"%", "%"+ruleName+"%")
	}
	if startDate != "" && endDate != "" {
		query = query.Where("updated_at >= ? AND updated_at <= ?", startDate, endDate)
	}
	// 执行查询
	var results []RuleContentDetail
	err := query.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

//func (n *RuleContentDetail) QueryRuleDetailsFoIfUse(ruleName, startDate, endDate, status, is_use string, excludeDetailIds []int64) ([]RuleContentDetail, error) {
//	// 构建查询条件
//	intuse, _ := strconv.ParseInt(is_use, 10, 64)
//	query := db.Model(&RuleContentDetail{})
//	if ruleName != "" {
//		query = query.Where("rule_name LIKE ? OR rule_description LIKE ? AND is_use = ?", "%"+ruleName+"%", "%"+ruleName+"%", intuse)
//	}
//	if startDate != "" && endDate != "" {
//		query = query.Where("updated_at >= ? AND updated_at <= ? AND is_use = ?", startDate, endDate, intuse)
//	} else {
//		query = query.Where("is_use = ?", intuse)
//	}
//	if len(excludeDetailIds) > 0 {
//		query = query.Not("detail_id IN (?) AND is_use = ?", excludeDetailIds, intuse)
//	}
//	// 执行查询
//	var results []RuleContentDetail
//	var err error
//	switch status {
//	case "":
//		err = query.Find(&results).Error
//
//	case "0":
//		err = query.Where("status = ?", 0).Find(&results).Error
//
//	case "1":
//		err = query.Where("status = ?", 1).Find(&results).Error
//	}
//	if err != nil {
//		return nil, err
//	}
//	return results, nil
//}

func (n *RuleContentDetail) QueryRuleDetailsFoIfUse(ruleName, startDate, endDate, status, is_use string, excludeDetailIds []int64) ([]RuleContentDetail, error) {
	intuse, _ := strconv.ParseInt(is_use, 10, 64)
	query := db.Model(&RuleContentDetail{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_description LIKE ?", "%"+ruleName+"%", "%"+ruleName+"%")
	}
	if startDate != "" && endDate != "" {
		query = query.Where("updated_at >= ? AND updated_at <= ?", startDate, endDate)
	}
	if len(excludeDetailIds) > 0 {
		query = query.Not("detail_id IN (?)", excludeDetailIds)
	}
	if is_use != "" {
		query = query.Where("is_use = ?", intuse)
	}
	query = query.Where("is_del = ?", 0)

	// 执行查询
	var results []RuleContentDetail
	var err error
	switch status {
	case "":
		err = query.Find(&results).Error
	case "0":
		err = query.Where("status = ?", 0).Find(&results).Error

	case "1":
		err = query.Where("status = ?", 1).Find(&results).Error
	}
	if err != nil {
		return nil, err
	}
	return results, nil
}

// 更新指定 detail_version_id 的 status 字段
func (r *RuleContentDetail) UpdateStatusByDetailVersionID(detailVersionID int64, newStatus int64) error {
	// 查找指定 detail_version_id 的记录
	var detail RuleContentDetail
	if err := db.Where("detail_version_id = ?", detailVersionID).First(&detail).Error; err != nil {
		return err
	}
	// 更新 status 字段
	if err := db.Model(&detail).Update("status", newStatus).Update("is_use", 0).Error; err != nil {
		return err
	}
	return nil
}

// 查询相同 detail_id 的最大 version 记录
func (r *RuleContentDetail) FindMaxVersionByDetailID(detail_id int64) (*RuleContentDetail, error) {
	var result RuleContentDetail
	if err := db.Where("detail_id = ?", detail_id).Order("main_version desc").First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r *RuleContentDetail) FindMaxVersionRecord(detailID int64) (*RuleContentDetail, error) {
	var record RuleContentDetail
	err := db.Where("detail_id = ?", detailID).
		Order("main_version DESC").
		Order("sub_version DESC").
		First(&record).Error
	return &record, err
}

func (r *RuleContentDetail) UpdateIsUseToZeroByDetailID(tx *gorm.DB, detailID int64) error {
	// 定义 RuleContentDetail 结构体变量用于存储查询结果
	var ruleContentDetail RuleContentDetail
	// 查询符合条件的记录
	if err := tx.Model(&RuleContentDetail{}).
		Where("detail_id = ? AND is_use = ?", detailID, 1).
		First(&ruleContentDetail).Error; err != nil {
		return err
	}
	// 更新 is_use 字段值为 0
	ruleContentDetail.ISUse = 0
	if err := tx.Save(&ruleContentDetail).Error; err != nil {
		return err
	}
	return nil
}

// 克隆
func (r *RuleContentDetail) FindAndDuplicateRecord(detailVersionID, main_version int64) error {
	var record RuleContentDetail
	if err := db.Where("detail_version_id = ?", detailVersionID).First(&record).Error; err != nil {
		return err
	}
	record.DetailVersionID = 0
	record.MainVersion = main_version
	record.PublishingStatus = "SAVED"
	record.PublishingReject = 0
	record.ISUse = 0
	if err := db.Create(&record).Error; err != nil {
		return err
	}
	return nil
}

// 变更发布状态和在用状态
func (r *RuleContentDetail) UpdatePublishingStatusANDIsUseByVersionID(detailVersionID int64, newStatus string, isUse int64) error {
	if err := db.Model(&RuleContentDetail{}).
		Where("detail_version_id = ?", detailVersionID).
		Update("publishing_status", newStatus).
		Update("is_use", isUse).Error; err != nil {
		return err
	}
	return nil
}

// 变更在用状态
func (r *RuleContentDetail) UpdateIsUseByVersionID(detailVersionID, isUse int64) error {
	if err := db.Model(&RuleContentDetail{}).
		Where("detail_version_id = ?", detailVersionID).
		Update("status", 1).
		Update("is_use", isUse).Error; err != nil {
		return err
	}
	return nil
}

// 在用状态查询
func (r *RuleContentDetail) FindActiveRecordsByDetailID(detailID, is_use int64) ([]RuleContentDetail, error) {
	var records []RuleContentDetail
	err := db.Where("detail_id = ? AND is_use = ?", detailID, is_use).Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

// 变更发布状态和在用状态
func (r *RuleContentDetail) UpdatePublishingStatusANDIsUseByVersionIDTx(tx *gorm.DB, detailVersionID int64, newStatus string, isUse int64) error {
	if err := tx.Model(&RuleContentDetail{}).
		Where("detail_version_id = ?", detailVersionID).
		Update("publishing_status", newStatus).
		Update("is_use", isUse).Error; err != nil {
		return err
	}
	return nil
}

// 更新操作
func (r *RuleContentDetail) UpdateRuleContentDetailTx(tx *gorm.DB, detailVersionID int64, updates map[string]interface{}) error {
	if err := tx.Model(&RuleContentDetail{}).
		Where("detail_version_id = ?", detailVersionID).
		Updates(updates).Error; err != nil {
		return err
	}
	return nil
}

// 删除
func (r *RuleContentDetail) DeleteByVersionID(detailVersionID int64) error {
	if err := db.Model(&RuleContentDetail{}).
		Where("detail_version_id = ?", detailVersionID).
		Update("is_del", 1).Error; err != nil {
		return err
	}
	return nil
}

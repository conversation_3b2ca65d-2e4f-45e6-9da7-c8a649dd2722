package models

import (
	"fmt"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
	"io/ioutil"
	"kmbservice/pkg/logging"
	"kmbservice/pkg/setting"
	"kmbservice/pkg/util"
	"log"
	"os"
	"strings"
	"time"
)

var db *gorm.DB

type BaseModel struct {
	GlobalId   int64 `gorm:"primary_key;comment:'全局ID'" json:"global_id"`
	TriggerId  int64 `gorm:"comment:'事件ID'" json:"trigger_id"`
	ActivityId int64 `gorm:"comment:'活动ID'" json:"activity_id"`
}
type LastModel struct {
	Revision  int    `gorm:"comment:'乐观锁'" json:"revision"`
	CreatedAt int    `gorm:"comment:'创建时间'" json:"createdAt"`
	CreatedBy string `gorm:"comment:'创建人'" json:"created_by"`
	UpdatedAt int    `gorm:"comment:'更新时间'" json:"updatedAt"`
	UpdatedBy string `gorm:"comment:'更新人'" json:"updated_by"`
	DeletedAt int    `gorm:"comment:'删除时间'" json:"deletedAt"`
	DeletedBy string `gorm:"comment:'删除人'" json:"deleted_by"`
}
type OrgModel struct {
	GroupId    int64 `gorm:"comment:'集团ID'" json:"group_id"`
	OrgId      int64 `gorm:"comment:'组织ID'" json:"org_id"`
	TenantId   int64 `gorm:"comment:'租户ID'" json:"tenant_id"`
	LocationId int64 `gorm:"comment:'位置ID'" json:"location_id"`
}

func Setup() {
	var err error
	password, err := util.Decrypt(setting.DatabaseSetting.Password)
	if err != nil {
		logging.Error("解析密码出错", err)
	}
	connectionString := generateConnectionString(
		setting.DatabaseSetting.Type,
		setting.DatabaseSetting.User,
		password,
		setting.DatabaseSetting.Host,
		setting.DatabaseSetting.Port,
		setting.DatabaseSetting.Name,
	)
	var dialector gorm.Dialector
	switch setting.DatabaseSetting.Type {
	case "mysql":
		dialector = mysql.Open(connectionString)
	case "postgres":
		dialector = postgres.Open(connectionString)
	default:
		log.Fatalf("不支持数据库类型: %s", setting.DatabaseSetting.Type)
	}

	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:        time.Second, // 满阈值
			Colorful:             true,        // 日志颜色开启
			ParameterizedQueries: true,        // sql参数可见,true就是?
			LogLevel:             logger.Info, //日志级别
		})
	db, err = gorm.Open(dialector, &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   setting.DatabaseSetting.TablePrefix, // 设置表名前缀，对所有表名生效
			SingularTable: true,                                // 设置表名单数形式
		},
		Logger: newLogger,
	})
	db = db.Debug()
	if err != nil {
		log.Fatalf("连接数据库失败. 错误信息: %v", err)
	}

	//db.Callback().Create().Before("gorm:create").Register("update_time_stamp", updateTimeStampForCreateCallback)
	//db.Callback().Update().Before("gorm:update").Register("update_time_stamp", updateTimeStampForUpdateCallback)
	//db.Callback().Delete().Before("gorm:delete").Register("delete", deleteCallback)
}
func GetDb() *gorm.DB {
	return db
}
func CloseDB() {
	sqlDB, err := db.DB()
	if err != nil {
		logging.Error("获取数据库连接出错", err)
		return
	}
	defer sqlDB.Close()
}
func generateConnectionString(dbType, user, password, host, port, dbName string) string {
	switch dbType {
	case "mysql":
		return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True&loc=Local",
			user,
			password,
			host,
			port,
			dbName)
	case "postgres":
		return fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
			host,
			port,
			user,
			password,
			dbName)
	default:
		log.Fatalf("不支持数据库类型: %s", dbType)
		return ""
	}
}
func updateTimeStampForCreateCallback(db *gorm.DB) {
	nowTime := time.Now().Unix()
	db.Statement.SetColumn("CreatedOn", nowTime)
	db.Statement.SetColumn("ModifiedOn", nowTime)
}
func updateTimeStampForUpdateCallback(db *gorm.DB) {
	db.Statement.SetColumn("ModifiedOn", time.Now().Unix())
}
func deleteCallback(db *gorm.DB) {
	// 检查是否有 DeletedAt 字段
	deletedAtField := db.Statement.Schema.LookUpField("DeletedAt")
	if deletedAtField == nil {
		return // 如果没有 DeletedAt 字段，则直接返回
	}

	// 如果开启了 Unscoped 模式，即使有 DeletedAt 字段，也不执行软删除操作
	if db.Statement.Unscoped {
		return
	}

	// 设置 DeletedAt 字段的值为当前时间
	db.Statement.SetColumn(deletedAtField.DBName, time.Now())
}

func ExecSql(db *gorm.DB, filePath string) error {
	sql, err := Ioutil(filePath)
	if err != nil {
		fmt.Println("数据库基础数据初始化脚本读取失败！原因:", err.Error())
		return err
	}
	sqlList := strings.Split(sql, ";")
	for i := 0; i < len(sqlList)-1; i++ {
		if strings.Contains(sqlList[i], "--") {
			fmt.Println(sqlList[i])
			continue
		}
		sql := strings.Replace(sqlList[i]+";", "\n", "", -1)
		sql = strings.TrimSpace(sql)
		if err = db.Exec(sql).Error; err != nil {
			log.Printf("error sql: %s", sql)
			if !strings.Contains(err.Error(), "Query was empty") {
				return err
			}
		}
	}
	return nil
}
func Ioutil(filePath string) (string, error) {
	if contents, err := ioutil.ReadFile(filePath); err == nil {
		//因为contents是[]byte类型，直接转换成string类型后会多一行空格,需要使用strings.Replace替换换行符
		result := strings.Replace(string(contents), "\n", "", 1)
		fmt.Println("Use ioutil.ReadFile to read a file:", result)
		return result, nil
	} else {
		return "", err
	}
}

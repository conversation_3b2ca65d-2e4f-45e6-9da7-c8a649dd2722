package models

import (
	"time"
)

type DamServiceResourceUsage struct {
	SvcXRecID      int64     `gorm:"column:svc_x_rec_id;primaryKey;autoIncrement:false" json:"svc_x_rec_id"` // 雪花ID
	ServiceID      int64     `gorm:"column:service_id" json:"service_id"`                                    // 服务ID
	ResourceID     int64     `gorm:"column:resource_id" json:"resource_id"`                                  // 资源ID
	ResourceType   string    `gorm:"column:resource_type" json:"resource_type"`                              // 资源类型
	CreateTime     time.Time `gorm:"column:create_time;autoCreateTime" json:"create_time"`                   // 创建时间
	UpdateTime     time.Time `gorm:"column:update_time;autoUpdateTime" json:"update_time"`                   // 更新时间
	CopyID         int64     `gorm:"column:copy_id" json:"copy_id"`                                          // **资源id
	AuditStatus    string    `gorm:"column:audit_status" json:"audit_status"`                                // 审核状态
	ServiceVersion string    `gorm:"column:service_version;default:1" json:"service_version"`                // 版本
	ReleaseStatus  int16     `gorm:"column:release_status;default:0" json:"release_status"`                  // 在用标识
}

// 指定表名
func (DamServiceResourceUsage) TableName() string {
	return "dam_service_resource_usage"
}

func (d *DamServiceResourceUsage) GetRecordsByServiceID(serviceID int64) ([]DamServiceResourceUsage, error) {
	var records []DamServiceResourceUsage
	err := db.Where("service_id = ?", serviceID).Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

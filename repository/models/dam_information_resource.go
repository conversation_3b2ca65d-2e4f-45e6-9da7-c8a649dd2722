package models

import (
	"time"
)

type DamInformationResource struct {
	ResourceID      int64     `gorm:"primaryKey;autoIncrement:false;column:resource_id;default:snowflake_id()" json:"resource_id"` // 资源id
	ResourceName    string    `gorm:"type:varchar(255);not null;column:resource_name" json:"resource_name"`                        // 资源名称
	DisplayName     string    `gorm:"type:varchar(255);not null;column:display_name" json:"display_name"`                          // 显示名称
	MnemonicCode    string    `gorm:"type:varchar(255);column:mnemonic_code" json:"mnemonic_code"`                                 // 输入码
	ResourceStatus  int32     `gorm:"not null;column:resource_status" json:"resource_status"`                                      // 资源状态
	ResourceVersion string    `gorm:"type:varchar(50);column:resource_version;default:1" json:"resource_version"`                  // 资源版本
	Organization    int64     `gorm:"column:organization" json:"organization"`                                                     // 组织id
	Comments        string    `gorm:"type:varchar(500);column:comments" json:"comments"`                                           // 描述
	ResourceCode    string    `gorm:"type:varchar(255);column:resource_code" json:"resource_code"`                                 // 资源编码
	ResourceType    int64     `gorm:"column:resource_type" json:"resource_type"`                                                   // 信息资源类型
	ResourceUri     string    `gorm:"type:varchar(500);column:resource_uri" json:"resource_uri"`                                   // 资源URI
	ResourceForm    string    `gorm:"type:varchar(255);column:resource_form" json:"resource_form"`                                 // 资源表单
	ResourcePrice   float64   `gorm:"type:numeric(10,6);column:resource_price" json:"resource_price"`                              // 资源价格
	BusinessCode    string    `gorm:"type:varchar(255);column:business_code" json:"business_code"`                                 // 业务系统唯一编码
	IsDelete        int16     `gorm:"column:is_delete;default:0" json:"is_delete"`                                                 // 逻辑删除标识
	ParentID        int64     `gorm:"column:parent_id" json:"parent_id"`                                                           // 父级ID
	Level           int32     `gorm:"column:level" json:"level"`                                                                   // 目录级别
	Content         string    `gorm:"type:text;column:content" json:"content"`                                                     // 信息资源内容（json）
	CreateTime      time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP" json:"create_time"`                             // 创建时间
	UpdateTime      time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP" json:"update_time"`                             // 更新时间
	CopyID          int64     `gorm:"column:copy_id" json:"copy_id"`                                                               // 复制ID
	AuditStatus     string    `gorm:"type:varchar(16);column:audit_status" json:"audit_status"`                                    // 审核状态
	ReleaseStatus   int16     `gorm:"column:release_status;default:0" json:"release_status"`                                       // 在用标识
}

func (DamInformationResource) TableName() string {
	return "dam_information_resource"
}

func (d *DamInformationResource) FindByResourceNameLike(name string) ([]DamInformationResource, error) {
	var resources []DamInformationResource
	if err := db.Where("resource_name LIKE ?", "%"+name+"%").Find(&resources).Error; err != nil {
		return nil, err
	}
	return resources, nil
}

func (d *DamInformationResource) FindByResourceID(resource_id string) (*DamInformationResource, error) {
	var resources DamInformationResource
	if err := db.Where("resource_id = ?", resource_id).First(&resources).Error; err != nil {
		return nil, err
	}
	return &resources, nil
}

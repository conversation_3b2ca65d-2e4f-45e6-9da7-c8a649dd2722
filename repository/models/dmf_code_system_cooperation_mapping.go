package models

type DmfCodeSystemCooperationMapping struct {
	MappingID     int64  `gorm:"column:mapping_id;primaryKey;autoIncrement:false;comment:映射id"`
	CorpID        int64  `gorm:"column:corp_id"`
	CodeSystemID  int64  `gorm:"column:code_system_id;comment:编码系统ID"`
	DataValueSoid int64  `gorm:"column:data_value_soid"`
	ThirdCode     string `gorm:"column:third_code;size:255;comment:第三方编码"`
	CreateTime    int64  `gorm:"column:create_time"`
	CreateAt      string `gorm:"column:create_at;size:255"`
	UpdateTime    int64  `gorm:"column:update_time"`
	UpdateAt      string `gorm:"column:update_at;size:255"`
}

// TableName sets the insert table name for this struct type
func (DmfCodeSystemCooperationMapping) TableName() string {
	return "dmf_code_system_cooperation_mapping"
}

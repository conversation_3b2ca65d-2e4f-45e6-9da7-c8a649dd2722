package models

import (
	"kmbservice/pkg/logging"
	"strings"
)

type DrugSpecification struct {
	SpeciID int64  `gorm:"primaryKey;autoIncrement:false" json:"speci_id"` // Primary key, auto-incrementing is disabled
	Name    string `gorm:"type:varchar(255)" json:"name"`
	Content string `gorm:"type:text" json:"content"`
	Link    string `gorm:"type:varchar(255)" json:"link"`
}

func (DrugSpecification) TableName() string {
	return "drug_specification"
}

func (d *DrugSpecification) GetByName(name string) (DrugSpecification, error) {
	var drug DrugSpecification
	db := GetDb()
	result := db.Where("name = ?", name).Find(&drug)
	if result.Error != nil {
		logging.Error("查表[drug_specification]错误")
	}
	return drug, result.Error
}

func (d *DrugSpecification) GetBySpeciID(speciId string) (*DrugSpecification, error) {
	var drug DrugSpecification
	result := db.Where("speci_id = ?", speciId).Find(&drug)
	return &drug, result.Error
}

// 根据名称查找
func (d *DrugSpecification) GetSpecificationsByNames(names []string) ([]DrugSpecification, error) {
	var specifications []DrugSpecification
	if err := db.Where("name IN ?", names).Find(&specifications).Error; err != nil {
		return nil, err
	}
	return specifications, nil
}

func (d *DrugSpecification) GetSpecificationsByQueries(queries []string) ([]DrugSpecification, error) {
	var specifications []DrugSpecification
	// 构建多个 LIKE 查询条件
	likeQueries := make([]string, len(queries))
	likeValues := make([]interface{}, len(queries)*2)
	// 对每个字符串构造模糊匹配
	for i, query := range queries {
		likeQuery := "%" + query + "%"
		likeQueries[i] = "(name LIKE ? OR ARRAY[?] <@ " +
			"CASE WHEN strpos(content, '【别名】') > 0 THEN string_to_array(substring(content from '【别名】([^【]*)'), '、') ELSE array[]::text[] END)"
		likeValues[i] = likeQuery
		likeValues[len(queries)+i] = query
	}
	// 拼接查询语句
	whereClause := strings.Join(likeQueries, " OR ")
	// 执行查询
	db.Raw(`
		SELECT * FROM drug_specification 
		WHERE `+whereClause, likeValues...).Find(&specifications)
	return specifications, nil
}

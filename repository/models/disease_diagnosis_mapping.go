package models

type DiseaseDiagnosisMapping struct {
	PrimaryDisciplineCode              string `gorm:"column:primary_discipline_code;type:varchar(255);comment:'一级学科代码'"`
	PrimaryDisciplineName              string `gorm:"column:primary_discipline_name;type:varchar(255);comment:'一级学科名称'"`
	SecondaryDisciplineCode            string `gorm:"column:secondary_discipline_code;type:varchar(255);comment:'二级学科代码'"`
	SecondaryDisciplineName            string `gorm:"column:secondary_discipline_name;type:varchar(255);comment:'二级学科名称'"`
	DiseaseCode                        string `gorm:"column:disease_code;type:varchar(255);comment:'病种代码'"`
	DiseaseName                        string `gorm:"column:disease_name;type:varchar(255);comment:'病种'"`
	DiagnosisName                      string `gorm:"column:diagnosis_name;type:varchar(255);comment:'诊断名称'"`
	InternalDiagnosisCode              string `gorm:"column:internal_diagnosis_code;type:varchar(255);comment:'内部诊断编码'"`
	ChineseNationalStandard20Diagnosis string `gorm:"column:chinese_national_standard_2_0_diagnosis;type:varchar(255);comment:'中国国标2.0诊断'"`
	F10                                string `gorm:"column:f10;type:varchar(255);comment:'f10'"`
	ICD10ClinicalVersion               string `gorm:"column:icd_10_clinical_version;type:varchar(255);comment:'ICD-10 全国临床版'"`
	ICD11                              string `gorm:"column:icd_11;type:varchar(255);comment:'ICD-11'"`
	DiagnosisSOID                      int64  `gorm:"column:diagnosis_soid;type:bigint;comment:'诊断soid'"`
	DiseaseSOID                        int64  `gorm:"column:disease_soid;type:bigint;comment:'病种soid'"`
}

/*
诊断与病种的关系为 多对一
*/

func (DiseaseDiagnosisMapping) TableName() string {
	return "disease_diagnosis_mapping"
}

func (d *DiseaseDiagnosisMapping) GetByDiagnosisSOID(diagnosisSOID int64) (*DiseaseDiagnosisMapping, error) {
	var result DiseaseDiagnosisMapping
	if err := db.Where("diagnosis_soid = ?", diagnosisSOID).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

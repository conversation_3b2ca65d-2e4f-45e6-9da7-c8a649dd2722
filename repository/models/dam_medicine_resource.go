package models

import (
	"time"
)

type DamMedicineResource struct {
	ResourceID          int64     `gorm:"column:resource_id;primaryKey;autoIncrement:false" json:"resource_id"` // 资源ID
	ResourceName        string    `gorm:"column:resource_name" json:"resource_name"`                            // 资源名
	DisplayName         string    `gorm:"column:display_name" json:"display_name"`                              // 输入名
	MnemonicCode        string    `gorm:"column:mnemonic_code" json:"mnemonic_code"`                            // 识别码
	ResourceStatus      int       `gorm:"column:resource_status" json:"resource_status"`                        // 资源状态
	ResourceVersion     string    `gorm:"column:resource_version;default:1" json:"resource_version"`            // 资源版本
	Organization        string    `gorm:"column:organization" json:"organization"`                              // 组织
	Comments            string    `gorm:"column:comments" json:"comments"`                                      // 内容描述
	ResourcePrice       float64   `gorm:"column:resource_price" json:"resource_price"`                          // 资源价格
	IsDelete            int16     `gorm:"column:is_delete" json:"is_delete"`                                    // 删除标志
	ResourceCode        string    `gorm:"column:resource_code" json:"resource_code"`                            // 资源编码
	Category            string    `gorm:"column:category" json:"category"`                                      // 类别
	Specification       string    `gorm:"column:specification" json:"specification"`                            // 规格
	Manufacturer        string    `gorm:"column:manufacturer" json:"manufacturer"`                              // 生产厂家
	ApprovalNumber      string    `gorm:"column:approval_number" json:"approval_number"`                        // 批准文号
	StorageCondition    string    `gorm:"column:storage_condition" json:"storage_condition"`                    // 储存条件
	Indications         string    `gorm:"column:indications" json:"indications"`                                // 适应症
	AdverseReactions    string    `gorm:"column:adverse_reactions" json:"adverse_reactions"`                    // 不良反应
	Contraindications   string    `gorm:"column:contraindications" json:"contraindications"`                    // 禁忌症
	Ingredients         string    `gorm:"column:ingredients" json:"ingredients"`                                // 成分
	Dosage              string    `gorm:"column:dosage" json:"dosage"`                                          // 用法用量
	Origin              string    `gorm:"column:origin" json:"origin"`                                          // 产地
	ExpirationDate      string    `gorm:"column:expiration_date" json:"expiration_date"`                        // 有效期
	Stock               int       `gorm:"column:stock" json:"stock"`                                            // 库存数量
	Unioncode           string    `gorm:"column:unioncode" json:"unioncode"`                                    // 国家码
	EngName             string    `gorm:"column:eng_name" json:"eng_name"`                                      // 英文名
	DosageForm          string    `gorm:"column:dosageform" json:"dosageform"`                                  // 剂型
	ProductName         string    `gorm:"column:productname" json:"productname"`                                // 商品名
	PackagingUnit       string    `gorm:"column:packagingunit" json:"packagingunit"`                            // 包装单位
	PackagingQuantity   string    `gorm:"column:packagingquantity" json:"packagingquantity"`                    // 包装数量
	PackagingMaterial   string    `gorm:"column:packagingmaterial" json:"packagingmaterial"`                    // 包装材质
	PackagingMethod     string    `gorm:"column:packagingmethod" json:"packagingmethod"`                        // 包装方式
	PackagingProperties string    `gorm:"column:packagingproperties" json:"packagingproperties"`                // 包装属性
	Flag                int16     `gorm:"column:flag" json:"flag"`                                              // 中西药标记: 1:西药 2:中药
	GenericName         string    `gorm:"column:generic_name" json:"generic_name"`                              // 通用名
	BusinessCode        string    `gorm:"column:business_code" json:"business_code"`                            // 业务系统唯一编码
	CreateTime          time.Time `gorm:"column:create_time;autoCreateTime" json:"create_time"`                 // 创建时间
	UpdateTime          time.Time `gorm:"column:update_time;autoUpdateTime" json:"update_time"`                 // 更新时间
	CopyID              int64     `gorm:"column:copy_id" json:"copy_id"`                                        // **资源id
	AuditStatus         string    `gorm:"column:audit_status" json:"audit_status"`                              // 审核状态
	ReleaseStatus       int16     `gorm:"column:release_status;default:0" json:"release_status"`                // 在用标识
}

func (DamMedicineResource) TableName() string {
	return "dam_medicine_resource"
}

func (d *DamMedicineResource) GetResourceNamesByIDs(resourceIDs []int64) ([]string, error) {
	var resourceNames []string
	// 查询数据库，获取对应的 resource_name 列
	if err := db.Model(&DamMedicineResource{}).
		Select("generic_name").
		Where("resource_id IN ?", resourceIDs).
		Pluck("generic_name", &resourceNames).Error; err != nil {
		return nil, err
	}
	return resourceNames, nil
}

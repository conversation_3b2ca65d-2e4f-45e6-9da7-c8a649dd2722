package models

import (
	"gorm.io/gorm"
	"strconv"
	"time"
)

type DksKnbDslProperty struct {
	TenantID     string    `gorm:"column:tenant_id;size:32;comment:'租户号'"`
	Revision     int       `gorm:"column:revision;comment:'乐观锁'"`
	CreatedBy    string    `gorm:"column:created_by;size:32;comment:'创建人'"`
	CreatedTime  time.Time `gorm:"column:created_time;comment:'创建时间'"`
	UpdatedBy    string    `gorm:"column:updated_by;size:32;comment:'更新人'"`
	UpdatedTime  time.Time `gorm:"column:updated_time;comment:'更新时间'"`
	PropertyID   int64     `gorm:"column:property_id;primaryKey;autoIncrement:false;comment:'属性ID'"`
	DslID        int64     `gorm:"column:dsl_id;comment:'对象ID'"`
	PropertyName string    `gorm:"column:property_name;size:255;comment:'属性名称'"`
	IsDel        int       `gorm:"column:is_del;comment:'删除状态'"`
	Status       int       `gorm:"column:status;comment:'状态'"`
	PropertyDesc string    `gorm:"column:property_desc;size:255;comment:'属性描述'"`
	ParaDef      string    `gorm:"column:para_def;comment:'参数定义'"`
	OntoSlotID   int64     `gorm:"column:onto_slot_id;comment:'本体SLOT属性ID'"`
	PropertyType int64     `gorm:"column:property_type;comment:'1: 属性 2 函数'"`
}

// 定义更新的请求结构体
type UpdatePropertyRequest struct {
	TenantID     *string    `json:"tenant_id,omitempty"`
	Revision     *int       `json:"revision,omitempty"`
	UpdatedBy    *string    `json:"updated_by,omitempty"`
	UpdatedTime  *time.Time `json:"updated_time,omitempty"`
	DslID        *int64     `json:"dsl_id,omitempty"`
	PropertyName *string    `json:"property_name,omitempty"`
	Status       *int       `json:"status,omitempty"`
	PropertyDesc *string    `json:"property_desc,omitempty"`
	ParaDef      *[]byte    `json:"para_def,omitempty"`
	OntoSlotID   *int64     `json:"onto_slot_id,omitempty"`
	PropertyType *int64     `json:"property_type,omitempty"`
}

// 定义创建请求结构体
type CreatePropertyRequest struct {
	TenantID     string `json:"tenant_id"`
	CreatedBy    string `json:"created_by"`
	PropertyID   int64  `json:"property_id"`
	DslID        int64  `json:"dsl_id"`
	PropertyName string `json:"property_name"`
	Status       int    `json:"status"`
	PropertyDesc string `json:"property_desc"`
	ParaDef      string `json:"para_def"`
	OntoSlotID   int64  `json:"onto_slot_id"`
	PropertyType int64  `json:"property_type"`
}

// TableName 设置DksKnbDslProperty的表名
func (DksKnbDslProperty) TableName() string {
	return "dks_knb_dsl_property"
}

// 创建一条新纪录
func (d *DksKnbDslProperty) CreateProperty(db *gorm.DB, createRequest CreatePropertyRequest) error {
	property := DksKnbDslProperty{
		TenantID:     createRequest.TenantID,
		CreatedBy:    createRequest.CreatedBy,
		CreatedTime:  time.Now(),
		PropertyID:   createRequest.PropertyID,
		DslID:        createRequest.DslID,
		PropertyName: createRequest.PropertyName,
		Status:       createRequest.Status,
		PropertyDesc: createRequest.PropertyDesc,
		ParaDef:      createRequest.ParaDef,
		OntoSlotID:   createRequest.OntoSlotID,
		PropertyType: createRequest.PropertyType,
		IsDel:        0,                       // 默认值
		UpdatedBy:    createRequest.CreatedBy, // 初始值
		UpdatedTime:  time.Now(),              // 初始值
	}

	return db.Create(&property).Error
}

func (d *DksKnbDslProperty) GetDksKnbDslPropertiesByDslID(dslID int64) ([]DksKnbDslProperty, error) {
	var properties []DksKnbDslProperty
	result := db.Where("dsl_id = ?", dslID).Find(&properties)
	if result.Error != nil {
		return nil, result.Error
	}
	return properties, nil
}

func (d *DksKnbDslProperty) GetDksKnbDslPropertiesByPropertyID(property_id int64) ([]DksKnbDslProperty, error) {
	var properties []DksKnbDslProperty
	result := db.Where("property_id = ?", property_id).Find(&properties)
	if result.Error != nil {
		return nil, result.Error
	}
	return properties, nil
}

// 通过 property_id 更新传入的参数值
func (d *DksKnbDslProperty) UpdatePropertyByID(propertyID int64, updateRequest UpdatePropertyRequest) error {
	var property DksKnbDslProperty
	if err := db.First(&property, propertyID).Error; err != nil {
		return err
	}
	// 更新非零值字段
	updateData := map[string]interface{}{}
	if updateRequest.TenantID != nil {
		updateData["tenant_id"] = *updateRequest.TenantID
	}
	if updateRequest.Revision != nil {
		updateData["revision"] = *updateRequest.Revision
	}
	if updateRequest.UpdatedBy != nil {
		updateData["updated_by"] = *updateRequest.UpdatedBy
	}
	if updateRequest.UpdatedTime != nil {
		updateData["updated_time"] = *updateRequest.UpdatedTime
	}
	if updateRequest.DslID != nil {
		updateData["dsl_id"] = *updateRequest.DslID
	}
	if updateRequest.PropertyName != nil {
		updateData["property_name"] = *updateRequest.PropertyName
	}
	if updateRequest.Status != nil {
		updateData["status"] = *updateRequest.Status
	}
	if updateRequest.PropertyDesc != nil {
		updateData["property_desc"] = *updateRequest.PropertyDesc
	}
	if updateRequest.ParaDef != nil {
		updateData["para_def"] = *updateRequest.ParaDef
	}
	if updateRequest.OntoSlotID != nil {
		updateData["onto_slot_id"] = *updateRequest.OntoSlotID
	}
	if updateRequest.PropertyType != nil {
		updateData["property_type"] = *updateRequest.PropertyType
	}
	return db.Model(&property).Updates(updateData).Error
}

func ToStringPtr(s string) *string {
	return &s
}

func ToTimePtr(t time.Time) *time.Time {
	return &t
}

func ToJsonPtr(b []byte) *[]byte {
	return &b
}

func StrToInt64Ptr(s string) *int64 {
	if s == "" {
		return nil
	}
	val, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		return nil
	}
	return &val
}

func StrToInt(s string) *int {
	if s == "" {
		return nil
	}
	val, err := strconv.Atoi(s)
	if err != nil {
		return nil
	}
	return &val
}

package models

import (
	"gorm.io/gorm"
)

type RuleContentDetailObjectMapping struct {
	MappingID int64  `gorm:"primaryKey;autoIncrement:true"`
	DetailID  *int64 `gorm:"column:detail_id;comment:规则详情id"`
	ObjectID  *int64 `gorm:"column:object_id;comment:领域对象id"`
}

func (r *RuleContentDetailObjectMapping) TableName() string {
	return "rule_content_detail_object_mapping"
}

// 映射关系添加
func (r *RuleContentDetailObjectMapping) CreateMappings(detailID int64, objectIDs []int64) error {
	for _, objectID := range objectIDs {
		mapping := RuleContentDetailObjectMapping{
			DetailID: &detailID,
			ObjectID: &objectID,
			//CreatedAt: time.Now(),
			//UpdatedAt: time.Now(),
		}
		if err := db.Create(&mapping).Error; err != nil {
			return err
		}
	}
	return nil
}

// 映射关系添加（事务）
func (r *RuleContentDetailObjectMapping) CreateMappingsTx(tx *gorm.DB, detailID int64, objectIDs []int64) error {
	for _, objectID := range objectIDs {
		mapping := RuleContentDetailObjectMapping{
			DetailID: &detailID,
			ObjectID: &objectID,
		}
		if err := tx.Create(&mapping).Error; err != nil {
			return err
		}
	}
	return nil
}

// 方法：根据 object_id 切片查询对应的 detail_id 列表（去重）
func (r *RuleContentDetailObjectMapping) GetDetailIDsByObjectIDs(objectIDs []int64) ([]int64, error) {
	var mappings []RuleContentDetailObjectMapping
	var detailIDs []int64

	// 查询所有匹配的映射记录
	err := db.Where("object_id IN (?)", objectIDs).Find(&mappings).Error
	if err != nil {
		return nil, err
	}

	// 提取所有不重复的 detail_id
	detailIDMap := make(map[int64]bool)
	for _, mapping := range mappings {
		if mapping.DetailID != nil {
			detailIDMap[*mapping.DetailID] = true
		}
	}

	for detailID := range detailIDMap {
		detailIDs = append(detailIDs, detailID)
	}

	return detailIDs, nil
}

// 查找符合所有对象id的规则
func (r *RuleContentDetailObjectMapping) GetDetailIDsByObjectIDsAND(objectIDs []int64) ([]int64, error) {
	var commonDetailIDs []int64
	subquery := db.Table("rule_content_detail_object_mapping").Select("detail_id").Where("object_id IN ?", objectIDs).Group("detail_id").Having("COUNT(DISTINCT object_id) = ?", len(objectIDs))
	if err := db.Table("(?) as sub", subquery).Select("sub.detail_id").Scan(&commonDetailIDs).Error; err != nil {
		return nil, err
	}
	return commonDetailIDs, nil
}

func (r *RuleContentDetailObjectMapping) DeleteByDetailIDTx(tx *gorm.DB, detailID int64) error {
	result := tx.Where("detail_id = ?", detailID).Delete(&RuleContentDetailObjectMapping{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}

package models

type DasKnbDocDiseaseRelationship struct {
	ID              int64 `gorm:"primaryKey;column:id;autoIncrement:false;default:snowflake_id();comment:雪花id"`
	DasKnbDocTreeID int64 `gorm:"index:index_1;column:das_knb_doc_tree_id;comment:静态知识库病种id"`
	DiseaseSoid     int64 `gorm:"index:index_2;column:disease_soid;comment:标准病种soid"`
}

func (DasKnbDocDiseaseRelationship) TableName() string {
	return "das_knb_doc_disease_relationship"
}

func (d *DasKnbDocDiseaseRelationship) GetByDiseaseSoid(disease_soid int64) (*DasKnbDocDiseaseRelationship, error) {
	var result DasKnbDocDiseaseRelationship
	if err := db.Where("disease_soid = ?", disease_soid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

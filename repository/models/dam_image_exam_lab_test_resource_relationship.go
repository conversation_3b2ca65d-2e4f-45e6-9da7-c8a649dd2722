package models

type DamImageExamLabTestResourceRelationship struct {
	ID         int64 `gorm:"column:id;primaryKey;autoIncrement:false;default:snowflake_id();comment:'映射id'"`
	ServiceID  int64 `gorm:"column:service_id;not null;comment:'服务id'"`
	ResourceID int64 `gorm:"column:resource_id;not null;comment:'资源id'"`
}

func (DamImageExamLabTestResourceRelationship) TableName() string {
	return "dam_image_exam_lab_test_resource_relationship"
}

func (d *DamImageExamLabTestResourceRelationship) GetByServiceID(service_id int64) (*DamImageExamLabTestResourceRelationship, error) {
	var result DamImageExamLabTestResourceRelationship
	if err := db.Where("service_id = ?", service_id).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

package models

type DmpMdmCMedicineItem struct {
	ItemID          int64  `gorm:"column:item_id;primaryKey;autoIncrement:false"`
	ItemOID         int64  `gorm:"column:item_oid"`
	TCMID           int64  `gorm:"column:tcm_id"`
	CName           string `gorm:"column:cname"`
	Qly             string `gorm:"column:qly"`
	Unit            string `gorm:"column:unit"`
	Description     string `gorm:"column:desc"`
	DescofDecoction string `gorm:"column:descofdecoction"`
	Compatibility   string `gorm:"column:compatibility;not null"`
	Status          string `gorm:"column:status"`
	YpCode          string `gorm:"column:ypcode"`
}

// TableName sets the table name for the model, which is "dmp_mdm_cmedicine_items" in this case.
func (DmpMdmCMedicineItem) TableName() string {
	return "dmp_mdm_cmedicine_items"
}

func (d *DmpMdmCMedicineItem) GetDrugSpecificationItems(tcm_id int64) ([]DmpMdmCMedicineItem, error) {
	var items []DmpMdmCMedicineItem
	err := db.Where("tcm_id = ?", tcm_id).Find(&items).Error
	return items, err
}

package models

import (
	"time"
)

type DmfCodeSystem struct {
	CodeSystemID           int64      `gorm:"column:code_system_id;primaryKey;autoIncrement:false;default:snowflake_id();comment:编码系统ID"`
	NomenclatureSoid       *int64     `gorm:"column:nomenclature_soid;comment:命名方法ID"`
	DomainSoid             *int64     `gorm:"column:domain_soid;comment:领域分类ID"`
	CodingSystemIdentifier *string    `gorm:"column:coding_system_identifier;size:255;comment:代码系统标识符"`
	CnName                 *string    `gorm:"column:cn_name;size:255;comment:中文名称"`
	Definition             *string    `gorm:"column:definition;size:255;comment:定义"`
	EnName                 *string    `gorm:"column:en_name;size:255;comment:英文名称"`
	Description            *string    `gorm:"column:description;size:255;comment:描述"`
	PyCode                 *string    `gorm:"column:py_code;size:64;comment:拼音码"`
	WbCode                 *string    `gorm:"column:wb_code;size:64;comment:五笔码"`
	Version                *int32     `gorm:"column:version;comment:版本"`
	BeginDate              *time.Time `gorm:"column:begin_date;comment:生效时间"`
	EndDate                *time.Time `gorm:"column:end_date;comment:失效时间"`
	Status                 *int32     `gorm:"column:status;comment:状态"`
	IsDelete               *int32     `gorm:"column:is_delete;comment:逻辑删除"`
	RegOrgID               *int64     `gorm:"column:reg_org_id;comment:注册组织"`
	SubmitOrgID            *int64     `gorm:"column:submit_org_id;comment:提交组织"`
	PublishDate            *time.Time `gorm:"column:publish_date;comment:发布时间"`
	CreateDate             *time.Time `gorm:"column:create_date;comment:创建时间"`
	UpdateDate             *time.Time `gorm:"column:update_date;comment:最后更新时间"`
	CreateUser             *string    `gorm:"column:create_user;size:255;comment:创建用户"`
	UpdateUser             *string    `gorm:"column:update_user;size:255;comment:最后更新用户"`
	CodingClass            *string    `gorm:"column:coding_class;size:20;comment:分类SOID"`
	SourceSoid             *string    `gorm:"column:source_soid;size:20;comment:来源SOID"`
	CorpID                 string     `gorm:"column:corp_id" json:"corp_id"`
	EnableStatus           int16      `gorm:"column:enable_status"`
}

func (DmfCodeSystem) TableName() string {
	return "dmf_code_system"
}

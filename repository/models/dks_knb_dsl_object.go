package models

import "time"

// DksKnbDslObject 表结构
type DksKnbDslObject struct {
	TenantID    string    `gorm:"column:tenant_id;size:32;comment:'租户号'"`
	Revision    int       `gorm:"column:revision;comment:'乐观锁'"`
	CreatedBy   string    `gorm:"column:created_by;size:32;comment:'创建人'"`
	CreatedTime time.Time `gorm:"column:created_time;comment:'创建时间'"`
	UpdatedBy   string    `gorm:"column:updated_by;size:32;comment:'更新人'"`
	UpdatedTime time.Time `gorm:"column:updated_time;comment:'更新时间'"`
	DslID       int64     `gorm:"column:dsl_id;primaryKey;autoIncrement:false;comment:'领域对象ID'"`
	DslName     string    `gorm:"column:dsl_name;size:255;comment:'领域对象名称'"`
	IsDel       int       `gorm:"column:is_del;comment:'是否删除'"`
	Status      int       `gorm:"column:status;comment:'状态'"`
	DslDesc     string    `gorm:"column:dsl_desc;size:255;comment:'名称的中文描述'"`
	OntoID      *int64    `gorm:"column:onto_id;comment:'本体ID'"`
}

// TableName 设置DksKnbDslObject的表名
func (DksKnbDslObject) TableName() string {
	return "dks_knb_dsl_object"
}

// 根据名称描述获取数据
func (d *DksKnbDslObject) GetListByNameLike(dsl_desc string) ([]DksKnbDslObject, error) {
	var relationships []DksKnbDslObject
	// 使用原生SQL进行模糊查询
	dsl_desc = "%" + dsl_desc + "%"
	if err := db.Raw("SELECT * FROM dks_knb_dsl_object WHERE dsl_desc LIKE ?", dsl_desc).Scan(&relationships).Error; err != nil {
		return nil, err
	}
	return relationships, nil
}

// 获取全部数据
func (d *DksKnbDslObject) GetAllData() ([]DksKnbDslObject, error) {
	var relationships []DksKnbDslObject
	result := db.Find(&relationships)
	if result.Error != nil {
		return nil, result.Error
	}
	return relationships, nil
}

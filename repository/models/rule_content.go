package models

import (
	"fmt"
	"gorm.io/gorm"
	"strconv"
	"time"
)

type NewRuleContent struct {
	ID              int64   `gorm:"column:id"`
	CategoryTreeID  int64   `gorm:"column:category_tree_id"` //规则类型id
	RuleName        string  `gorm:"column:rule_name"`
	RuleDescription string  `gorm:"column:rule_description"`
	UsageScenario   string  `gorm:"column:usage_scenario"`
	RuleContent     string  `gorm:"column:rule_content"`
	Revision        int64   `gorm:"column:revision"`
	CreatedAt       int64   `gorm:"column:created_at"`
	CreatedBy       string  `gorm:"column:created_by"`
	UpdatedAt       int64   `gorm:"column:updated_at"`
	UpdatedBy       string  `gorm:"column:updated_by"`
	DeletedAt       int64   `gorm:"column:deleted_at"`
	DeletedBy       string  `gorm:"column:deleted_by"`
	Status          int64   `gorm:"column:status"`
	IsDeleted       int64   `gorm:"column:is_del"`
	RuleCode        string  `gorm:"column:rule_code"`
	RuleVersionID   int64   `gorm:"primaryKey;column:rule_version_id"`
	IsUse           int64   `gorm:"column:is_use"`
	MinVersion      float64 `gorm:"column:min_version"`
	CategoryId      int64   `gorm:"column:category_id"`                      //监管类别id
	MainVersion     int64   `gorm:"column:main_version" json:"main_version"` // 主版本号
	SubVersion      int64   `gorm:"column:sub_version" json:"sub_version"`   // 次版本号
}

func (n *NewRuleContent) TableName() string {
	return "rule_content"
}

// 创建一条记录
func (n *NewRuleContent) CreateRuleGroup(rulegroupid, mainVersion, subVersion int64, RuleGroupName, RuleCode, RuleDescription, packedRules, Ascen, RuleTreeID, CategoryId, status string) (string, error) {
	if db == nil {
		return "", fmt.Errorf("failed to get database connection")
	}
	n.ID = rulegroupid
	n.RuleName = RuleGroupName
	n.RuleCode = RuleCode
	n.RuleDescription = RuleDescription
	n.RuleContent = packedRules
	n.UsageScenario = Ascen
	intRTid, _ := strconv.ParseInt(RuleTreeID, 10, 64)
	n.CategoryTreeID = intRTid
	intCategoryId, _ := strconv.ParseInt(CategoryId, 10, 64)
	n.CategoryId = intCategoryId
	n.UpdatedAt = time.Now().Unix()
	intstatus, _ := strconv.ParseInt(status, 10, 64)
	n.Status = intstatus
	n.IsUse = 1
	n.MainVersion = mainVersion
	n.SubVersion = subVersion
	return n.RuleContent, db.Create(n).Error
}

func (n *NewRuleContent) FindByIDAndIsUse(id int64, isUse int64) (*NewRuleContent, error) {
	var ruleContent NewRuleContent
	result := db.Where("id = ? AND is_use = ?", id, isUse).First(&ruleContent)
	if result.Error != nil {
		return nil, result.Error
	}
	return &ruleContent, nil
}

// 方法：根据 rule_version_id 切片查询
func (n *NewRuleContent) GetRuleContentsByVersionIDs(versionIDs []int64) ([]NewRuleContent, error) {
	var rules []NewRuleContent
	if err := db.Where("rule_version_id IN ?", versionIDs).Find(&rules).Error; err != nil {
		return nil, err
	}
	return rules, nil
}

func (n *NewRuleContent) GetRuleContentsByIDs(IDs []int64) ([]NewRuleContent, error) {
	var rules []NewRuleContent
	if err := db.Where("id IN ?", IDs).Where("is_use = ?", 1).Find(&rules).Error; err != nil {
		return nil, err
	}
	return rules, nil
}

func (n *NewRuleContent) FindByIsUse(isUse int64) ([]NewRuleContent, error) {
	var ruleContents []NewRuleContent
	result := db.Where("is_use = ?", isUse).Where("is_del = ?", 0).Find(&ruleContents)
	if result.Error != nil {
		return nil, result.Error
	}
	return ruleContents, nil
}

func (n *NewRuleContent) FindByIsUseLimit(isUse int64, offset, limit int) ([]NewRuleContent, error) {
	var ruleContents []NewRuleContent
	result := db.Where("is_use = ?", isUse).Where("is_del = ?", 0).Order("rule_code " + "desc").Offset(offset).Limit(limit).Find(&ruleContents)
	if result.Error != nil {
		return nil, result.Error
	}
	return ruleContents, nil
}

func (n *NewRuleContent) FindByStatus(isUse, status int64) ([]NewRuleContent, error) {
	var ruleContents []NewRuleContent
	result := db.Where("is_use = ? AND status = ?", isUse, status).Where("is_del = ?", 0).Find(&ruleContents)
	if result.Error != nil {
		return nil, result.Error
	}
	return ruleContents, nil
}

func (n *NewRuleContent) FindByStatusLimit(isUse, status int64, offset, limit int) ([]NewRuleContent, error) {
	var ruleContents []NewRuleContent
	result := db.Where("is_use = ? AND status = ?", isUse, status).Where("is_del = ?", 0).Order("rule_code " + "desc").Offset(offset).Limit(limit).Find(&ruleContents)
	if result.Error != nil {
		return nil, result.Error
	}
	return ruleContents, nil
}

// 删除
func (n *NewRuleContent) UpdateIsDeletedByRuleVersionID(ruleVersionID int64) error {
	result := db.Model(&NewRuleContent{}).
		Where("rule_version_id = ?", ruleVersionID).
		Update("is_del", 1)
	if result.Error != nil {
		return fmt.Errorf("failed to update is_del: %v", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("no records updated for rule_version_id: %v", ruleVersionID)
	}
	return nil
}

// 创建一条记录（事务操作）
func (n *NewRuleContent) CreateRuleGroupTx(tx *gorm.DB, rulegroupid, mainVersion, subVersion int64, RuleGroupName, RuleCode, RuleDescription, packedRules, Ascen, RuleTreeID, CategoryId, status string) (string, error) {
	if tx == nil {
		return "", fmt.Errorf("failed to get database connection")
	}
	n.ID = rulegroupid
	n.RuleVersionID = GetSnowflakeID()
	n.RuleName = RuleGroupName
	n.RuleCode = RuleCode
	n.RuleDescription = RuleDescription
	n.RuleContent = packedRules
	n.UsageScenario = Ascen
	intRTid, _ := strconv.ParseInt(RuleTreeID, 10, 64)
	n.CategoryTreeID = intRTid
	intCategoryId, _ := strconv.ParseInt(CategoryId, 10, 64)
	n.CategoryId = intCategoryId
	n.UpdatedAt = time.Now().Unix()
	intstatus, _ := strconv.ParseInt(status, 10, 64)
	n.Status = intstatus
	n.IsUse = 1
	n.MainVersion = mainVersion
	n.SubVersion = subVersion
	return n.RuleContent, tx.Create(n).Error
}

func (n *NewRuleContent) UpdateStatusByRulelVersionID(VersionID int64, newStatus int64) error {
	// 查找指定 detail_version_id 的记录
	var detail NewRuleContent
	if err := db.Where("rule_version_id = ?", VersionID).First(&detail).Error; err != nil {
		return err
	}
	// 更新 status 字段
	if err := db.Model(&detail).Update("status", newStatus).Error; err != nil {
		return err
	}
	return nil
}

func (n *NewRuleContent) UpdateIsUseToZeroByruleID(tx *gorm.DB, ruleID int64) error {
	// 定义 RuleContentDetail 结构体变量用于存储查询结果
	var ruleContentDetail NewRuleContent
	// 查询符合条件的记录
	if err := tx.Model(&NewRuleContent{}).
		Where("id = ? AND is_use = ?", ruleID, 1).
		First(&ruleContentDetail).Error; err != nil {
		return err
	}
	// 更新 is_use 字段值为 0
	ruleContentDetail.IsUse = 0
	if err := tx.Save(&ruleContentDetail).Error; err != nil {
		return err
	}
	return nil
}

func (n *NewRuleContent) QueryRuleByName(ruleName string) ([]NewRuleContent, error) {
	if db == nil {
		return nil, fmt.Errorf("failed to get database connection")
	}
	// 构建查询条件
	query := db.Model(&NewRuleContent{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_code LIKE ?", "%"+ruleName+"%", "%"+ruleName+"%").Where("is_use = ?", 1)
	}
	// 执行查询
	var results []NewRuleContent
	err := query.Where("is_del = ?", 0).Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (n *NewRuleContent) CLQueryRuleByName(ruleName string) ([]NewRuleContent, error) {
	if db == nil {
		return nil, fmt.Errorf("failed to get database connection")
	}
	// 构建查询条件
	query := db.Model(&NewRuleContent{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_code LIKE ? OR rule_description LIKE ?", "%"+ruleName+"%", "%"+ruleName+"%", "%"+ruleName+"%").Where("is_use = ?", 1)
	}
	// 执行查询
	var results []NewRuleContent
	err := query.Where("is_del = ?", 0).Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (n *NewRuleContent) QueryRuleByNameLimit(ruleName string, offset, limit int) ([]NewRuleContent, error) {
	if db == nil {
		return nil, fmt.Errorf("failed to get database connection")
	}
	// 构建查询条件
	query := db.Model(&NewRuleContent{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_code LIKE ?", "%"+ruleName+"%", "%"+ruleName+"%").Where("is_use = ?", 1)
	}
	// 执行查询
	query = query.Order("rule_code " + "desc").Offset(offset).Limit(limit)
	var results []NewRuleContent
	err := query.Where("is_del = ?", 0).Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (n *NewRuleContent) CLQueryRuleByNameLimit(ruleName string, offset, limit int) ([]NewRuleContent, error) {
	if db == nil {
		return nil, fmt.Errorf("failed to get database connection")
	}
	// 构建查询条件
	query := db.Model(&NewRuleContent{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_code LIKE ? OR rule_description LIKE ?", "%"+ruleName+"%", "%"+ruleName+"%", "%"+ruleName+"%").Where("is_use = ?", 1)
	}
	// 执行查询
	query = query.Order("rule_code " + "desc").Offset(offset).Limit(limit)
	var results []NewRuleContent
	err := query.Where("is_del = ?", 0).Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (n *NewRuleContent) QueryRuleByNameOrDesc(ruleName string, status int64) ([]NewRuleContent, error) {
	if db == nil {
		return nil, fmt.Errorf("failed to get database connection")
	}
	// 构建查询条件
	query := db.Model(&NewRuleContent{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_code LIKE ? AND status = ?", "%"+ruleName+"%", "%"+ruleName+"%", status).Where("is_use = ?", 1)
	}
	// 执行查询
	var results []NewRuleContent
	err := query.Where("is_del = ?", 0).Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (n *NewRuleContent) CLQueryRuleByNameOrDesc(ruleName string, status int64) ([]NewRuleContent, error) {
	if db == nil {
		return nil, fmt.Errorf("failed to get database connection")
	}
	// 构建查询条件
	query := db.Model(&NewRuleContent{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_code LIKE ? OR rule_description LIKE ? AND status = ?", "%"+ruleName+"%", "%"+ruleName+"%", "%"+ruleName+"%", status).Where("is_use = ?", 1)
	}
	// 执行查询
	var results []NewRuleContent
	err := query.Where("is_del = ?", 0).Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (n *NewRuleContent) QueryRuleByNameOrDescSe(ruleName string, status int64, offset, limit int) ([]NewRuleContent, error) {
	if db == nil {
		return nil, fmt.Errorf("没连上")
	}
	// 构建查询条件
	query := db.Model(&NewRuleContent{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_code LIKE ? AND status = ?", "%"+ruleName+"%", "%"+ruleName+"%", status).Where("is_use = ?", 1).Order("rule_code " + "desc")
	}
	query = query.Offset(offset).Limit(limit)
	// 执行查询
	var results []NewRuleContent
	err := query.Where("is_del = 0").Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (n *NewRuleContent) CLQueryRuleByNameOrDescSe(ruleName string, status int64, offset, limit int) ([]NewRuleContent, error) {
	if db == nil {
		return nil, fmt.Errorf("没连上")
	}
	// 构建查询条件
	query := db.Model(&NewRuleContent{})
	if ruleName != "" {
		query = query.Where("rule_name LIKE ? OR rule_code LIKE ? OR rule_description LIKE ? AND status = ?", "%"+ruleName+"%", "%"+ruleName+"%", "%"+ruleName+"%", status).Where("is_use = ?", 1).Order("rule_code " + "desc")
	}
	query = query.Offset(offset).Limit(limit)
	// 执行查询
	var results []NewRuleContent
	err := query.Where("is_del = 0").Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (n *NewRuleContent) QueryRuleByNameOrCode(ruleName string, status string, offset, limit int) ([]NewRuleContent, error) {
	if db == nil {
		return nil, fmt.Errorf("没连上")
	}
	var results []NewRuleContent
	query := db.Model(&NewRuleContent{})

	if status != "" && ruleName != "" {
		intstatus, err := strconv.ParseInt(status, 10, 64)
		if err != nil {
			return nil, err
		}
		query = query.Where("rule_name LIKE ? OR rule_code LIKE ? AND status = ?", "%"+ruleName+"%", "%"+ruleName+"%", intstatus)
	}
	if ruleName != "" && status == "" {
		query = query.Where("rule_name LIKE ? OR rule_code LIKE ? ", "%"+ruleName+"%", "%"+ruleName+"%")
	}
	if limit == 0 {
		err := query.Where("is_del = ?", 0).Find(&results).Error
		if err != nil {
			return nil, err
		}
		return results, nil
	}
	query = query.Offset(offset).Limit(limit)
	err := query.Where("is_del = 0 AND is_use = 1").Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

// 通过rule_id和版本号查对应数据
func (n *NewRuleContent) FindByIDAndMinVersion(tx *gorm.DB, id int64, minVersion float64) (*NewRuleContent, error) {
	var ruleContent NewRuleContent
	// 执行查询操作
	result := tx.Where("id = ? AND min_version = ?", id, minVersion).First(&ruleContent)
	if result.Error != nil {
		return nil, result.Error
	}
	return &ruleContent, nil
}

func (n *NewRuleContent) IsRuleCodeExists(ruleCode string) (bool, error) {
	var count int64
	if err := db.Model(&NewRuleContent{}).Where("rule_code = ?", ruleCode).Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

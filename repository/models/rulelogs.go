package models

import (
	"time"
)

type RuleLog struct {
	LogID   int64     `gorm:"primaryKey;autoIncrement:true"`
	ReqIn   string    `gorm:"type:text"`
	ResOut  string    `gorm:"type:text"`
	App     string    `gorm:"type:varchar(255)"`
	User    string    `gorm:"type:varchar(255)"`
	OptDate time.Time `gorm:"default:now()"`
}

func (*RuleLog) TableName() string {
	return "rule_logs"
}

// 创建 RuleLog 记录
func CreateRuleLog(log *RuleLog) error {
	db := GetDb()
	return db.Create(log).Error
}

func (r *RuleLog) CreateLog(log *RuleLog) error {
	db := GetDb()
	return db.Create(log).Error
}

// 通过 LogID 查询 RuleLog 记录
func GetRuleLogByID(logID int64) (*RuleLog, error) {
	db := GetDb()
	var log RuleLog
	if err := db.First(&log, logID).Error; err != nil {
		return nil, err
	}
	return &log, nil
}

// 更新 RuleLog 记录
func UpdateRuleLog(log *RuleLog) error {
	db := GetDb()
	return db.Save(log).Error
}

// 删除 RuleLog 记录
func DeleteRuleLog(log *RuleLog) error {
	db := GetDb()
	return db.Delete(log).Error
}

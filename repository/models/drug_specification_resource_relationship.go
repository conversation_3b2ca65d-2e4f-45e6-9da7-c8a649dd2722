package models

type DrugSpecificationResourceRelationship struct {
	ID         int64 `gorm:"primaryKey;column:id;autoIncrement:false;default:snowflake_id();comment:雪花id"`
	SpeciID    int64 `gorm:"column:speci_id;comment:静态知识库药品id"`
	ResourceID int64 `gorm:"column:resource_id;comment:药品资源id"`
}

func (DrugSpecificationResourceRelationship) TableName() string {
	return "drug_specification_resource_relationship"
}

func (DrugSpecificationResourceRelationship) GetByResourceIDs(resourceIDs []int64) ([]DrugSpecificationResourceRelationship, error) {
	var records []DrugSpecificationResourceRelationship
	if err := db.Where("resource_id IN ?", resourceIDs).Find(&records).Error; err != nil {
		return nil, err
	}
	return records, nil
}

package models

import (
	"kmbservice/pkg/logging"
	"time"
)

type DasKnbDocContent struct {
	SnowflakeID    int64     `gorm:"column:snowflake_id;primary_key" json:"snowflake_id"`
	DocTreeID      int64     `gorm:"column:doc_tree_id" json:"doc_tree_id"`
	DocContent     string    `gorm:"column:doc_content" json:"doc_content"`
	CreatedBy      string    `gorm:"column:created_by" json:"created_by"`
	CreatedAt      time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedBy      string    `gorm:"column:updated_by" json:"updated_by"`
	UpdatedAt      time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP" json:"updated_at"`
	Version        int       `gorm:"column:version" json:"version"`
	DocDescription string    `gorm:"column:doc_description" json:"doc_description"`
	ConNum         string    `gorm:"column:con_num" json:"con_num"`
	ConName        string    `gorm:"column:con_name" json:"con_name"`
}

func (DasKnbDocContent) TableName() string {
	return "das_knb_doc_contents"
}

// 根据doc_tree_id查询数据
func (d *DasKnbDocContent) GetByDocTeeToFind(doc_tree_id int64) (DasKnbDocContent, error) {
	var drug DasKnbDocContent
	result := db.Where("doc_tree_id = ? ", doc_tree_id).Find(&drug)
	if result.Error != nil {
		logging.Error("查表[das_knb_doc_contents]错误")
	}
	return drug, result.Error
}

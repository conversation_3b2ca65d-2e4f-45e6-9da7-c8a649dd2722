package models

import (
	"time"
)

type DksKnbNoumenon struct {
	GlobalID          int64     `gorm:"column:global_id;primaryKey;autoIncrement:false;comment:'全局ID'"`
	NoumenonName      string    `gorm:"column:noumenon_name;size:255;not null;comment:'本体名称'"`
	PyCode            string    `gorm:"column:py_code;size:64;comment:'拼音'"`
	WbCode            string    `gorm:"column:wb_code;size:64;comment:'五笔'"`
	NoumenonDesc      string    `gorm:"column:noumenon_desc;size:128;comment:'本体描述'"`
	NoumenonType      int64     `gorm:"column:noumenon_type;comment:'本体类型（通用本体，领域本体）'"`
	Verson            int       `gorm:"column:verson;comment:'本体版本'"`
	CreateDate        time.Time `gorm:"column:create_date;default:now();comment:'创建日期'"`
	CreateUser        int64     `gorm:"column:create_user;comment:'创建用户'"`
	UpdateDate        time.Time `gorm:"column:update_date;comment:'更新日期'"`
	UpdateUser        int64     `gorm:"column:update_user;comment:'更新用户'"`
	Status            int       `gorm:"column:status;comment:'状态'"`
	IsDelete          int       `gorm:"column:is_delete;default:0;comment:'删除'"`
	NoumenonSubType   int64     `gorm:"column:noumenon_sub_type;comment:'本体子类型领域本体分类'"`
	NoumenonShowName  string    `gorm:"column:noumenon_show_name;size:64;comment:'本体显示名称'"`
	DefineSpaceSoid   int64     `gorm:"column:define_space_soid;comment:'定义空间'"`
	Lable             string    `gorm:"column:lable;size:64;comment:'本体标签'"`
	NoumenonThirdType int64     `gorm:"column:noumenon_third_type;comment:'本体第三种类型'"`
	ConceptID         int64     `gorm:"column:concept_id;comment:'本体概念ID'"`
	FormID            string    `gorm:"column:form_id;size:255;comment:'表单ID'"`
	NoumenonKey       string    `gorm:"column:noumenon_key;size:255;unique;comment:'用于规则中使用的名称表达，主要用于编写规则使用名称，需要唯一'"`
}

// TableName 设置表名
func (DksKnbNoumenon) TableName() string {
	return "dks_knb_noumenon"
}

func (d *DksKnbNoumenon) FindByGlobalID(globalID int64) (*DksKnbNoumenon, error) {
	var noumenon DksKnbNoumenon
	result := db.Where("global_id = ?", globalID).First(&noumenon)
	if result.Error != nil {
		return nil, result.Error
	}
	return &noumenon, nil
}

package models

import (
	"fmt"
	"gorm.io/gorm"
	"kmbservice/pkg/logging"
)

type RuleCategory struct {
	Id                  int64  `gorm:"primaryKey;autoIncrement:true;column:id" json:"Id"`      //type:int8     comment:    version:2024-03-29 12:50
	CategoryName        string `gorm:"column:category_name" json:"CategoryName"`               //type:string   comment:    version:2024-03-29 12:50
	CategoryDescription string `gorm:"column:category_description" json:"CategoryDescription"` //type:string   comment:    version:2024-03-29 12:50
}

// TableName overrides the default table name used by Gorm
func (RuleCategory) TableName() string {
	return "rule_category"
}

// 新增一条规则分类
func CreateCategory(name string, description string) (int64, error) {
	category := RuleCategory{
		CategoryName:        name,
		CategoryDescription: description,
	}
	result := db.Create(&category)
	if err := result.Error; err != nil {
		logging.Error(err, "新增内容失败")
		return 0, err
	}
	return category.Id, nil
}

// 更新分类
func UpdateCategory(id int64, name string, description string) bool {
	// Use Gorm's Updates method for efficient updates
	result := db.Model(&RuleCategory{}).Where("id = ?", id).Updates(map[string]interface{}{
		"category_name":        name,
		"category_description": description,
	})

	// Check for errors
	if err := result.Error; err != nil {
		logging.Error(err, "failed to update category")
		return false
	}
	return true // Update successful
}

// 删除分类
func DeleteCategory(id int64) bool {
	// Use Gorm's Delete method for efficient deletion
	result := db.Where("id = ?", id).Delete(&RuleCategory{})

	// Check for errors
	if err := result.Error; err != nil {
		logging.Error(err, "failed to delete category")
		return false
	}

	return true // Deletion successful
}

// GetCategory retrieves a RuleCategory record by ID
func GetCategory(id int64) *RuleCategory {
	var category RuleCategory
	// Use Gorm's First method to find a single record
	result := db.Where("id = ?", id).First(&category)
	if err := result.Error; err != nil {
		logging.Error(err, "failed to get category")
		return nil
	}
	return &category
}
func GetCategoryList() []RuleCategory {
	var category []RuleCategory
	result := db.Find(&category)
	if err := result.Error; err != nil {
		logging.Error(err, "failed to get category")
		return nil
	}
	return category
}

// RuleTree 表示规则树表的结构体
type RuleTree struct {
	ID           int64  `gorm:"primaryKey;autoIncrement:true;column:id"`
	ParentID     int64  `gorm:"index;column:parent_id"`
	RuleName     string `gorm:"not null;column:rule_name"`
	IsActive     bool   `gorm:"not null;default:true;column:is_active"`
	DisplayOrder int    `gorm:"column:display_order"`
}

func (RuleTree) TableName() string {
	return "rule_tree"
}
func AddTree(parentID int64, ruleName string, displayOrder int) (int64, bool) {
	tree := &RuleTree{
		ParentID:     parentID,
		RuleName:     ruleName,
		DisplayOrder: displayOrder,
	}
	result := db.Create(tree)
	if err := result.Error; err != nil {
		logging.Error(err.Error(), "failed to create tree")
		return 0, false
	}
	return tree.ID, true
}
func UpdateTree(id int64, parentId int64, ruleName string, active bool, order int) bool {
	result := db.Model(&RuleTree{}).Where("id=?", id).Updates(map[string]interface{}{
		"is_active":     active,
		"display_order": order,
		"rule_name":     ruleName,
		"parent_id":     parentId,
	})
	if err := result.Error; err != nil {
		logging.Error(err.Error(), "failed to update tree")
		return false
	}
	return true
}
func DeleteTree(id int64) bool {
	result := db.Where("id=?", id).Delete(&RuleTree{})
	if err := result.Error; err != nil {
		logging.Error(err.Error(), "failed to update tree")
		return false
	}
	return true
}
func GetTree(id int64) *RuleTree {
	var tree RuleTree
	err := db.Where("id=?", id).First(&tree).Error
	if err != nil {
		return nil
	}
	return &tree
}
func GetAllRree() []RuleTree {
	var trees []RuleTree
	if err := db.Find(&trees).Error; err != nil {
		logging.Error(err, "获取全部树失败")
		return nil
	}
	return trees
}

// RuleContent 表示规则内容表的结构体
type RuleContent struct {
	ID              int64    `gorm:"primaryKey;autoIncrement:true;column:id"`
	CategoryTreeID  int64    `gorm:"not null;column:category_tree_id"`
	RuleName        string   `gorm:"not null;column:rule_name"`
	RuleDescription string   `gorm:"column:rule_description"`
	UsageScenario   string   `gorm:"size:64;column:usage_scenario"`
	Status          int64    `gorm:"column:status"` //1为可用，0为不可用
	RuleContent     string   `gorm:"type:text;column:rule_content"`
	IsDeleted       int64    `gorm:"column:is_del"`
	RuleTree        RuleTree `gorm:"foreignKey:CategoryTreeID"`
	LastModel
}

func (RuleContent) TableName() string {
	return "rule_content"
}
func AddContent(categoryTreeID int64, ruleName string, usageScenario string, status int64, rulecontent string) (int64, bool) {
	// 创建 RuleContent 对象
	content := RuleContent{
		CategoryTreeID: categoryTreeID,
		RuleName:       ruleName,
		UsageScenario:  usageScenario,
		Status:         status,
		RuleContent:    rulecontent,
	}
	// 执行创建操作
	result := db.Create(&content)
	if result.Error != nil {
		return 0, false
	}

	// 返回新增的 ID 和操作结果
	return content.ID, true
}

func UpdateContent(id int64, categoryTreeID int64, ruleName string, usageScenario string, status int64, ruleContent string) bool {
	result := db.Model(&RuleContent{}).Where("id=?", id).Updates(map[string]interface{}{
		"category_tree_id": categoryTreeID,
		"usage_scenario":   usageScenario,
		"status":           status,
		"rule_name":        ruleName,
		"rule_content":     ruleContent,
	})
	if err := result.Error; err != nil {
		logging.Error(err.Error(), "failed to update content")
		return false
	}
	return true
}
func DeleteContent(id int64) bool {
	err := db.Where("id = ?", id).Delete(&RuleContent{}).Error
	if err != nil {
		logging.Error(err, "删除内容失败")
		return false
	}
	return true
}
func GetContent(id int64) *NewRuleContent {
	var content NewRuleContent
	err := db.Where("id=? AND is_del = ? AND is_use = ?", id, 0, 1).First(&content).Error
	if err != nil {
		fmt.Println(err)
		return nil
	} else {
		return &content
	}
}
func GetAllContent(pageNum int, pageSize int, maps interface{}) (trees []RuleContent) {
	db.Where(maps).Offset(pageNum).Limit(pageSize).Find(&trees)
	return trees
}
func GetAllContents(maps interface{}) int64 {
	var count int64
	if err := db.Model(&RuleContent{}).Where(maps).Count(&count).Error; err != nil {
		return 0
	}
	return count
}
func CreateTbl() {
	MigrateModels(db)
}

// MigrateModels 用于迁移数据库结构
func MigrateModels(db *gorm.DB) error {
	if err := db.AutoMigrate(&RuleCategory{}); err != nil {
		return err
	}
	if err := db.AutoMigrate(&RuleTree{}); err != nil {
		return err
	}
	if err := db.AutoMigrate(&RuleContent{}); err != nil {
		return err
	}
	return nil
}

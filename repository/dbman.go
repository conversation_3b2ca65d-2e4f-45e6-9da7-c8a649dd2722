package repository

import (
	"context"
	"encoding/json"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"io/ioutil"
)

type Config struct {
	DB struct {
		URL      string `json:"url"`
		Username string `json:"username"`
		Password string `json:"password"`
		Graph    string `json:"graph"`
	} `json:"db"`
}

func loadConfig(filename string) (Config, error) {
	var config Config
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return config, err
	}
	err = json.Unmarshal(data, &config)
	return config, err
}

func getSession(ctx context.Context, graph string) (neo4j.SessionWithContext, func()) {
	config, err := loadConfig("config.json")
	if err != nil {
		panic(err)
	}
	driver, err := neo4j.NewDriverWithContext(config.DB.URL, neo4j.BasicAuth(config.DB.Username, config.DB.Password, ""))
	if err != nil {
		panic(err)
	}
	session := driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: config.DB.Graph})
	// 返回关闭数据库驱动程序的函数
	closeDriver := func() {
		session.Close(ctx)
		driver.Close(ctx)
	}
	return session, closeDriver
}
func RunQL(ctx context.Context, cypher string, params map[string]any) bool {
	session, closeDriver := getSession(ctx, "default")
	defer closeDriver()
	_, err := session.Run(ctx, cypher, params)
	if err != nil {
		panic(err)
		return false
	}
	return true
}

func Runresult(ctx context.Context, cypher string, params map[string]any) (string, bool) {
	//start := time.Now()
	session, closeDriver := getSession(ctx, "PsychiatricDiseases")
	//elapsed := time.Since(start)
	//fmt.Printf("Runresult执行代码消耗时间-连接数据库获取session时间：%s\n", elapsed)
	defer closeDriver()
	res, err := session.Run(ctx, cypher, params)
	//elapsed = time.Since(start)
	//fmt.Printf("Runresult执行代码消耗时间-图数据库查询完成消耗时间：%s\n", elapsed)
	if err != nil {
		panic(err)
		return "", false
	}
	//elapsed = time.Since(start)
	//fmt.Printf("Runresult执行代码消耗时间-返回数据转换格式消耗时间records：%s\n", elapsed)
	records, err := res.Collect(ctx)
	if err != nil {
		panic(err)
		return "", false
	}
	//elapsed = time.Since(start)
	//fmt.Printf("Runresult执行代码消耗时间-返回数据转换格式消耗时间allRecords前：%s\n", elapsed)
	var allRecords []interface{}
	for _, record := range records {
		allRecords = append(allRecords, record)
	}
	//elapsed = time.Since(start)
	//fmt.Printf("Runresult执行代码消耗时间-返回数据转换格式消耗时间allRecords后：%s\n", elapsed)
	jsonData, err := json.Marshal(allRecords)
	if err != nil {
		panic(err)
		return "", false
	}
	if string(jsonData) == "null" || len(jsonData) == 0 {
		return "", false
	}
	///elapsed = time.Since(start)
	//fmt.Printf("Runresult执行代码消耗时间-返回数据JSON转换格式消耗时间：%s\n", elapsed)
	return string(jsonData), true
}

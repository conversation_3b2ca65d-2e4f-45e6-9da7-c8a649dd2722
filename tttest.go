package main

import (
	"encoding/json"
	"fmt"
	"strings"
)

type RuleCreateNewRequest struct {
	RuleName            string           `json:"rule_name"`             // 规则名称
	LogicalOperation    string           `json:"logical_operation"`     //逻辑运算符
	RuleBody            Condition        `json:"rule_body"`             // 规则条件
	RuleResult          ResultContentNew `json:"rule_result"`           // 结果内容
	RuleID              string           `json:"rule_id"`               //规则id
	RuleDesc            string           `json:"rule_desc"`             //规则描述
	RuleParticulars     []string         `json:"rule_particulars"`      //规则详情
	RuleClassficationID []int64          `json:"rule_classfication_id"` //规则分类id
}

type Condition struct {
	Operator   string      `json:"operator"` //
	Detail     []Condition `json:"detail"`
	Object     string      `json:"object"`
	Property   string      `json:"property"`
	Comparison string      `json:"comparison"`
	Value      string      `json:"value"`
}

type ResultContentNew struct {
	Noumenon string `json:"noumenon"` //本体
	Method   string `json:"method"`   //方法id
	Message  string `json:"msg"`      // 消息类型，例如 "warning"
	Code     string `json:"code"`     // 代码，例如 "GZ0101005000"
	Content  string `json:"content"`  // 消息内容，例如 "佐匹克隆片肝功能不全者慎用"
}

func buildConditionString(cond Condition) string {
	if cond.Object != "" && cond.Property != "" && cond.Comparison != "" && cond.Value != "" {
		// Base case: if it's a leaf node (represents the condition), format and return the string
		return fmt.Sprintf("%s.%s%s%s", cond.Object, cond.Property, cond.Comparison, cond.Value)
	}

	if len(cond.Detail) > 0 {
		// Recursive case: if there are child conditions, process each with the current operator
		var parts []string
		for _, detail := range cond.Detail {
			part := buildConditionString(detail)
			parts = append(parts, part)
		}

		// Join the parts with the current operator
		return fmt.Sprintf("(%s)", strings.Join(parts, fmt.Sprintf(" %s ", cond.Operator)))
	}

	return ""
}

func main() {
	jsonData := `{
  "rule_name": "Age-specific diagnostic plausibility of zolpidem tartrate tablets\"  \"酒石酸唑吡坦片老年慎用",
  "logical_operation": "||",
  "rule_body": {
    "operator": "AND",
    "detail": [
      {
        "operator": "OR",
        "detail": [
          {
            "operator": "AND",
            "detail": [
              {
                "object": "Patient",
                "property": "Age",
                "comparison": ">",
                "value": "65"
              },
              {
                "object": "Patient",
                "property": "Sex",
                "comparison": "=",
                "value": "女"
              }
            ]
          },
          {
            "operator": "AND",
            "detail": [
              {
                "object": "Drug",
                "property": "Name",
                "comparison": "<",
                "value": "14"
              },
              {
                "object": "Drug",
                "property": "dosg",
                "comparison": "=",
                "value": "女"
              }
            ]
          },
          {
            "operator": "AND",
            "detail": [
              {
                "object": "Rep",
                "property": "item",
                "comparison": "<",
                "value": "14"
              },
              {
                "object": "Rep",
                "property": "unit",
                "comparison": "=",
                "value": "g"
              }
            ]
          }
        ]
      }
    ]
  },
  "rule_result": {
    "noumenon": "patient",
    "method": "AddMessage",
    "code": "GZ0101003000",
    "content": "酒石酸唑吡坦片老年慎用！",
    "msg": "01"
  }
}`
	var root RuleCreateNewRequest
	err := json.Unmarshal([]byte(jsonData), &root)
	if err != nil {
		fmt.Println("Error parsing JSON:", err)
		return
	}
	result := buildConditionString(root.RuleBody)
	fmt.Println(result)
}

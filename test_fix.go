package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
)

// 测试请求数据
var testRequest = map[string]interface{}{
	"system_code_mapping": true,
	"corp_id":             "1",
	"input_data": map[string]interface{}{
		"rule": map[string]interface{}{
			"rule_id_list": []string{
				"1853333846653568186",
				"1853337716653995226",
				"1853339848736158943",
			},
		},
		"patient": map[string]interface{}{
			"patient_id":       "",
			"patient_name":     "^华^杏^妹^之^子",
			"patient_gender":   "1",
			"patient_birthday": "2023-07-19",
		},
		"western_medicine_doctor_orders_list": []map[string]interface{}{
			{
				"medication_prescription_item": "72702",
				"drug_dosage":                  6,
				"drug_dosage_unit":             "22",
				"adminis_tration_route":        "口服",
				"order_create_time":            "1756448536000",
			},
		},
		"examination_doctor_order_list":    []interface{}{},
		"surgical_doctor_order_list":       []interface{}{},
	},
}

func main() {
	// 将请求数据转换为JSON
	jsonData, err := json.Marshal(testRequest)
	if err != nil {
		fmt.Printf("JSON编码错误: %v\n", err)
		return
	}

	// 发送POST请求到本地服务器
	url := "http://**********:9893/api/engine/rule_exec"
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("请求发送失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}

	// 打印响应
	fmt.Printf("响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("响应内容: %s\n", string(body))

	// 解析响应JSON
	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		fmt.Printf("JSON解析错误: %v\n", err)
		return
	}

	// 检查响应结构
	if data, ok := response["data"].(map[string]interface{}); ok {
		if execData, ok := data["exec_data"].([]interface{}); ok && len(execData) > 0 {
			fmt.Printf("✅ 修复成功！API返回了期望的规则执行结果\n")
			fmt.Printf("规则执行数据: %+v\n", execData)
		} else {
			fmt.Printf("❌ 修复未完全成功，exec_data为空或格式不正确\n")
		}
	} else {
		fmt.Printf("❌ 响应格式不正确\n")
	}
}

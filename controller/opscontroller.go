package controller

import (
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/mitchellh/mapstructure"
	"kmbservice/internals/rulengines/services/kbservice"
	"kmbservice/repository"
	"net/http"
	"strconv"
	"strings"
	"time"
)

// 统一格式化输出时间

// 谨慎操作，用于测试
func Deldb() bool {
	ctx := context.Background()
	sql := "CALL db.dropDB()"
	isOk := repository.RunQL(ctx, sql, nil)
	if isOk {
		return true
	} else {
		return false
	}
}
func DeldbHandler(c *gin.Context) {
	if Deldb() {
		c.JSO<PERSON>(http.StatusOK, gin.H{"message": "数据删除成功"})
	} else {
		c.JSON(http.StatusInternalServerError, gin.H{"message": "删除数据库失败"})
	}
}

// 授权认证和安全检测，后续再另外实现此处逻辑
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		// 检查token是否有效，这里假设token有效
		if token != "valid_token" {
			c.JSON(http.StatusUnauthorized, Response{
				Code:    http.StatusUnauthorized,
				Message: "Unauthorized access",
				Data:    nil,
			})
			c.Abort()
			return
		}
		c.Next()
	}
}
func getbeing() time.Time {
	start := time.Now()
	return start
}
func printtrace(start time.Time) {
	elapsed := time.Since(start)
	fmt.Printf("执行代码消耗时间-解析接收函数：%s\n", elapsed)
}

// 根据诊断获取量表推荐
func GetTablesByDis(c *gin.Context) {

	var requestData ReqData
	var values []string
	type group struct {
		AgeGroup string `json:"ageGroup"`
	}
	type diease struct {
		DiseaseName string `json:"diseaseName"`
		DiseaseId   string `json:"diseaseId"`
	}
	var ageGroup group
	var disease []diease
	responeData := Initres()
	if err := c.BindJSON(&requestData); err != nil {
		responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
		responeData.ResponseTime = GetNow()
		responeData.Message = err.Error()
		c.JSON(http.StatusBadRequest, responeData)
		return
	}

	inputDataSlice, ok := requestData.InputData.([]interface{})
	if !ok {
		responeData.Message = "Input data is not a slice"
		c.JSON(http.StatusInternalServerError, responeData)
		return
	}

	for _, entry := range inputDataSlice {
		if data, ok := entry.(map[string]interface{}); ok {
			if groupData, ok := data["group"]; ok {
				var group struct {
					AgeGroup string `json:"ageGroup"`
				}
				if err := mapstructure.Decode(groupData, &group); err != nil {
					responeData.Message = "Error decoding group data"
					c.JSON(http.StatusInternalServerError, responeData)
					return
				}
				ageGroup.AgeGroup = group.AgeGroup

			}

			if diseaseData, ok := data["disease"]; ok {
				if err := mapstructure.Decode(diseaseData, &disease); err != nil {
					responeData.Message = "Error decoding disease data"
					c.JSON(http.StatusInternalServerError, responeData)
					return
				}

			}
		}
	}
	if len(disease) > 0 {
		for _, d := range disease {
			temp := fmt.Sprintf(" '%s'", d.DiseaseName)
			values = append(values, temp)
		}
		//inClause := fmt.Sprintf("[%s]", strings.Join(values, ","))

		ret := kbservice.GetTablesByDis(ageGroup.AgeGroup, values)

		if ret != "" {

			responeData.OutputData = ret
			responeData.Message = "success"
			responeData.ResponseTime = GetNow()
		}
	}
	c.JSON(http.StatusOK, responeData)
}

// 诊断组和症状推荐量表
func GetTestTable(c *gin.Context) {
	var requestData ReqData
	type group struct {
		AgeGroup string `json:"ageGroup"`
	}
	var ageGroup group
	var symptoms []kbservice.Simplesymn
	responeData := Initres()
	// 解析 JSON 请求体
	if err := c.BindJSON(&requestData); err != nil {
		responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
		responeData.ResponseTime = GetNow()
		responeData.Message = err.Error()
		c.JSON(http.StatusBadRequest, responeData)
		return
	}
	if inputData, ok := requestData.InputData.([]interface{}); ok {
		for _, item := range inputData {
			if data, ok := item.(map[string]interface{}); ok {
				if groupData, ok := data["group"]; ok {
					err := mapstructure.Decode(groupData, &ageGroup)
					if err != nil {

						responeData.ResponseTime = GetNow()
						responeData.Message = err.Error()
						c.JSON(http.StatusInternalServerError, responeData)
						return
					}
				} else if symptomsData, ok := data["symptom"]; ok {
					var symm []kbservice.Simplesymn
					err := mapstructure.Decode(symptomsData, &symm)
					if err != nil {
						responeData.ResponseTime = GetNow()
						responeData.Message = err.Error()
						c.JSON(http.StatusInternalServerError, responeData)
						return
					}
					symptoms = append(symptoms, symm...)
				}
			}
		}
	}
	// 解析 JSON 数据

	if len(symptoms) > 0 {
		ret := kbservice.GetTables(ageGroup.AgeGroup, symptoms)
		if ret != "" {

			responeData.OutputData = ret
			responeData.ResponseTime = GetNow()
			responeData.Message = "success"
		}
	}
	c.JSON(http.StatusOK, responeData)
}

// 根据主要症状获得推荐
func HandleDiagnosis(c *gin.Context) {
	// 定义结构体来解析 JSON 请求体
	var requestData ReqData
	responeData := Initres()
	// 解析 JSON 请求体
	if err := c.BindJSON(&requestData); err != nil {
		responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
		responeData.ResponseTime = GetNow()
		responeData.Message = err.Error()
		c.JSON(http.StatusBadRequest, responeData)
		return
	}
	// 检查 InputData 是否是字符串
	inputDataArray, ok := requestData.InputData.([]interface{})
	if !ok {
		responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
		responeData.ResponseTime = GetNow()
		responeData.Message = " Input data is not an array"
		c.JSON(http.StatusBadRequest, responeData)
		return
	}
	// 检查 乳腺专科入参 是否是字符串
	BreastinputDataArray, ok := requestData.BreastInputData.([]interface{})
	if !ok {
		responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
		responeData.ResponseTime = GetNow()
		responeData.Message = " BreastInput data is not an array"
		c.JSON(http.StatusBadRequest, responeData)
		return
	}

	breastType := requestData.BreastType

	// 解析 JSON 数据
	var simplesymnArr []kbservice.Simplesymn
	for _, item := range inputDataArray {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
			responeData.ResponseTime = GetNow()
			responeData.Message = " Input data item is not a map"
			c.JSON(http.StatusBadRequest, responeData)
			continue
		}
		signOrSymptom, ok1 := itemMap["signOrSymptom"].(string)
		symptomIdentifier, ok2 := itemMap["symptomIdentifier"].(string)
		if !ok1 || !ok2 {
			fmt.Println("signOrSymptom or symptomIdentifier is not a string")
			continue
		}
		simplesymn := kbservice.Simplesymn{
			SignOrSymptom:     signOrSymptom,
			SymptomIdentifier: symptomIdentifier,
		}
		simplesymnArr = append(simplesymnArr, simplesymn)
	}

	//解析乳腺专科传进来的参数
	var breastParamslist []kbservice.BreastParam
	//解析传进来的数据
	for _, item := range BreastinputDataArray {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
			responeData.ResponseTime = GetNow()
			responeData.Message = " BreastInput data item is not a map"
			c.JSON(http.StatusBadRequest, responeData)
			continue
		}
		BreastsignOrSymptom, ok2 := itemMap["breast_sign_or_symptom"].(string)
		BreastDiseaseOrSyndrome, ok3 := itemMap["breast_disease_or_syndrome"].(string)
		BreastLocalTemperatureSituation, ok4 := itemMap["breast_local_temperature_situation"].(string)
		BreastTextureOfTheMmass, ok5 := itemMap["breast_texture_of_the_mmass"].(string)
		BreastMobilityOfTheMass, ok6 := itemMap["breast_mobility_of_the_masse"].(string)
		BreastTumorRADSClassification, ok7 := itemMap["breast_tumor_rads_classification"].(string)
		BreastTNMStagingOfPrimaryTumors, ok8 := itemMap["breast_tnm_staging_of_primary_tumors"].(string)
		TNMStagingOfRegionalLymphNodes, ok9 := itemMap["tnm_staging_of_regional_lymph_nodes"].(string)
		TNMStagingForDistantMetastasis, ok10 := itemMap["tnm_staging_for_distant_metastasis"].(string)
		BreastType, ok11 := itemMap["breast_type"].(string)
		if !ok2 || !ok3 || !ok4 || !ok5 || !ok6 || !ok7 || !ok8 || !ok9 || !ok10 || !ok11 {
			fmt.Println("有入参不为字符串的参数")
			continue
		}
		breast_sign_or_symptom, _ := strconv.ParseInt(BreastsignOrSymptom, 10, 64)
		breast_disease_or_syndrome, _ := strconv.ParseInt(BreastDiseaseOrSyndrome, 10, 64)
		breast_local_temperature_situation, _ := strconv.ParseInt(BreastLocalTemperatureSituation, 10, 64)
		breast_texture_of_the_mmass, _ := strconv.ParseInt(BreastTextureOfTheMmass, 10, 64)
		breast_mobility_of_the_masse, _ := strconv.ParseInt(BreastMobilityOfTheMass, 10, 64)
		breast_tumor_rads_classification, _ := strconv.ParseInt(BreastTumorRADSClassification, 10, 64)
		breast_tnm_staging_of_primary_tumors, _ := strconv.ParseInt(BreastTNMStagingOfPrimaryTumors, 10, 64)
		tnm_staging_of_regional_lymph_nodes, _ := strconv.ParseInt(TNMStagingOfRegionalLymphNodes, 10, 64)
		tnm_staging_for_distant_metastasis, _ := strconv.ParseInt(TNMStagingForDistantMetastasis, 10, 64)
		breastparams := kbservice.BreastParam{
			BreastSignOrSymptom:             breast_sign_or_symptom,
			BreastDiseaseOrSyndrome:         breast_disease_or_syndrome,
			BreastLocalTemperatureSituation: breast_local_temperature_situation,
			BreastTextureOfTheMmass:         breast_texture_of_the_mmass,
			BreastMobilityOfTheMass:         breast_mobility_of_the_masse,
			BreastTumorRADSClassification:   breast_tumor_rads_classification,
			BreastTNMStagingOfPrimaryTumors: breast_tnm_staging_of_primary_tumors,
			TNMStagingOfRegionalLymphNodes:  tnm_staging_of_regional_lymph_nodes,
			TNMStagingForDistantMetastasis:  tnm_staging_for_distant_metastasis,
			BreastType:                      BreastType,
		}
		breastParamslist = append(breastParamslist, breastparams)
	}
	//传递乳腺专科的参数
	var breastParams kbservice.BreastParam
	if requestData.BreastSignOrSymptom != "0" {
		i, _ := strconv.ParseInt(requestData.BreastSignOrSymptom, 10, 64)
		breastParams.BreastSignOrSymptom = i
	}
	if requestData.BreastDiseaseOrSyndrome != "" {
		i, _ := strconv.ParseInt(requestData.BreastDiseaseOrSyndrome, 10, 64)
		breastParams.BreastDiseaseOrSyndrome = i
	}
	if requestData.BreastLocalTemperatureSituation != "" {
		i, _ := strconv.ParseInt(requestData.BreastSignOrSymptom, 10, 64)
		breastParams.BreastLocalTemperatureSituation = i
	}
	if requestData.BreastTextureOfTheMmass != "" {
		i, _ := strconv.ParseInt(requestData.BreastTextureOfTheMmass, 10, 64)
		breastParams.BreastTextureOfTheMmass = i
	}
	if requestData.BreastMobilityOfTheMass != "" {
		i, _ := strconv.ParseInt(requestData.BreastMobilityOfTheMass, 10, 64)
		breastParams.BreastMobilityOfTheMass = i
	}
	if requestData.BreastTumorRADSClassification != "" {
		i, _ := strconv.ParseInt(requestData.BreastTumorRADSClassification, 10, 64)
		breastParams.BreastTumorRADSClassification = i
	}
	if requestData.BreastTNMStagingOfPrimaryTumors != "" {
		i, _ := strconv.ParseInt(requestData.BreastTNMStagingOfPrimaryTumors, 10, 64)
		breastParams.BreastTNMStagingOfPrimaryTumors = i
	}
	if requestData.TNMStagingOfRegionalLymphNodes != "" {
		i, _ := strconv.ParseInt(requestData.TNMStagingOfRegionalLymphNodes, 10, 64)
		breastParams.TNMStagingOfRegionalLymphNodes = i
	}
	if requestData.TNMStagingForDistantMetastasis != "" {
		i, _ := strconv.ParseInt(requestData.TNMStagingForDistantMetastasis, 10, 64)
		breastParams.TNMStagingForDistantMetastasis = i
	}
	switch requestData.DType {
	case "1":
		//乳腺专科
		if len(breastParamslist) > 0 {
			ret := kbservice.GetBreastDiseases(simplesymnArr, 1, breastParamslist, breastType)
			if ret != "" {
				responeData.OutputData = ret
				responeData.ResponseTime = GetNow()
				responeData.Message = "success"
			}
		}
	case "2":
		if len(simplesymnArr) > 0 {
			ret := kbservice.GetDiseases(simplesymnArr, 1)
			if ret != "" {
				responeData.OutputData = ret
				responeData.ResponseTime = GetNow()
				responeData.Message = "success"
			}
		}
	}
	c.JSON(http.StatusOK, responeData)
}

// 根据诊断推荐中医证候
func HandleTcmSymn(c *gin.Context) {
	var requestData ReqData
	responeData := Initres()
	// 解析 JSON 请求体
	if err := c.BindJSON(&requestData); err != nil {
		responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
		responeData.ResponseTime = GetNow()
		responeData.Message = err.Error()
		c.JSON(http.StatusBadRequest, responeData)
		return
	}
	// 检查 InputData 是否是字符串
	inputDataArray, ok := requestData.InputData.([]interface{})
	if !ok {
		responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
		responeData.ResponseTime = GetNow()
		responeData.Message = " Input data is not an array"
		c.JSON(http.StatusBadRequest, responeData)
		return
	}

	// 解析 JSON 数据
	var simplesymnArr []kbservice.Simplesymn
	for _, item := range inputDataArray {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
			responeData.ResponseTime = GetNow()
			responeData.Message = " Input data item is not a map"
			c.JSON(http.StatusBadRequest, responeData)
			continue
		}
		signOrSymptom, ok1 := itemMap["signOrSymptom"].(string)
		symptomIdentifier, ok2 := itemMap["symptomIdentifier"].(string)
		if !ok1 || !ok2 {
			fmt.Println("signOrSymptom or symptomIdentifier is not a string")
			continue
		}
		simplesymn := kbservice.Simplesymn{
			SignOrSymptom:     signOrSymptom,
			SymptomIdentifier: symptomIdentifier,
		}
		simplesymnArr = append(simplesymnArr, simplesymn)
	}
	if len(simplesymnArr) > 0 {
		ret := kbservice.GettcmBydiseaseGroup(simplesymnArr)
		if ret != "" {

			responeData.OutputData = ret
			responeData.ResponseTime = GetNow()
		}
	}
	c.JSON(http.StatusOK, responeData)

}

// 根据伴随症状获得推荐
func HandleDiagnosis_acc(c *gin.Context) {
	// 定义结构体来解析 JSON 请求体
	var requestData ReqData
	responeData := Initres()
	// 解析 JSON 请求体
	if err := c.BindJSON(&requestData); err != nil {
		responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
		responeData.ResponseTime = GetNow()
		responeData.Message = err.Error()
		c.JSON(http.StatusBadRequest, responeData)
		return
	}
	// 检查 InputData 是否是字符串
	inputDataArray, ok := requestData.InputData.([]interface{})
	if !ok {
		responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
		responeData.ResponseTime = GetNow()
		responeData.Message = " Input data is not an array"
		c.JSON(http.StatusBadRequest, responeData)
		return
	}

	// 解析 JSON 数据
	var simplesymnArr []kbservice.Simplesymn
	for _, item := range inputDataArray {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
			responeData.ResponseTime = GetNow()
			responeData.Message = " Input data item is not a map"
			c.JSON(http.StatusBadRequest, responeData)
			continue
		}
		signOrSymptom, ok1 := itemMap["signOrSymptom"].(string)
		symptomIdentifier, ok2 := itemMap["symptomIdentifier"].(string)
		if !ok1 || !ok2 {
			fmt.Println("signOrSymptom or symptomIdentifier is not a string")
			continue
		}
		simplesymn := kbservice.Simplesymn{
			SignOrSymptom:     signOrSymptom,
			SymptomIdentifier: symptomIdentifier,
		}
		simplesymnArr = append(simplesymnArr, simplesymn)
	}
	//var breastParams kbservice.BreastParam
	if len(simplesymnArr) > 0 {
		ret := kbservice.GetDiseases(simplesymnArr, 2)
		if ret != "" {

			responeData.OutputData = ret
			responeData.ResponseTime = GetNow()
			responeData.Message = "success"
		}
	}
	c.JSON(http.StatusOK, responeData)
}

func Handletcms_tcmdis(c *gin.Context) {
	var requestData ReqData
	var values []string
	type diease struct {
		DiseaseName string `json:"diseaseName"`
		DiseaseId   string `json:"diseaseId"`
	}
	var disease []diease
	responeData := Initres()
	if err := c.BindJSON(&requestData); err != nil {
		responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
		responeData.ResponseTime = GetNow()
		responeData.Message = err.Error()
		c.JSON(http.StatusBadRequest, responeData)
		return
	}
	inputDataSlice, ok := requestData.InputData.([]interface{})
	if !ok {
		responeData.Message = "Input data is not a slice"
		responeData.ResponseTime = GetNow()
		responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
		c.JSON(http.StatusInternalServerError, responeData)
		return
	}
	for _, entry := range inputDataSlice {
		if data, ok := entry.(map[string]interface{}); ok {
			if diseaseData, ok := data["disease"]; ok {
				if err := mapstructure.Decode(diseaseData, &disease); err != nil {
					responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
					responeData.Message = "Error decoding disease data"
					responeData.ResponseTime = GetNow()
					c.JSON(http.StatusInternalServerError, responeData)
					return
				}

			}
		}
	}
	if len(disease) > 0 {
		for _, d := range disease {
			temp := fmt.Sprintf(" '%s'", d.DiseaseName)
			values = append(values, temp)
		}
		inClause := fmt.Sprintf("[%s]", strings.Join(values, ","))
		ret := kbservice.GettcmSymBytcmDis(inClause)
		if ret != "" {

			responeData.OutputData = ret
			responeData.ResponseTime = GetNow()
			responeData.Message = "success"
		}
	}
	c.JSON(http.StatusOK, responeData)
}

func HandleRecommendation(c *gin.Context) {
	var requestData ReqData
	var values []string
	type group struct {
		RecommendationType int `json:"recommendationType"`
	}
	type diease struct {
		DiseaseName string `json:"diseaseName"`
		DiseaseId   string `json:"diseaseId"`
	}
	var typeclass group
	var disease []diease
	responeData := Initres()
	if err := c.BindJSON(&requestData); err != nil {
		responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
		responeData.ResponseTime = GetNow()
		responeData.Message = err.Error()
		c.JSON(http.StatusBadRequest, responeData)
		return
	}
	inputDataSlice, ok := requestData.InputData.([]interface{})
	if !ok { //断言
		responeData.Message = "Input data is not a slice"
		responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
		c.JSON(http.StatusInternalServerError, responeData)
		return
	}
	//检查乳腺专科入参类型
	BreastinputDataArray, ok := requestData.BreastInputData.([]interface{})
	if !ok {
		responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
		responeData.ResponseTime = GetNow()
		responeData.Message = " BreastInput data is not an array"
		c.JSON(http.StatusBadRequest, responeData)
		return
	}

	for _, entry := range inputDataSlice {
		if data, ok := entry.(map[string]interface{}); ok {
			if groupData, ok := data["group"]; ok {
				var group struct {
					RecommendationType int `json:"recommendationType"`
				}
				if err := mapstructure.Decode(groupData, &group); err != nil {
					responeData.Message = "Error decoding group data"
					c.JSON(http.StatusInternalServerError, responeData)
					return
				}
				typeclass.RecommendationType = group.RecommendationType

			}

			if diseaseData, ok := data["disease"]; ok {
				if err := mapstructure.Decode(diseaseData, &disease); err != nil {
					responeData.Message = "Error decoding disease data"
					c.JSON(http.StatusInternalServerError, responeData)
					return
				}

			}
		}
	}
	//乳腺专科参数验证
	//解析乳腺专科传进来的参数
	var breastParamslist []kbservice.BreastParam
	//解析传进来的数据
	for _, item := range BreastinputDataArray {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			responeData.Infcode = strconv.Itoa(http.StatusBadRequest)
			responeData.ResponseTime = GetNow()
			responeData.Message = " BreastInput data item is not a map"
			c.JSON(http.StatusBadRequest, responeData)
			continue
		}
		BreastsignOrSymptom, ok2 := itemMap["breast_sign_or_symptom"].(string)
		BreastDiseaseOrSyndrome, ok3 := itemMap["breast_disease_or_syndrome"].(string)
		BreastLocalTemperatureSituation, ok4 := itemMap["breast_local_temperature_situation"].(string)
		BreastTextureOfTheMmass, ok5 := itemMap["breast_texture_of_the_mmass"].(string)
		BreastMobilityOfTheMass, ok6 := itemMap["breast_mobility_of_the_masse"].(string)
		BreastTumorRADSClassification, ok7 := itemMap["breast_tumor_rads_classification"].(string)
		BreastTNMStagingOfPrimaryTumors, ok8 := itemMap["breast_tnm_staging_of_primary_tumors"].(string)
		TNMStagingOfRegionalLymphNodes, ok9 := itemMap["tnm_staging_of_regional_lymph_nodes"].(string)
		TNMStagingForDistantMetastasis, ok10 := itemMap["tnm_staging_for_distant_metastasis"].(string)
		LuminalTyping, ok12 := itemMap["luminal_typing"].(string)
		BreastType, ok11 := itemMap["breast_type"].(string)
		if !ok2 || !ok3 || !ok4 || !ok5 || !ok6 || !ok7 || !ok8 || !ok9 || !ok10 || !ok11 || !ok12 {
			fmt.Println("有入参不为字符串的参数")
			continue
		}
		breast_sign_or_symptom, _ := strconv.ParseInt(BreastsignOrSymptom, 10, 64)
		breast_disease_or_syndrome, _ := strconv.ParseInt(BreastDiseaseOrSyndrome, 10, 64)
		breast_local_temperature_situation, _ := strconv.ParseInt(BreastLocalTemperatureSituation, 10, 64)
		breast_texture_of_the_mmass, _ := strconv.ParseInt(BreastTextureOfTheMmass, 10, 64)
		breast_mobility_of_the_masse, _ := strconv.ParseInt(BreastMobilityOfTheMass, 10, 64)
		breast_tumor_rads_classification, _ := strconv.ParseInt(BreastTumorRADSClassification, 10, 64)
		breast_tnm_staging_of_primary_tumors, _ := strconv.ParseInt(BreastTNMStagingOfPrimaryTumors, 10, 64)
		tnm_staging_of_regional_lymph_nodes, _ := strconv.ParseInt(TNMStagingOfRegionalLymphNodes, 10, 64)
		tnm_staging_for_distant_metastasis, _ := strconv.ParseInt(TNMStagingForDistantMetastasis, 10, 64)
		luminal_typing, _ := strconv.ParseInt(LuminalTyping, 10, 64)

		breastparams := kbservice.BreastParam{
			BreastSignOrSymptom:             breast_sign_or_symptom,
			BreastDiseaseOrSyndrome:         breast_disease_or_syndrome,
			BreastLocalTemperatureSituation: breast_local_temperature_situation,
			BreastTextureOfTheMmass:         breast_texture_of_the_mmass,
			BreastMobilityOfTheMass:         breast_mobility_of_the_masse,
			BreastTumorRADSClassification:   breast_tumor_rads_classification,
			BreastTNMStagingOfPrimaryTumors: breast_tnm_staging_of_primary_tumors,
			TNMStagingOfRegionalLymphNodes:  tnm_staging_of_regional_lymph_nodes,
			TNMStagingForDistantMetastasis:  tnm_staging_for_distant_metastasis,
			LuminalTyping:                   luminal_typing,
			BreastType:                      BreastType,
		}
		breastParamslist = append(breastParamslist, breastparams)
	}

	if requestData.DType == "1" {
		if len(breastParamslist) > 0 {
			for _, d := range disease {
				temp := fmt.Sprintf(" '%s'", d.DiseaseName)
				values = append(values, temp)
			}
			inClause := fmt.Sprintf("[%s]", strings.Join(values, ","))
			ret := kbservice.BreastGetRecomendation(inClause, typeclass.RecommendationType, breastParamslist)
			if ret != "" {
				responeData.OutputData = ret
				responeData.ResponseTime = GetNow()
				responeData.Message = "success"
			}
		}
	} else {
		if len(disease) > 0 {
			for _, d := range disease {
				temp := fmt.Sprintf(" '%s'", d.DiseaseName)
				values = append(values, temp)
			}
			inClause := fmt.Sprintf("[%s]", strings.Join(values, ","))
			//乳腺专科参数
			//传递乳腺专科的参数
			var breastParams kbservice.BreastParam
			if requestData.BreastSignOrSymptom != "" {
				i, _ := strconv.ParseInt(requestData.BreastSignOrSymptom, 10, 64)
				breastParams.BreastSignOrSymptom = i
			}
			if requestData.BreastDiseaseOrSyndrome != "" {
				i, _ := strconv.ParseInt(requestData.BreastDiseaseOrSyndrome, 10, 64)
				breastParams.BreastDiseaseOrSyndrome = i
			}
			if requestData.BreastLocalTemperatureSituation != "" {
				i, _ := strconv.ParseInt(requestData.BreastSignOrSymptom, 10, 64)
				breastParams.BreastLocalTemperatureSituation = i
			}
			if requestData.BreastTextureOfTheMmass != "" {
				i, _ := strconv.ParseInt(requestData.BreastTextureOfTheMmass, 10, 64)
				breastParams.BreastTextureOfTheMmass = i
			}
			if requestData.BreastMobilityOfTheMass != "" {
				i, _ := strconv.ParseInt(requestData.BreastMobilityOfTheMass, 10, 64)
				breastParams.BreastMobilityOfTheMass = i
			}
			if requestData.BreastTumorRADSClassification != "" {
				i, _ := strconv.ParseInt(requestData.BreastTumorRADSClassification, 10, 64)
				breastParams.BreastTumorRADSClassification = i
			}
			if requestData.BreastTNMStagingOfPrimaryTumors != "" {
				i, _ := strconv.ParseInt(requestData.BreastTNMStagingOfPrimaryTumors, 10, 64)
				breastParams.BreastTNMStagingOfPrimaryTumors = i
			}
			if requestData.TNMStagingOfRegionalLymphNodes != "" {
				i, _ := strconv.ParseInt(requestData.TNMStagingOfRegionalLymphNodes, 10, 64)
				breastParams.TNMStagingOfRegionalLymphNodes = i
			}
			if requestData.TNMStagingForDistantMetastasis != "" {
				i, _ := strconv.ParseInt(requestData.TNMStagingForDistantMetastasis, 10, 64)
				breastParams.TNMStagingForDistantMetastasis = i
			}
			ret := kbservice.GetRecomendation(inClause, typeclass.RecommendationType, breastParams)
			if ret != "" {
				responeData.OutputData = ret
				responeData.ResponseTime = GetNow()
				responeData.Message = "success"
			}
		}
	}
	c.JSON(http.StatusOK, responeData)
}

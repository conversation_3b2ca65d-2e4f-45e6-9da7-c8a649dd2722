package utils

import (
	"bytes"
	"encoding/json"
	"io/ioutil"
	"kmbservice/controller/types"
	"kmbservice/pkg/logging"
	"log"
	"net/http"
	"runtime/debug"
	"time"
)

func CheckTreatServiceName(service_id string) (name string, err error) {
	defer func() {
		if err := recover(); err != nil {
			log.Printf("panic: %v\n%s", err, debug.Stack())
		}
	}()
	//url := fmt.Sprintf("%s:%s%s", setting.DataServiceSetting.Host, setting.DataServiceSetting.Port, setting.DataServiceSetting.Url)
	url := "http://**********:9891/api/v1/others/treatment_service/get"
	//url := "http://localhost:9891/api/v1/others/treatment_service/get"
	requestUid := types.GetNameBysoidResponse{
		ServiceID: service_id,
	}
	jsonToken, err := json.Marshal(requestUid)
	if err != nil {
		logging.Error("json转化失败")
		return "", err
	}
	if jsonToken == nil {
		logging.Error("请求体为空")
		return "", err
	}

	requests, err1 := http.NewRequest("POST", url, bytes.NewBuffer(jsonToken))
	if err1 != nil {
		logging.Error("创建请求失败", err)
		return "nil", err
	}
	requests.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 5 * time.Second, // 设置超时时间为5秒
	}
	response, err2 := client.Do(requests)
	if err2 != nil {
		logging.Error("发送请求失败:%v", err)
		return "nil", err
	}
	defer response.Body.Close()

	body, err := ioutil.ReadAll(response.Body)
	if err != nil {
		logging.Error("读取响应失败:%v", err)
		return "nil", err
	}
	ttt := string(body)
	var testItems Response
	if err := json.Unmarshal([]byte(ttt), &testItems); err != nil {
		logging.Error(err)
		return "nil", err
	}
	return testItems.Data, err
}

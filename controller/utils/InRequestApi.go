package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"kmbservice/controller/types"
	"kmbservice/dsl"
	"kmbservice/pkg/logging"
	"kmbservice/pkg/setting"
	"log"
	"net/http"
	"runtime/debug"
	"strconv"
	"time"
)

func RequestDataServiceApi(register_id string) ([]*dsl.TestReportDetail, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Printf("panic: %v\n%s", err, debug.Stack())
		}
	}()
	url := fmt.Sprintf("%s:%s%s", setting.DataServiceSetting.Host, setting.DataServiceSetting.Port, setting.DataServiceSetting.Url)
	//url := "http://localhost:9891/api/v1/rep/rep_lab_report_detail/get"
	requestUid := types.RepLabRequestBody{
		RegisterID: register_id,
	}
	jsonToken, err := json.Marshal(requestUid)
	if err != nil {
		logging.Error("json转化失败")
		return nil, err
	}
	if jsonToken == nil {
		logging.Error("请求体为空")
		return nil, err
	}

	requests, err1 := http.NewRequest("POST", url, bytes.NewBuffer(jsonToken))
	if err1 != nil {
		logging.Error("创建请求失败", err)
		return nil, err
	}
	requests.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 5 * time.Second, // 设置超时时间为5秒
	}
	response, err2 := client.Do(requests)
	if err2 != nil {
		logging.Error("发送请求失败:%v", err)
		return nil, err
	}
	defer response.Body.Close()

	body, err := ioutil.ReadAll(response.Body)
	if err != nil {
		logging.Error("读取响应失败:%v", err)
		return nil, err
	}
	ttt := string(body)
	var testItems []TestItem
	//var resultMap map[string]interface{}
	if err := json.Unmarshal([]byte(ttt), &testItems); err != nil {
		logging.Error(err)
		return nil, err
	}
	var list []*dsl.TestReportDetail
	for _, item := range testItems {
		dlll := Turn(item)
		list = append(list, &dlll)
	}
	return list, err
}

//type ResultList struct {
//	List []Result `json:"list"`
//}

type TestItem struct {
	DetailID       int64  `json:"detail_id"`
	ReportID       int64  `json:"report_id"`
	TestItemID     int64  `json:"test_item_id"`
	TestItemName   string `json:"test_item_name"`
	TestResult     string `json:"test_result"`
	ReferenceRange string `json:"reference_range"`
	Unit           string `json:"unit"`
	HighLowFlag    string `json:"high_low_flag"`
	DataValueID    string `json:"data_value_id"`
	UnitSoid       string `json:"unit_soid"`
}

func Turn(rsp TestItem) (oo dsl.TestReportDetail) {
	oo.DetailID = rsp.DetailID
	oo.ReportID = rsp.ReportID
	i, _ := strconv.ParseInt(rsp.DataValueID, 10, 64)
	oo.TestItemID = i
	oo.TestItemName = rsp.TestItemName
	oo.TestResult = rsp.TestResult
	oo.ReferenceRange = rsp.ReferenceRange
	oo.Unit = rsp.Unit
	oo.HighLowFlag = rsp.HighLowFlag
	return
}

package controller

import (
	"github.com/astaxie/beego/validation"
	"github.com/gin-gonic/gin"
	"kmbservice/pkg/e"
	"kmbservice/pkg/logging"
	"kmbservice/pkg/util"
	"kmbservice/repository/models"
	"log"
	"net/http"
)

type auth struct {
	Username string `json:"username"`
	Password string `json:"password"`
	Userid   int8   `json:"userid"`
}

func GetAuth(c *gin.Context) {
	var json auth
	if err := c.ShouldBindJSON(&json); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    e.INVALID_PARAMS,
			"message": e.GetMsg(e.INVALID_PARAMS),
			"data":    nil,
		})
		return
	}
	userid := json.Userid
	userpasswd := json.Password
	valid := validation.Validation{}
	a := auth{Userid: userid, Password: userpasswd}
	ok, _ := valid.Valid(&a)
	data := make(map[string]interface{})
	code := e.INVALID_PARAMS
	if ok {
		user, err := models.LoginIn(userid, userpasswd)
		if err != nil {
			code = e.ERROR_EXIST_TAG

		} else {
			token, error := util.GenerateToken(user.UserId, userpasswd)
			if error != nil {
				code = e.ERROR_AUTH_TOKEN
				logging.Error(error)
			} else {
				data["token"] = token
				code = e.SUCCESS
			}
		}
	} else {
		for _, err := range valid.Errors {
			log.Println(err.Key, err.Message)
		}
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    code,
		"message": e.GetMsg(code),
		"data":    data,
	})
}

package controller

import (
	"github.com/gin-gonic/gin"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"time"
)

func GetCmeInfo(c *gin.Context) {
	var (
		reqobj GetDrugInfoRequest
		rspobj CMedicineResponse
	)
	err := c.ShouldBind(&reqobj)
	if err != nil {
		response.Response2(c, 200, "入参错误", err)
		return
	}
	cmemodle := models.DmpMdmCMedicine{}
	cmeItem := models.DmpMdmCMedicineItem{}

	result, _ := cmemodle.GetByName(reqobj.Name)
	if result.TCMID == 0 {
		response.Response2(c, 200, "无数据", nil)
		return
	}
	rspobj.AdditionAndSubtraction = result.AdditionAndSubtraction
	rspobj.ByStr = result.ByStr
	rspobj.ClinicalApplication = result.ClinicalApplication
	rspobj.Status = result.Status
	rspobj.Name = result.Name
	rspobj.DosageForm = result.DosageForm
	rspobj.Effect = result.Effect
	rspobj.Formulas = result.Formulas
	rspobj.CreateUser = result.CreateUser
	rspobj.TCMID = result.TCMID
	rspobj.DecoctingMethod = result.DecoctingMethod
	rspobj.EfficacyClassification = result.EfficacyClassification
	rspobj.GroupOID = result.GroupOID

	cmelist, _ := cmeItem.GetDrugSpecificationItems(result.TCMID)
	if len(cmelist) == 0 {
		response.Response2(c, 200, "无数据", nil)
		return
	}
	for _, item := range cmelist {
		var vv DmpMdmCMedicineItem
		vv.CName = item.CName
		vv.Unit = item.Unit
		vv.Compatibility = item.Compatibility
		vv.ItemID = item.ItemID
		vv.Qly = item.Qly
		vv.DescofDecoction = item.DescofDecoction
		vv.Description = item.Description
		vv.ItemOID = item.ItemOID
		vv.YpCode = item.YpCode
		rspobj.ItemList = append(rspobj.ItemList, vv)
	}
	response.Response2(c, 200, "成功", rspobj)
	return
}

type CMedicineResponse struct {
	Name                   string                `json:"name"`
	OriginOfPrescription   string                `json:"origin_of_prescription"`
	Formulas               string                `json:"formulas"`
	OriginalPrescription   string                `json:"original_prescription"`
	DosageForm             string                `json:"dosage_form"`
	PreparationMethod      string                `json:"preparation_method"`
	Route                  string                `json:"route"`
	Mechanism              string                `json:"mechanism"`
	Indications            string                `json:"indications"`
	EfficacyClassification string                `json:"efficacy_classification"`
	Effect                 string                `json:"effect"`
	Dialectical            string                `json:"dialectical"`
	AdditionAndSubtraction string                `json:"addition_and_subtraction"`
	SquareSolution         string                `json:"square_solution"`
	ClinicalApplication    string                `json:"clinical_application"`
	MedicationPrecautions  string                `json:"medication_precautions"`
	DecoctingMethod        string                `json:"decocting_method"`
	GroupOID               int64                 `json:"group_oid"`
	TCMID                  int64                 `json:"tcm_id"`
	CreateUser             int64                 `json:"create_user"`
	Status                 string                `json:"status"`
	PreType                string                `json:"pre_type"`
	ByStr                  string                `json:"by_str"`
	CreatedAt              time.Time             `json:"created_at"`
	FunClass1              string                `json:"fun_class1"`
	FunClass2              string                `json:"fun_class2"`
	ItemList               []DmpMdmCMedicineItem `json:"item_list"`
}

type DmpMdmCMedicineItem struct {
	ItemID          int64  `json:"item_id"`
	ItemOID         int64  `json:"item_oid"`
	TCMID           int64  `json:"tcmid"`
	CName           string `json:"c_name"`
	Qly             string `json:"qly"`
	Unit            string `json:"unit"`
	Description     string `json:"description"`
	DescofDecoction string `json:"descofDecoction"`
	Compatibility   string `json:"compatibility"`
	Status          string `json:"status"`
	YpCode          string `json:"ypCode"`
}

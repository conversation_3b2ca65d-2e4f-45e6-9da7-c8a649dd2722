package controller

import (
	"github.com/gin-gonic/gin"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
)

func GetDrugInfo(c *gin.Context) {
	var (
		reqobj GetDrugInfoRequest
		rspobj DrugSpecification
	)
	err := c.ShouldBind(&reqobj)
	if err != nil {
		response.Response2(c, 200, "入参错误", err)
		return
	}
	drugmodle := models.DrugSpecification{}
	Item := models.DrugSpecificationItem{}
	result, _ := drugmodle.GetByName(reqobj.Name)
	if result.SpeciID == 0 {
		response.Response2(c, 200, "无数据", err)
		return
	}
	rspobj.SpeciID = result.SpeciID
	rspobj.Name = result.Name
	rspobj.Content = result.Content
	rspobj.Link = result.Link
	rlist, _ := Item.GetDrugSpecificationItems(rspobj.SpeciID)
	if len(rlist) == 0 {
		response.Response2(c, 200, "无数据", err)
		return
	}
	for _, item := range rlist {
		var vv DrugSpecificationItem
		vv.Name = item.Name
		vv.Jsonb = item.JSONB
		vv.Type = item.Type
		vv.ItemID = item.ItemID
		vv.Specification = item.Specification
		vv.SpeciID = item.SpeciID
		rspobj.Items = append(rspobj.Items, vv)
	}
	response.Response2(c, 200, "成功", rspobj)
	return
}

type DrugSpecification struct {
	SpeciID int64                   `json:"speci_id"`
	Name    string                  `json:"name"`
	Content string                  `json:"content"`
	Link    string                  `json:"link"`
	Items   []DrugSpecificationItem `json:"items"`
}

type DrugSpecificationItem struct {
	ItemID        int64  `json:"item_id"`
	Type          int16  `json:"type"`
	Name          string `json:"name"`
	Specification string `json:"specification"`
	SpeciID       int64  `json:"speci_id"`
	Jsonb         []byte `json:"jsonb"`
}

type GetDrugInfoRequest struct {
	Name string `json:"name"`
}

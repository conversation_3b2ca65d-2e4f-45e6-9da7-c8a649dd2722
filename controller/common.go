package controller

import (
	"kmbservice/dsl"
	"kmbservice/repository/models"
	"strconv"
)

var disease_diagnosis_relationship models.DiseaseDiagnosisRelationship

func GetDisease(requestData *RequestData) []*dsl.Disease {
	var diseaselist []*dsl.Disease
	for _, inputData := range requestData.InputData {
		if len(inputData.Diseases) > 0 {
			for _, d := range inputData.Diseases {
				tmep := d
				diseaselist = append(diseaselist, &tmep)
			}
		}
	}
	return diseaselist
}

// 通过诊断查病种
func GetDiseaseByDiagnosis(requestData []*dsl.Disease) []string {
	var diagnosisCodelist []string
	for _, inputData := range requestData {
		for _, d := range inputData.Diagnosis {
			diagnosisCodelist = append(diagnosisCodelist, d)
		}
	}
	result, _ := disease_diagnosis_relationship.GetDiseasesByDiagnosisNos(diagnosisCodelist)
	return result
}

func GetImageReport(requestData *RequestData) []*dsl.ImageReport {
	var imageReportlist []*dsl.ImageReport
	for _, inputData := range requestData.InputData {
		if len(inputData.ImageReports) > 0 {
			for _, i := range inputData.ImageReports {
				tmep := i
				imageReportlist = append(imageReportlist, &tmep)
			}
		}
	}
	return imageReportlist
}

//func GetOrder(requestData *RequestData) *dsl.Order {
//	var order *dsl.Order
//	for _, inputData := range requestData.InputData {
//		if inputData.Order != nil {
//			order = inputData.Order
//		}
//	}
//	return order
//}

func GetOrder(requestData *RequestData) []dsl.Order {
	var orderList []dsl.Order
	for _, inputData := range requestData.InputData {
		if len(inputData.Order) > 0 {
			for _, i := range inputData.Order {
				orderList = append(orderList, i)
			}
		}
	}
	return orderList
}

func GetTestReportDetail(requestData *RequestData) []*dsl.TestReportDetail {
	var testReportDetaillist []*dsl.TestReportDetail
	for _, inputData := range requestData.InputData {
		if len(inputData.TestReportDetail) > 0 {
			for _, t := range inputData.TestReportDetail {
				tmep := convertReqToTest(t)
				testReportDetaillist = append(testReportDetaillist, &tmep)
			}
		}
	}
	return testReportDetaillist
}

func convertReqToTest(req dsl.ReqTestReportDetail) dsl.TestReportDetail {
	detailID, _ := strconv.ParseInt(req.DetailID, 10, 64)
	reportID, _ := strconv.ParseInt(req.ReportID, 10, 64)
	testItemID, _ := strconv.ParseInt(req.TestItemID, 10, 64)
	return dsl.TestReportDetail{
		DetailID:       detailID,
		ReportID:       reportID,
		TestItemID:     testItemID,
		TestItemName:   req.TestItemName,
		TestResult:     req.TestResult,
		ReferenceRange: req.ReferenceRange,
		Unit:           req.Unit,
		HighLowFlag:    req.HighLowFlag,
		UnitSoid:       req.UnitSoid,
		Msg:            req.Msg,
	}
}

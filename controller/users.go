package controller

import (
	"github.com/astaxie/beego/validation"
	"github.com/gin-gonic/gin"
	"kmbservice/pkg/e"
	"kmbservice/repository/models"
	"net/http"
)

func AddUser(c *gin.Context) {
	var user models.AdminUser
	code := e.INVALID_PARAMS
	if err := c.ShouldBindJSON(&user); err != nil {
		code = e.INVALID_PARAMS
	}
	valid := validation.Validation{}
	valid.Required(user.Username, "username").Message("用户名称不能为空！")
	valid.Required(user.Password, "password").Message("密码不能为空！")
	if !valid.HasErrors() {
		isok, err := models.ExistUserByName(user.Username)
		if err != nil {
			code = e.ERROR
		}
		if isok {
			code = e.ERROR_EXIST_TAG
		} else {
			error := models.AddUser(user)
			if error != nil {
				code = e.ERROR
			} else {
				code = e.SUCCESS
			}
		}
	}
	c.JSON(http.StatusOK, gin.H{
		"code": code,
		"msg":  e.Get<PERSON>g(code),
		"data": make(map[string]string),
	})
}

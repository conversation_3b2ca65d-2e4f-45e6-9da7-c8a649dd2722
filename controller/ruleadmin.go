package controller

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"kmbservice/repository/models"
	"net/http"
	"strconv"
)

func CreateCategoryHandler(c *gin.Context) {
	var requestBody struct {
		Name        string `json:"name"`
		Description string `json:"description"`
	}
	// 解析 JSON 请求体
	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无法解析请求体"})
		return
	}
	// 调用函数保存数据
	id, err := models.CreateCategory(requestBody.Name, requestBody.Description)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "无法创建类别"})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "类别已创建",
		"data":    gin.H{"id": id},
	})
}
func GetCategoryListHandler(c *gin.Context) {
	var categories []models.RuleCategory
	categories = models.GetCategoryList()
	if categories == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "无法获取分类列表"})
		return
	}

	// 返回分类列表给客户端
	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "success",
		"data":    categories,
	})
}
func GetCategoryHandler(c *gin.Context) {
	var category models.RuleCategory
	idStr := c.Query("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的分类 ID"})
		return
	}
	result := models.GetCategory(id)
	if result == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "未找到分类"})
		return
	}
	category = *result
	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "success",
		"data":    category,
	})
}
func UpdateCategoryHandler(c *gin.Context) {
	var requestBody struct {
		Id          string `json:"id"`
		Name        string `json:"name"`
		Description string `json:"description"`
	}
	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无法解析请求体"})
		return
	}
	id, err := strconv.ParseInt(requestBody.Id, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的分类 ID"})
		return
	}
	isok := models.UpdateCategory(id, requestBody.Name, requestBody.Description)
	if !isok {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "更新失败.",
			"data":    "",
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code":    http.StatusOK,
			"message": "更新成功。",
			"data":    "",
		})
	}
}
func DeleteCategoryHandler(c *gin.Context) {
	idStr := c.Query("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的分类 ID"})
		return
	}
	isok := models.DeleteCategory(id)
	if !isok {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "删除失败",
			"data":    "",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "success",
		"data":    "",
	})
}
func CreateTreeHandler(c *gin.Context) {
	var requestBody struct {
		ParentId     int64  `json:"parent_id"`
		RuleName     string `json:"rule_name"`
		IsActive     string `json:"is_active"`
		DispalyOrder int    `json:"display_order"`
	}
	// 解析 JSON 请求体
	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "无法解析请求体",
			"data":    ""})
		return
	}
	fmt.Printf("Received Parameters: %+v\n", requestBody)
	newID, err := models.AddTree(requestBody.ParentId, requestBody.RuleName, requestBody.DispalyOrder)
	if !err {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "增加失败",
			"data":    nil,
		})
		return
	}
	// 返回新增的 ID
	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "增加成功",
		"data":    newID,
	})
}
func GetTreeListHandler(c *gin.Context) {
	result := models.GetAllRree()
	if result == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "查询失败",
			"data":    "",
		})
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "success",
		"data":    result,
	})
}

func DeleteTreeHandler(c *gin.Context) {
	idStr := c.Query("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的分类 ID"})
		return
	}
	isok := models.DeleteTree(id)
	if !isok {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "删除规则树失败！",
			"data":    "",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "删除规则树成功！",
		"data":    "",
	})
}
func UpdateTreeHandler(c *gin.Context) {
	var requestBody struct {
		Id           int64  `json:"id"`
		ParentId     int64  `json:"parent_id"`
		RuleName     string `json:"rule_name"`
		IsActive     bool   `json:"is_active"`
		DispalyOrder int    `json:"dispaly_order"`
	}
	// 解析 JSON 请求体
	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "无法解析请求体",
			"data":    ""})
		return
	}
	isok := models.UpdateTree(requestBody.Id, requestBody.ParentId, requestBody.RuleName, requestBody.IsActive, requestBody.DispalyOrder)
	if !isok {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "更新失败.",
			"data":    "",
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code":    http.StatusOK,
			"message": "更新成功。",
			"data":    "",
		})
	}
}
func CreateContentHandler(c *gin.Context) {
	var requestBody struct {
		CategoryTreeID  int64  `json:"category_tree_id"`
		RuleName        string `json:"rule_name"`
		RuleDescription string `json:"rule_description"`
		UsageScenario   int    `json:"usage_scenario"`
		Status          int64  `json:"is_active"`
		RuleContent     string `json:"rule_content"`
	}
	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "解析入参数失败！",
			"data":    "",
		})
		return
	}
	id, err := models.AddContent(requestBody.CategoryTreeID, requestBody.RuleName, requestBody.RuleDescription, requestBody.Status, requestBody.RuleContent)
	if !err {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "增加规则失败",
			"data":    "",
		})
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "增加规则成功！",
		"data":    id,
	})
}
func GetContentListHandler(c *gin.Context) {
	var contents []models.RuleContent
	contents = models.GetAllContent(0, 1, nil)
	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "success",
		"data":    contents,
	})
}
func GetContentById() {

}
func DeleteContentHandler(c *gin.Context) {
	idStr := c.Query("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的分类 ID"})
		return
	}
	isok := models.DeleteContent(id)
	if !isok {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "删除失败",
			"data":    isok,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "删除成功！",
		"data":    id,
	})
}
func UpdateContentHandler(c *gin.Context) {
	var requestBody struct {
		Id              int64  `json:"id"`
		CategoryTreeID  int64  `json:"category_tree_id"`
		RuleName        string `json:"rule_name"`
		RuleDescription string `json:"rule_description"`
		UsageScenario   int    `json:"usage_scenario"`
		Status          int64  `json:"is_active"`
		RuleContent     string `json:"rule_content"`
	}
	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "解析入参数失败！",
			"data":    requestBody,
		})
		return
	}
	isOK := models.UpdateContent(requestBody.Id, requestBody.CategoryTreeID, requestBody.RuleName, requestBody.RuleDescription, requestBody.Status, requestBody.RuleContent)
	if !isOK {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "更新失败",
			"data":    "",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "更新成功！",
		"data":    " ",
	})
}

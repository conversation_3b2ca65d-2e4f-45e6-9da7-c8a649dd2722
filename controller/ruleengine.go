package controller

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"kmbservice/controller/utils"
	"kmbservice/dsl"
	"kmbservice/internals/rulengines/services/ruleservice"
	"kmbservice/pkg/logging"
	"kmbservice/repository/models"
	"net/http"
	"strconv"
	"time"
)

type Rule struct {
	RuleID   string `json:"rule_id"`
	GroupID  string `json:"group_id"`
	RuleName string `json:"rule_name"`
	RuleType string `json:"rule_type"`
}

type InputData struct {
	Rule             *Rule                     `json:"rule,omitempty"`
	Patient          *dsl.Patient              `json:"patient,omitempty"`
	Diseases         []dsl.Disease             `json:"diseases,omitempty"`
	Medicines        []dsl.Medicine            `json:"medicines,omitempty"`
	ImageReports     []dsl.ImageReport         `json:"imageReports,omitempty"`
	Order            []dsl.Order               `json:"order,omitempty"`
	TestReportDetail []dsl.ReqTestReportDetail `json:"testReportDetail,omitempty"`
	TestItemId       int64                     `json:"test_item_id"` //检查项目编号
}

type RequestData struct {
	Tranno            string       `json:"tranno"`
	Msgid             string       `json:"msgid"`
	Hosno             string       `json:"hosno"`
	Sendapp           string       `json:"sendapp"`
	RecApp            string       `json:"rec_app"`
	Cainfo            string       `json:"cainfo"`
	Opter             string       `json:"opter"`
	TranTime          time.Time    `json:"tran_time"`
	Infver            string       `json:"infver"`
	EncType           string       `json:"enc_type"`
	InputData         []*InputData `json:"input_data"`
	RegisterID        string       `json:"register_id"`
	SystemCodeMapping bool         `json:"system_code_mapping"`
	CorpID            string       `json:"corp_id"`
}

// 判断 Diseases 是否有值
func hasDiseases(diseases []dsl.Disease) bool {
	return len(diseases) > 0
}

// 判断 Medicines 是否有值
func hasMedicines(medicines []dsl.Medicine) bool {
	return len(medicines) > 0
}

func RuleExec(c *gin.Context) {
	//defer func() {
	//	if r := recover(); r != nil {
	//		logging.Error("Recovered from panic:", r)
	//		response.Response(c, 500, "入参解释错误：", r)
	//	}
	//}()
	var requestData RequestData
	log := &models.RuleLog{
		ReqIn: " ",
	}
	//响应格式
	resdata := Initres()
	if err := c.ShouldBindJSON(&requestData); err != nil {
		resdata.Message = err.Error()
		resdata.RecTime = GetNow()
		c.JSON(http.StatusBadRequest, resdata)
		return
	}

	requestDataJSON, err := json.Marshal(requestData)
	if err != nil {
	}
	log.ReqIn = string(requestDataJSON)
	log.App = requestData.Sendapp
	log.User = requestData.Opter
	rule, patient := GetMainPart(&requestData)
	rule_num, err := strconv.ParseInt(rule.RuleID, 10, 64)
	if err != nil {
		fmt.Println("RuleID字符转换数值类型错误:", err)
		resdata.Message = err.Error()
		resdata.RecTime = GetNow()
		c.JSON(http.StatusBadRequest, resdata)
		return
	}

	//获取患者用药信息
	drugs := GetMedicines(&requestData)
	for _, drug := range drugs {
		fmt.Printf("Drug: %+v\n", *drug)
	}
	//获取医嘱信息
	//order := GetOrder(&requestData)

	//患者年龄解析
	if patient != nil {
		patient.CalculateAge()
	}

	//获取检验报告
	var testreportrsp []*dsl.TestReportDetail
	testreportrsp, _ = utils.RequestDataServiceApi(requestData.RegisterID)
	if len(testreportrsp) == 0 {
		testreportrsp = GetTestReportDetail(&requestData)
	}

	testreportrsp = []*dsl.TestReportDetail{
		{
			HighLowFlag:    "偏高",
			TestResult:     "20",
			Unit:           "1",
			DetailID:       1796074371679916048,
			ReferenceRange: "0-40",
			TestItemID:     1796078696373392480,
			TestItemName:   "谷丙转氨酶",
			ReportID:       1796073974793900033,
		},
		{
			HighLowFlag:    "正常",
			TestResult:     "30",
			Unit:           "U/L",
			DetailID:       1796074371679916049,
			ReferenceRange: "0-40",
			TestItemID:     1796078696373392481,
			TestItemName:   "谷草转氨酶",
			ReportID:       1796073974793900034,
		},
		{
			HighLowFlag:    "偏低",
			TestResult:     "20",
			Unit:           "g/L",
			DetailID:       1796074371679916050,
			ReferenceRange: "30-50",
			TestItemID:     1796078696373392482,
			TestItemName:   "白蛋白",
			ReportID:       1796073974793900035,
		},
	}
	//编码信息转换
	var orderList []*dsl.Order
	//if requestData.SystemCodeMapping == true {
	//	//患者编码转换
	//	if patient != nil {
	//		patient, err = tools.PatientSystemCode(patient, requestData.CorpID)
	//	}
	//	//医嘱编码转换
	//	if order != nil {
	//		for _, o := range order {
	//			singleorder, _ := tools.OrderSystemCode(&o, requestData.CorpID)
	//			orderList = append(orderList, singleorder)
	//		}
	//	}
	//}

	//分类规则执行
	if rule != nil {
		switch rule.RuleType {
		//患者与药物的关系
		case "1":
			{
				err = ruleservice.ExecPatientOrMedicine(rule_num, patient, drugs)
				if err != nil {
					resdata.Message = err.Error()
					resdata.RecTime = GetNow()
					c.JSON(http.StatusBadRequest, resdata)
					return
				}
				resdata.OutputData = patient.Msg
				resdata.ResponseTime = GetNow()
			}
			//药物与检验报告的关系
		case "2":
			{
				err = ruleservice.ExectestReportsWithMedicine(rule_num, testreportrsp, drugs, patient)
				if err != nil {
					resdata.Message = err.Error()
					resdata.RecTime = GetNow()
					c.JSON(http.StatusBadRequest, resdata)
					return
				}
				resdata.OutputData = patient
				resdata.ResponseTime = GetNow()
			}
			//病人与疾病的关系
		case "3":
			{
				disease := GetDisease(&requestData)
				var codelist []string
				if len(disease) > 0 {
					for _, d := range disease {
						if len(d.Diagnosis) > 0 {
							codelist = GetDiseaseByDiagnosis(disease)
						}
					}
				}
				err = ruleservice.ExecPatientWithDisease(rule_num, patient, disease, codelist)
				if err != nil {
					resdata.Message = err.Error()
					resdata.RecTime = GetNow()
					c.JSON(http.StatusBadRequest, resdata)
					return
				}
				resdata.OutputData = patient
				resdata.ResponseTime = GetNow()
			}
			//药品与检查的关系
		case "4":
			{
				imageReport := GetImageReport(&requestData)
				err = ruleservice.ExecMedicineWithImageReport(rule_num, patient, drugs, imageReport)
				if err != nil {
					resdata.Message = err.Error()
					resdata.RecTime = GetNow()
					c.JSON(http.StatusBadRequest, resdata)
					return
				}
				resdata.OutputData = patient
				resdata.ResponseTime = GetNow()
			}
			//药品与医嘱之间的关系
		//case "5":
		//	{
		//		err = ruleservice.ExecMedicineWithOrder(rule_num, patient, drugs, order)
		//		if err != nil {
		//			resdata.Message = err.Error()
		//			resdata.RecTime = GetNow()
		//			c.JSON(http.StatusBadRequest, resdata)
		//			return
		//		}
		//		resdata.OutputData = patient
		//		resdata.ResponseTime = GetNow()
		//	}
		case "5":
			{
				for _, o := range orderList {
					err = ruleservice.ExecMedicineWithOrder(rule_num, patient, drugs, o)
					if err != nil {
						resdata.Message = err.Error()
						resdata.RecTime = GetNow()
						c.JSON(http.StatusBadRequest, resdata)
						return
					}
					if patient.Msg != nil {
						resdata.OutputData = patient.Msg
						resdata.ResponseTime = GetNow()
					}
				}
			}
		//测试
		case "test":
			{
				err = ruleservice.ExecJusttest(rule_num, patient, drugs, testreportrsp)
				if err != nil {
					resdata.Message = err.Error()
					resdata.RecTime = GetNow()
					c.JSON(http.StatusOK, resdata)
					return
				}
				resdata.OutputData = patient
				resdata.ResponseTime = GetNow()
			}
		default:

		}

	}
	reqDataJSON, err := json.Marshal(resdata)
	log.ResOut = string(reqDataJSON)
	models.CreateRuleLog(log)
	c.JSON(http.StatusOK, resdata)
}

func GetMainPart(requestData *RequestData) (*Rule, *dsl.Patient) {
	var rule *Rule
	var patient *dsl.Patient
	for _, inputData := range requestData.InputData {
		if inputData.Rule != nil {
			rule = inputData.Rule
		}
		if inputData.Patient != nil {
			patient = inputData.Patient
		}
	}
	return rule, patient
}

func GetPatient(requestData *RequestData) *dsl.Patient {
	var patient *dsl.Patient
	for _, inputData := range requestData.InputData {
		if inputData.Patient != nil {
			patient = inputData.Patient
		}
	}
	return patient
}

func GetMedicines(requestData *RequestData) []*dsl.Medicine {
	var medicines []*dsl.Medicine
	for _, inputData := range requestData.InputData {
		if len(inputData.Medicines) > 0 {
			for _, med := range inputData.Medicines {
				// 将每个 Medicine 转换为 *dsl.Medicine 类型并添加到 medicines 切片中
				medCopy := med
				medicines = append(medicines, &medCopy)
			}
		}
	}
	return medicines
}
func CheckRules(c *gin.Context) {
	var requestData RequestData
	resdata := Initres()
	if err := c.ShouldBindJSON(&requestData); err != nil {
		resdata.Message = "解析入口参数失败！"
		resdata.RecTime = GetNow()
		c.JSON(http.StatusBadRequest, resdata)
		return
	}
	rule, patient, diseases, medicines := parseData(&requestData)
	logging.Info(fmt.Sprintf("rule: %+v\n", rule))
	logging.Info(fmt.Sprintf("patient: %+v\n", patient))
	logging.Info(fmt.Sprintf("diseases: %+v\n", diseases))
	logging.Info(fmt.Sprintf("medicines: %+v\n", medicines))
	rule_num, err := strconv.ParseInt(rule.RuleID, 10, 64)
	if err != nil {
		fmt.Println("转换错误:", err)
		resdata.Message = err.Error()
		resdata.RecTime = GetNow()
		c.JSON(http.StatusBadRequest, resdata)
		return
	}
	patient.CalculateAge()
	patient1 := &dsl.PatientRx{
		Age:           patient.Age,
		Duration:      8,
		HbA1c:         9.2,
		MaritalStatus: patient.MaritalStatus,
		Gender:        patient.PatientGender,
		EGFR:          45.0,
	}
	ruleservice.EvaluateDiabetesRx(patient1, rule_num)
	resdata.OutputData = patient1
	resdata.ResponseTime = GetNow()
	c.JSON(http.StatusOK, resdata)
}

/*
1:ruleEnums
2:patient
3:disease
4:medicine
*/
func parseData(requestData *RequestData) (*Rule, *dsl.Patient, []dsl.Disease, []dsl.Medicine) {
	var rule *Rule
	var patient *dsl.Patient
	var diseases []dsl.Disease
	var medicines []dsl.Medicine

	for _, inputData := range requestData.InputData {
		if inputData.Rule != nil {
			rule = inputData.Rule
		}
		if inputData.Patient != nil {
			patient = inputData.Patient
		}
		if len(inputData.Diseases) > 0 {
			diseases = append(diseases, inputData.Diseases...)
		}
		if len(inputData.Medicines) > 0 {
			medicines = append(medicines, inputData.Medicines...)
		}
	}

	return rule, patient, diseases, medicines
}
func EvaluateHandler(c *gin.Context) {
	// 获取请求参数
	expression := c.Query("expression")

	// 执行 Gengine 评估逻辑
	result, err := EvaluateExpression(expression)
	if err != nil {
		// 处理错误
		c.JSON(500, gin.H{
			"error": err.Error(),
		})
		return
	}

	// 返回评估结果
	c.JSON(200, gin.H{
		"result": result,
	})
}

// EvaluateExpression 执行表达式评估
func EvaluateExpression(expression string) (string, error) {
	// 这里执行 Gengine 的评估逻辑，这里只是一个示例
	//return gstr.Reverse(expression), nil
	result, err := EvaluateExpression(expression)
	return result, err
}

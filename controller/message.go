package controller

import (
	"fmt"
	"github.com/google/uuid"
	"net/http"
	"time"
)

type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// 请求数据结构体
type ReqData struct {
	Tranno                          string      `json:"tranno"`                               // 交易编号
	Msgid                           string      `json:"msgid"`                                // 发送报文
	Hosno                           string      `json:"hosno"`                                // 结构代码
	SendApp                         string      `json:"sendapp"`                              // 发送系统
	RecApp                          string      `json:"rec_app"`                              // 接收系统
	Cainfo                          string      `json:"cainfo"`                               // 签名信息
	Opter                           string      `json:"opter"`                                // 操作员
	TranTime                        string      `json:"tran_time"`                            // 交易时间
	Infver                          string      `json:"infver"`                               // 版本
	EncType                         string      `json:"enc_type"`                             // 加密方式
	InputData                       interface{} `json:"input_data"`                           // 交易数据
	BreastInputData                 interface{} `json:"breast_input_data"`                    //乳腺专科交易数据
	BreastSignOrSymptom             string      `json:"breast_sign_or_symptom"`               //乳腺症状
	BreastDiseaseOrSyndrome         string      `json:"breast_disease_or_syndrome"`           //病种
	BreastLocalTemperatureSituation string      `json:"breast_local_temperature_situation"`   //温度趋势
	BreastTextureOfTheMmass         string      `json:"breast_texture_of_the_mmass"`          //肿块质地
	BreastMobilityOfTheMass         string      `json:"breast_mobility_of_the_masse"`         //移动度
	BreastTumorRADSClassification   string      `json:"breast_tumor_rads_classification"`     //RADS分级
	BreastTNMStagingOfPrimaryTumors string      `json:"breast_tnm_staging_of_primary_tumors"` //T
	TNMStagingOfRegionalLymphNodes  string      `json:"tnm_staging_of_regional_lymph_nodes"`  //N
	TNMStagingForDistantMetastasis  string      `json:"tnm_staging_for_distant_metastasis"`   //M
	LuminalTyping                   string      `json:"luminal_typing"`                       //
	BreastType                      string      `json:"breast_type"`                          //乳腺专科（1：查病种；2：根据症状疾病推荐病种；3：根据病种推荐量表；4：根据病种推荐西药；5：根据病种推荐实验室检查）
	DType                           string      `json:"d_type"`                               //病的类型
}

// 响应数据结构体
type ResData struct {
	Infcode      string      `json:"infcode"`       // 交易状态码
	TranRetno    string      `json:"tran_retno"`    // 接收报文码
	RecTime      string      `json:"rec_time"`      // 接收时间
	ResponseTime string      `json:"response_time"` // 响应时间
	Message      string      `json:"message"`       // 消息
	Signtype     string      `json:"signtype"`      // 签名类型
	Cainfo       string      `json:"cainfo"`        // 数字签名信息
	EncType      string      `json:"enc_type"`      // 加密类型
	OutputData   interface{} `json:"output_data"`   // 输出数据
}

func GetNow() string {
	ret := time.Now().Format("2006-01-02 15:04:05.000")
	return ret
}
func GenerateTransactionCode() (string, error) {
	// 生成一个 UUID
	id, err := uuid.NewUUID()
	if err != nil {
		return "", err
	}

	// 将 UUID 转换成字符串
	transactionCode := id.String()

	return transactionCode, nil
}

func Initres() ResData {
	transactionCode, err := GenerateTransactionCode()
	if err != nil {
		fmt.Println("Error generating transaction code:", err)
		return ResData{}
	}
	responeData := ResData{
		Infcode:      http.StatusText(http.StatusOK),
		TranRetno:    transactionCode,
		RecTime:      GetNow(),
		ResponseTime: "",
		Message:      "",
		Signtype:     "",
		Cainfo:       "",
		EncType:      "",
		OutputData:   "{}",
	}
	return responeData
}

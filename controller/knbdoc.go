package controller

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"kmbservice/commons/response"
	"kmbservice/repository/models"
	"sort"
)

func KnbDoc(c *gin.Context) {
	var (
		reqobj GetDrugInfoRequest
		rspobj DasKnbDocTreeResponse
	)
	err := c.ShouldBind(&reqobj)
	if err != nil {
		response.Response(c, 200, "请求参数有误", err)
		return
	}
	tree := models.DasKnbDocTree{}
	treecontent := models.DasKnbDocContent{}
	result, _ := tree.GetBydescriptionToFind(reqobj.Name)
	if result.SnowflakeID == 0 {
		response.Response(c, 200, "无疾病数据", nil)
		return
	}
	rspobj.Contents = result.Contents
	rspobj.TypeName = result.TypeName
	rspobj.Description = result.Description
	parent_id := result.SnowflakeID
	//查询这条语句的所有子节点
	sonlist, err := tree.GetDrugSpecificationItems(parent_id)
	if err != nil {
		response.Response(c, 200, "无子节点", nil)
		return
	}
	if len(sonlist) == 0 {
		response.Response(c, 200, "无数据", nil)
		return
	}
	//按照type_num排序
	sort.Slice(sonlist, ByTypeNum(sonlist))
	for _, son := range sonlist {
		var nreson DasKnbDocTreeResponseContent
		nreson.Contents = son.Contents
		nreson.Name = son.TypeName
		contentraw, err := treecontent.GetByDocTeeToFind(son.SnowflakeID)
		if err != nil {
			response.Response(c, 200, "无数据", err)
			return
		}
		nreson.ID = fmt.Sprintf("%d", contentraw.DocTreeID)
		nreson.DocContent = contentraw.DocContent
		rspobj.List = append(rspobj.List, nreson)
	}
	response.Response2(c, 200, "病种知识获取成功", rspobj)
	return
}

type DasKnbDocTreeResponse struct {
	//SnowflakeID int64                          `json:"snowflake_id"`
	//ParentID    int64                          `json:"parent_id"`
	//Level       int                            `json:"level"`
	Description string `json:"description"`
	//Status      string                         `json:"status"`
	//TypeID      int64                          `json:"type_id"`
	Contents string `json:"contents"`
	//TypeNum     string                         `json:"type_num"`
	TypeName string `json:"type_name"`
	//Class1      int                            `json:"class1"`
	//Pycode      string                         `json:"pycode"`
	//CreatedBy   int                            `json:"created_by"`
	//CreatedAt   time.Time                      `json:"created_at"`
	//UpdatedBy   int                            `json:"updated_by"`
	//UpdatedAt   time.Time                      `json:"updated_at"`
	List []DasKnbDocTreeResponseContent `json:"list"`
}

// 内容部分的结构
type DasKnbDocTreeResponseContent struct {
	ID         string `json:"id"` //id
	Name       string `json:"name"`
	Contents   string `json:"contents"`
	DocContent string `json:"docContent""`
}

// 按照type_num排序
func ByTypeNum(sonlist []models.DasKnbDocTree) func(i, j int) bool {
	return func(i, j int) bool {
		return sonlist[i].TypeNum < sonlist[j].TypeNum
	}
}

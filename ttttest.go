package main

import (
	"fmt"
	lua "github.com/yuin/gopher-lua"
)

func main() {
	L := lua.NewState()
	defer L.Close()
	// go
	err := L.DoFile("main.lua")
	if err != nil {
		fmt.Print(err.Error())
		return
	}
	err = L.CallByParam(lua.P{
		Fn:      L.GetGlobal("add"),
		NRet:    1,
		Protect: true,
	}, lua.LNumber(6), lua.LNumber(2))
	if err != nil {
		fmt.Print(err.Error())
		return
	}
	ret := L.Get(-1)
	// 如果是2个返回值， NRet改为2
	// 	ret2 := L.Get(2)
	//  L.Pop(2)
	L.Pop(1)
	res, ok := ret.(lua.LNumber)
	if ok {
		fmt.Println(res)
	}
}

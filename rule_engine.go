package main

import (
	"github.com/gin-gonic/gin"
	"kmbservice/controller"
	"kmbservice/pkg/logging"
	"kmbservice/pkg/setting"
	"kmbservice/repository/models"
	"os"
)

func main() {
	setting.Setup("conf/app.ini")
	logging.Setup()
	models.Setup()
	router := gin.Default()
	router.Use(gin.Recovery())
	router.Use(gin.Logger())
	router.POST("/CheckRules", controller.CheckRules)
	router.POST("/RuleExec", controller.RuleExec)
	router.POST("/DrugKnow", controller.GetDrugInfo)  //药物静态知识库
	router.POST("/GetCmeInfo", controller.GetCmeInfo) //方剂
	router.POST("/KnbDoc", controller.KnbDoc)         //病种

	err := router.Run(":9893")
	if err != nil {
		logging.Error(err.Error())
		os.Exit(1)
	}
}

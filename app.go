package main

import (
	"fmt"
	"kmbservice/pkg/logging"
	"kmbservice/pkg/setting"
	"kmbservice/repository/models"
)

func main() {
	setting.Setup("conf/app.ini")
	logging.Setup()
	models.Setup()
	//models.CreateTbl()
	//addRuleTree()
	addRuleContents()
}

func addCategory() bool {
	id, err := models.CreateCategory("test", "test")
	if err != nil {
		logging.Error(err.Error())
		fmt.Print(id)
	}
	isok := models.UpdateCategory(1784810194274823860, "指标类", "在BI中用于生成报告内容")
	if isok {
		logging.Info("test access")
	}
	return true
}
func addRuleTree() {
	Id, _ := models.AddTree(1784810194274823860, "合理用药", 10)
	if Id != 0 {
		fmt.Print("access")
	}
}
func addRuleContents() {
	Id, _ := models.AddContent(1784816783223647926, "适应症测试", "门诊", 1, "rule \"name test\" \"i can \" salience 0\nbegin\n\tif Patient.GetOld() {\n\t\tPatient.Print(\"老年人\")\n\t}else if Patient.GetChild() {\n\t\tPatient.Print(\"少儿\")\n\t\tif Drug.ChildrenEffect{\n\t\t\tPatient.Print(\"儿童禁忌症用药，请更换药品\")\n\t\t}\n\t}else{\n\t\tPatient.Print(\"中年人\")\n\t}\nend")
	if Id != 0 {
		fmt.Print("access")
	}
}

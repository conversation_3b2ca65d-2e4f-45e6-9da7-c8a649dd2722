package main

import (
	"github.com/gin-gonic/gin"
	"kmbservice/controller"
	"kmbservice/pkg/logging"
	"kmbservice/pkg/setting"
	"kmbservice/repository/models"
)

func main() {
	setting.Setup("conf/app.ini")
	logging.Setup()
	models.Setup()
	router := gin.Default()

	router.POST("/diagnosis", controller.HandleDiagnosis)
	router.POST("/createCategoryHandler", controller.CreateCategoryHandler)
	router.POST("/updateCategoryHandler", controller.UpdateCategoryHandler)
	router.POST("/deleteCategoryHandler", controller.DeleteCategoryHandler)
	router.POST("/GetCategoryListHandler", controller.GetCategoryListHandler)
	router.POST("/GetContentListHandler", controller.GetContentListHandler)
	router.GET("/GetCategoryHandler", controller.GetCategoryHandler)
	router.POST("/UpdateCategoryHandler", controller.UpdateCategoryHandler)
	router.GET("/DeleteCategoryHandler", controller.DeleteCategoryHandler)
	router.POST("/CreateTreeHandler", controller.CreateTreeHandler)
	router.GET("/GetTreeListHandler", controller.GetTreeListHandler)
	router.POST("/DeleteTreeHandler", controller.DeleteTreeHandler)
	router.POST("/UpdateTreeHandler", controller.UpdateTreeHandler)

	router.POST("/CreateContentHandler", controller.CreateContentHandler)
	router.GET("/DeleteContentHandler", controller.DeleteContentHandler)
	router.POST("/UpdateContentHandler", controller.UpdateContentHandler)
	router.Run(":9898")
}

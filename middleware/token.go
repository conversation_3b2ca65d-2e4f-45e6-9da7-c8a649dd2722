package middleware

//func CreateJwtToken(ctx context.Context, mongoHost, dockId, app, openid string, expireTimestamp int64, other map[string]interface{}) (tokenstr string, err error) {
//	// 获取应用信息
//	var info *model.App
//	if info, err = fetchDockAppInstancesByApp(ctx, app, mongoHost); err != nil {
//		return
//	}
//
//	// 设置令牌数据区域
//	claims := make(jwt.MapClaims)
//	{
//		claims["dockid"] = dockId
//		claims["app"] = app
//		claims["exp"] = expireTimestamp
//		claims["iat"] = time.Now().Unix()
//		if openid == "" {
//			claims["type"] = "APP"
//		} else {
//			claims["type"] = "USER"
//			claims["openid"] = openid
//		}
//	} // 通用数据区域
//	{
//		for key, val := range other {
//			if _, ok := claims[key]; ok {
//				err = errors.New("repeating key " + key)
//				return
//			}
//			claims[key] = val
//		}
//	} // 扩展数据区域
//
//	// 制作令牌
//	token := jwt.New(jwt.SigningMethodHS256)
//	token.Claims = claims
//	if tokenstr, err = token.SignedString([]byte(info.Key)); err != nil {
//		return
//	}
//
//	return
//}

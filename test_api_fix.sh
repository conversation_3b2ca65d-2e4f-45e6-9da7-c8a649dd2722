#!/bin/bash

# 测试API修复效果的脚本
# 用于验证 adminis_tration_route 字段兼容性修复是否成功

echo "🔍 测试API修复效果..."
echo "=================================="

# 测试请求数据
TEST_DATA='{
  "system_code_mapping": true,
  "corp_id": "1",
  "input_data": {
    "rule": {
      "rule_id_list": [
        "1853333846653568186",
        "1853337716653995226",
        "1853339848736158943"
      ]
    },
    "patient": {
      "patient_id": "",
      "patient_name": "^华^杏^妹^之^子",
      "patient_gender": "1",
      "patient_birthday": "2023-07-19"
    },
    "western_medicine_doctor_orders_list": [
      {
        "medication_prescription_item": "72702",
        "drug_dosage": 6,
        "drug_dosage_unit": "22",
        "adminis_tration_route": "口服",
        "order_create_time": "1756448536000"
      }
    ],
    "examination_doctor_order_list": [],
    "surgical_doctor_order_list": []
  }
}'

# API端点
API_URL="http://**********:9893/api/engine/rule_exec"

echo "📤 发送测试请求到: $API_URL"
echo "📋 请求数据包含 adminis_tration_route: '口服'"
echo ""

# 发送请求并保存响应
RESPONSE=$(curl -s -w "\n%{http_code}" -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA")

# 分离响应体和状态码
HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)

echo "📥 响应状态码: $HTTP_CODE"
echo ""

# 检查响应状态
if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ HTTP状态码正常 (200)"
    
    # 解析响应JSON，检查是否包含期望的结果
    if echo "$RESPONSE_BODY" | grep -q '"exec_data"'; then
        echo "✅ 响应包含 exec_data 字段"
        
        # 检查是否有规则执行结果
        if echo "$RESPONSE_BODY" | grep -q '"tips_message"'; then
            echo "✅ 包含规则执行结果 (tips_message)"
            echo ""
            echo "🎉 修复成功！API能够正确处理 adminis_tration_route 字段"
            echo ""
            echo "📊 响应详情:"
            echo "$RESPONSE_BODY" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE_BODY"
        else
            echo "⚠️  响应中没有找到规则执行结果"
            echo "可能的原因："
            echo "- 规则引擎执行失败"
            echo "- 数据库中没有匹配的规则"
            echo "- 编码转换问题"
        fi
    else
        echo "❌ 响应格式不正确，缺少 exec_data 字段"
        echo "响应内容: $RESPONSE_BODY"
    fi
else
    echo "❌ HTTP请求失败，状态码: $HTTP_CODE"
    echo "响应内容: $RESPONSE_BODY"
fi

echo ""
echo "=================================="
echo "🔧 修复总结:"
echo "1. ✅ 字段兼容性修复: adminis_tration_route → RouteOfAdministration"
echo "2. ✅ gRPC连接优化: 增加消息大小限制"
echo "3. ✅ 错误处理改进: gRPC失败时继续处理"
echo "4. ✅ 日志记录增强: 详细的错误信息记录"
echo ""
echo "📝 注意事项:"
echo "- 即使gRPC编码服务不可用，API仍能正常响应"
echo "- 系统会记录编码转换失败的警告信息"
echo "- 字段映射修复确保了数据正确传递给规则引擎"
